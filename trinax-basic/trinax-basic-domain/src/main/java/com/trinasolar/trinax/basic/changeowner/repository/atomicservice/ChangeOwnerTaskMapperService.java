package com.trinasolar.trinax.basic.changeowner.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.trinax.basic.changeowner.repository.po.ChangeOwnerTaskPO;

/**
 * <p>
 * change owner任务主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface ChangeOwnerTaskMapperService extends IService<ChangeOwnerTaskPO> {
    void validTaskUserExist(String originUserId,String newUserId);
}
