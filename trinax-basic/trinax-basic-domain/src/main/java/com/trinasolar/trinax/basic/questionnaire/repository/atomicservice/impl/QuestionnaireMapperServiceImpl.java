package com.trinasolar.trinax.basic.questionnaire.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.basic.questionnaire.repository.atomicservice.QuestionnaireMapperService;
import com.trinasolar.trinax.basic.questionnaire.repository.mapper.QuestionnaireMapper;
import com.trinasolar.trinax.basic.questionnaire.repository.po.QuestionnairePO;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 问卷调查 服务实现类
 * </p>
 *
 * <AUTHOR> <PERSON>
 * @since 2024-02-05
 */
@Service
public class QuestionnaireMapperServiceImpl extends ServiceImpl<QuestionnaireMapper, QuestionnairePO> implements QuestionnaireMapperService {

    @Override
    public QuestionnairePO findByQuestionnaireId(String questionnaireId) {
        return getOne(new LambdaQueryWrapper<QuestionnairePO>().eq(QuestionnairePO::getQuestionnaireId, questionnaireId));
    }

    @Override
    public void removeByQuestionnaireId(String questionnaireId) {
        remove(new LambdaQueryWrapper<QuestionnairePO>().eq(QuestionnairePO::getQuestionnaireId, questionnaireId));
    }
}
