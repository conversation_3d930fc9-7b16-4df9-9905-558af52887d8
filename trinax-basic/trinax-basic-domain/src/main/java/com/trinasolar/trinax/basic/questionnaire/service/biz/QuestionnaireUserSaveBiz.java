package com.trinasolar.trinax.basic.questionnaire.service.biz;

import cn.hutool.core.util.ObjectUtil;
import com.trinasolar.trinax.basic.questionnaire.repository.atomicservice.QuestionnaireUserMapperService;
import com.trinasolar.trinax.basic.questionnaire.repository.po.QuestionnaireUserPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
public class QuestionnaireUserSaveBiz {

    private final QuestionnaireUserMapperService questionnaireUserMapperService;


    public void saveBatch(List<String> userIds, String loginUserId, String loginUserName, String questionnaireId) {
        LocalDateTime now = LocalDateTime.now();
        if (ObjectUtil.isNotEmpty(userIds)) {
            List<QuestionnaireUserPO> questionnaireUserPOS = userIds.stream().map(userId -> {
                QuestionnaireUserPO questionnaireUserPO = new QuestionnaireUserPO();
                questionnaireUserPO.setQuestionnaireId(questionnaireId);
                questionnaireUserPO.setUserId(userId);
                questionnaireUserPO.setCreatedBy(loginUserId);
                questionnaireUserPO.setCreatedName(loginUserName);
                questionnaireUserPO.setCreatedTime(now);
                questionnaireUserPO.setUpdatedBy(loginUserId);
                questionnaireUserPO.setUpdatedName(loginUserName);
                questionnaireUserPO.setUpdatedTime(now);
                return questionnaireUserPO;
            }).collect(Collectors.toList());
            questionnaireUserMapperService.saveBatch(questionnaireUserPOS);
        }
    }
}
