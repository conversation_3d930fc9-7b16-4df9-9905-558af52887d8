package com.trinasolar.trinax.basic.agreement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.basic.agreement.repository.atomicservice.ContentInfoMapperService;
import com.trinasolar.trinax.basic.agreement.repository.mapper.ContentFileRelationMapper;
import com.trinasolar.trinax.basic.agreement.repository.mapper.ContentRecordMapper;
import com.trinasolar.trinax.basic.agreement.repository.po.ContentInfoPO;
import com.trinasolar.trinax.basic.agreement.repository.po.ContentRecordPO;
import com.trinasolar.trinax.basic.agreement.service.ContentRecordService;
import com.trinasolar.trinax.basic.agreement.service.ContentService;
import com.trinasolar.trinax.basic.constants.AgreementResultCode;
import com.trinasolar.trinax.basic.constants.enums.AgreementTypeEnum;
import com.trinasolar.trinax.basic.constants.enums.content.ContentEffectEnum;
import com.trinasolar.trinax.basic.dto.output.ContentUserDto;
import com.trinasolar.trinax.basic.dto.output.content.AgreementAdminPageRespDTO;
import com.trinasolar.trinax.basic.dto.output.content.ContentFileInfo;
import com.trinasolar.trinax.basic.dto.output.content.NotificationPopupReqDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.SysOrganizationTypeEnum;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Service
public class ContentRecordServiceImpl extends ServiceImpl<ContentRecordMapper, ContentRecordPO> implements ContentRecordService {

    @Autowired
    private ContentRecordMapper contentRecordMapper;
    @Autowired
    private ContentFileRelationMapper contentFileRelationMapper;
    @Autowired
    private ContentService contentService;

    @Autowired
    private SysUserFeign sysUserFeign;

    @Override
    public Result<List<AgreementAdminPageRespDTO>> popNotification(NotificationPopupReqDTO reqDTO) {
        List<ContentUserDto> contentIdList = contentRecordMapper.getContentIds(reqDTO.getUserId());

        ArrayList<Integer> arr = new ArrayList<>();
        for (int i = 0; i <contentIdList.size() ; i++) {
            ContentUserDto contentUserDto = contentIdList.get(i);
            arr.add(contentUserDto.getContentId());
        }

        List<AgreementAdminPageRespDTO> records = contentRecordMapper.popNotification(arr);
        if(ObjectUtils.isEmpty(records)){
            return Result.ok();
        }
        Result<SysUserRespDTO> userData = sysUserFeign.getUserByUserId(reqDTO.getUserIdStr());
        SysUserRespDTO userInfo = userData.getData();
        if (SysOrganizationTypeEnum.SALES.getCode().equals(userInfo.getOrganizationType())) {
            // 销售的话需要额外筛选一下区域
            records = records.stream().filter(rec -> rec.getAreaScope() != null).filter(rec -> rec.getAreaScope().contains(userInfo.getOrganizationCode())).collect(Collectors.toList());
        } else if (userInfo.externalUser() && userInfo.getOrganizationCode() != null) {
            // 外部用户筛选
            records = records.stream().filter(rec -> rec.getAreaScope() != null).filter(rec -> rec.getAreaScope().contains(userInfo.getOrganizationCode())).collect(Collectors.toList());
        }
        List<AgreementAdminPageRespDTO> agreementAdminPageRespDtos = BeanUtil.copyToList(records, AgreementAdminPageRespDTO.class);
        List<String> contentIds = agreementAdminPageRespDtos.stream().map(AgreementAdminPageRespDTO::getContentId).collect(Collectors.toList());

        List<ContentFileInfo> contentFileInfos = contentFileRelationMapper.selFileInfoByFileId(contentIds);

        if (ObjectUtils.isEmpty(contentFileInfos)) {
            contentFileInfos = new ArrayList<>();
        }

        Map<String, List<ContentFileInfo>> map = contentFileInfos.stream().collect(Collectors.groupingBy(ContentFileInfo::getContentId));
        agreementAdminPageRespDtos.forEach(agreement -> {
                    agreement.setTypeText(AgreementTypeEnum.getDescByCode(agreement.getType()));
                    agreement.setIsEffectName(ContentEffectEnum.finContentEffDesc(agreement.getIsEffect()));
                    if (ObjectUtils.isEmpty(map.get(agreement.getContentId()))) {
                        agreement.setAnnouncementType(0);
                    } else {
                        agreement.setAnnouncementType(1);
                        agreement.setFileInfos(map.get(agreement.getContentId()));
                    }
                }
        );
        return Result.ok(agreementAdminPageRespDtos);
    }

    @Override
    public Result<AgreementAdminPageRespDTO> readAgreement(Long contentId, Long userId) {
        List<ContentRecordPO> list = this.lambdaQuery().eq(ContentRecordPO::getContentId, contentId).
                eq(ContentRecordPO::getUserId, userId).list();
        if(ObjectUtils.isNotEmpty(list)){
            throw new BizException(AgreementResultCode.AGREEMENT_IS_EXIST.getCode(),AgreementResultCode.AGREEMENT_IS_EXIST.getMessage());
        }

        ContentInfoPO contentInfoPO= contentService.getById(contentId);
        Assert.notNull(contentInfoPO,"内容不存在");

        ContentRecordPO contentRecordPO = new ContentRecordPO();
        contentRecordPO.setContentId(contentId);
        contentRecordPO.setUserId(userId);
        contentRecordPO.setIsRead(1);
        contentRecordPO.setCreatedTime(LocalDateTime.now());
        contentRecordPO.setUpdatedTime(LocalDateTime.now());
        contentRecordPO.setType(contentInfoPO.getType());
        this.save(contentRecordPO);

        ContentInfoPO byId = contentService.getById(contentId);
        AgreementAdminPageRespDTO adminPageRespDTO = new AgreementAdminPageRespDTO();
        BeanUtils.copyProperties(byId,adminPageRespDTO);
        adminPageRespDTO.setIsRead(1);

        return Result.ok(adminPageRespDTO);
    }


}
