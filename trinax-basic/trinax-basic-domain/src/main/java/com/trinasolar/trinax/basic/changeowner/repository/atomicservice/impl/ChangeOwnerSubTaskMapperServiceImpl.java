package com.trinasolar.trinax.basic.changeowner.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.basic.changeowner.repository.atomicservice.ChangeOwnerSubTaskMapperService;
import com.trinasolar.trinax.basic.changeowner.repository.mapper.ChangeOwnerSubTaskMapper;
import com.trinasolar.trinax.basic.changeowner.repository.po.ChangeOwnerSubTaskPO;
import org.springframework.stereotype.Service;

/**
 * <p>
 * change owner任务子表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
public class ChangeOwnerSubTaskMapperServiceImpl extends ServiceImpl<ChangeOwnerSubTaskMapper, ChangeOwnerSubTaskPO> implements ChangeOwnerSubTaskMapperService {

}
