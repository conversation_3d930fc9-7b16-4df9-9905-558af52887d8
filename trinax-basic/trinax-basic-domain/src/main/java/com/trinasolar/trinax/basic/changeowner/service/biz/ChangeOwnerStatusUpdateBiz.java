package com.trinasolar.trinax.basic.changeowner.service.biz;

import com.trinasolar.trinax.basic.changeowner.repository.atomicservice.ChangeOwnerSubTaskMapperService;
import com.trinasolar.trinax.basic.changeowner.repository.atomicservice.ChangeOwnerTaskMapperService;
import com.trinasolar.trinax.basic.changeowner.repository.po.ChangeOwnerSubTaskPO;
import com.trinasolar.trinax.basic.changeowner.repository.po.ChangeOwnerTaskPO;
import com.trinasolar.trinax.basic.constants.enums.changeowner.ChangeTaskStatusEnum;
import com.trinasolar.trinax.basic.dto.input.changeOwner.*;
import com.trinasolar.trinax.common.dto.output.Result;
import dtt.cache.redisclient.RedisUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;

@RequiredArgsConstructor
@Component
public class ChangeOwnerStatusUpdateBiz {

    private final ChangeOwnerTaskMapperService changeOwnerTaskMapperService;
    private final ChangeOwnerSubTaskMapperService changeOwnerSubTaskMapperService;
    private final TransactionTemplate transactionTemplate;
    private final RedisUtil redisUtil;

    /**
     * 更新子任务状态 & 同步更新主任务状态
     * 任务状态只会是：成功、失败
     */
    public Result<String> updateTaskStatus(ChangeOwnerStatusUpdateReqDTO req) {

        //获取子任务
        ChangeOwnerSubTaskPO subTaskPO = getSubTask(req);
        int retryTimes;
        if(ChangeTaskStatusEnum.FAIL.getCode().equals(subTaskPO.getSubTaskStatus())){
            retryTimes = subTaskPO.getRetryTimes() + 1;
        } else {
            retryTimes = subTaskPO.getRetryTimes();
        }
        //完成子任务时,如果子任务是成功状态，需要校验是否所有子任务都已完成，当所有子任务完成时，需要将主任务状态修改为成功
        if(ChangeTaskStatusEnum.SUCCESS.getCode().equals(req.getSubTaskStatus())){
            return transactionTemplate.execute(ts -> {
                try {
                    //修改当前子任务状态
                    updateSubTask(req,retryTimes);
                    //是否存在非成功状态的子任务
                    int unCompleteNum = changeOwnerSubTaskMapperService.lambdaQuery()
                            .eq(ChangeOwnerSubTaskPO::getTaskNo,req.getTaskNo())
                            .ne(ChangeOwnerSubTaskPO::getSubTaskStatus,ChangeTaskStatusEnum.SUCCESS.getCode())
                            .list()
                            .size();
                    //所有子任务都已完成时，将主任务完成
                    if(unCompleteNum == 0){
                        updateTask(req);
                    }
                    return Result.ok();
                } catch (Exception e) {
                    ts.setRollbackOnly();
                    return Result.fail(e.toString());
                }
            });
            //当当前子任务失败时，主任务失败
        }else if(ChangeTaskStatusEnum.FAIL.getCode().equals(req.getSubTaskStatus())){
            return transactionTemplate.execute(ts -> {
                try {
                    updateSubTask(req,retryTimes);
                    updateTask(req);
                    return Result.ok();
                } catch (Exception e) {
                    ts.setRollbackOnly();
                    return Result.fail(e.toString());
                }
            });
        }else{
            return Result.fail("消息状态不正确");
        }


    }

    /**
     * 修改主任务状态
     * @param req
     */
    private void updateTask(ChangeOwnerStatusUpdateReqDTO req){
        changeOwnerTaskMapperService.lambdaUpdate()
                .eq(ChangeOwnerTaskPO::getTaskNo,req.getTaskNo())
                .set(ChangeOwnerTaskPO::getTaskStatus,req.getSubTaskStatus())
                .set(StringUtils.isNotEmpty(req.getUpdateUserId()),ChangeOwnerTaskPO::getUpdatedBy,req.getUpdateUserId())
                .set(StringUtils.isNotEmpty(req.getUpdateUserName()),ChangeOwnerTaskPO::getUpdatedName,req.getUpdateUserName())
                .set(ChangeOwnerTaskPO::getUpdatedTime,LocalDateTime.now())
                .update();
    }

    private ChangeOwnerSubTaskPO getSubTask(ChangeOwnerStatusUpdateReqDTO req){
        return changeOwnerSubTaskMapperService.lambdaQuery()
                .eq(ChangeOwnerSubTaskPO::getTaskNo,req.getTaskNo())
                .eq(ChangeOwnerSubTaskPO::getSubTaskNo,req.getSubTaskNo())
                .one();

    }
    /**
     * 修改子任务状态
     * @param req
     */
    private void updateSubTask(ChangeOwnerStatusUpdateReqDTO req,int retryTimes){
        changeOwnerSubTaskMapperService.lambdaUpdate()
                .eq(ChangeOwnerSubTaskPO::getTaskNo,req.getTaskNo())
                .eq(ChangeOwnerSubTaskPO::getSubTaskNo,req.getSubTaskNo())
                .set(ChangeOwnerSubTaskPO::getSubTaskStatus,req.getSubTaskStatus())
                .set(StringUtils.isNotEmpty(req.getUpdateUserId()),ChangeOwnerSubTaskPO::getUpdatedBy,req.getUpdateUserId())
                .set(StringUtils.isNotEmpty(req.getUpdateUserName()),ChangeOwnerSubTaskPO::getUpdatedName,req.getUpdateUserName())
                .set(ChangeOwnerSubTaskPO::getUpdatedTime,LocalDateTime.now())
                .set(ChangeOwnerSubTaskPO::getChangeAmount,req.getChangeAmount())
                .set(StringUtils.isNotEmpty(req.getErrMessage()),ChangeOwnerSubTaskPO::getErrMessage,req.getErrMessage())
                .set(ChangeOwnerSubTaskPO::getRetryTimes,retryTimes)
                .update();
    }




}
