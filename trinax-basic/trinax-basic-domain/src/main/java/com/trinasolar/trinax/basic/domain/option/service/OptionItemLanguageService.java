package com.trinasolar.trinax.basic.domain.option.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.basic.constants.enums.OptionEnableEnum;
import com.trinasolar.trinax.basic.constants.enums.OptionItemLanguageEnum;
import com.trinasolar.trinax.basic.domain.option.repository.atomicservice.OptionGroupMapperService;
import com.trinasolar.trinax.basic.domain.option.repository.atomicservice.OptionItemLanguageMapperService;
import com.trinasolar.trinax.basic.domain.option.repository.atomicservice.OptionItemMapperService;
import com.trinasolar.trinax.basic.domain.option.repository.mapper.OptionItemLanguageMapper;
import com.trinasolar.trinax.basic.domain.option.repository.po.OptionGroupPO;
import com.trinasolar.trinax.basic.domain.option.repository.po.OptionItemLanguagePO;
import com.trinasolar.trinax.basic.domain.option.repository.po.OptionItemPO;
import com.trinasolar.trinax.basic.dto.input.option.OptionItemLanguageEnableReqDTO;
import com.trinasolar.trinax.basic.dto.input.option.OptionItemLanguagePageReqDTO;
import com.trinasolar.trinax.basic.dto.input.option.OptionItemLanguageSaveReqDTO;
import com.trinasolar.trinax.basic.dto.input.option.OptionItemLanguageUpdateReqDTO;
import com.trinasolar.trinax.basic.dto.output.option.OptionItemLanguageExcelDTO;
import com.trinasolar.trinax.basic.dto.output.option.OptionItemLanguagePageResDTO;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OptionItemLanguageService {


    @Autowired
    private OptionItemLanguageMapper optionItemLanguageMapper;

    @Autowired
    private OptionItemLanguageMapperService optionItemLanguageMapperService;

    @Autowired
    private OptionItemMapperService optionItemMapperService;

    @Autowired
    private OptionGroupMapperService optionGroupMapperService;

    /**
     * 字典组项目分页查询
     *
     * @param reqDTO
     * @return
     */
    public Result<PageResponse<OptionItemLanguagePageResDTO>> findOptionItemLanguagePage(PageRequest<OptionItemLanguagePageReqDTO> reqDTO) {
        //查询字典项语言表
        LambdaQueryWrapper<OptionItemLanguagePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OptionItemLanguagePO::getOptionGroup, reqDTO.getQuery().getOptionGroup())
                .eq(StringUtils.isNotBlank(reqDTO.getQuery().getLanguage()), OptionItemLanguagePO::getLanguage, reqDTO.getQuery().getLanguage())
                .eq(StringUtils.isNotBlank(reqDTO.getQuery().getOptionValue()), OptionItemLanguagePO::getOptionValue, reqDTO.getQuery().getOptionValue())
                .orderByDesc(OptionItemLanguagePO::getOptionValue);
        Page<OptionItemLanguagePO> page = new Page<>(reqDTO.getIndex(), reqDTO.getSize());
        IPage<OptionItemLanguagePO> groupPage = optionItemLanguageMapper.selectPage(page, queryWrapper);
        List<OptionItemLanguagePO> pageList = groupPage.getRecords();
        List<OptionItemLanguagePageResDTO> resultList = BeanUtil.copyToList(pageList, OptionItemLanguagePageResDTO.class);

        if (CollUtil.isNotEmpty(resultList)) {
            List<String> optionValueList = resultList.stream().map(OptionItemLanguagePageResDTO::getOptionValue).distinct().collect(Collectors.toList());
            List<OptionItemPO> optionItemPOList = optionItemMapperService.findByOptionItemList(optionValueList);
            Map<String, List<OptionItemPO>> optionItemMap = optionItemPOList.stream().collect(Collectors.groupingBy(item -> item.getOptionGroup() + item.getOptionValue()));
            resultList.forEach(e -> {
                e.setLanguageText(OptionItemLanguageEnum.getDescByCode(e.getLanguage()));
                if (CollUtil.isNotEmpty(optionItemMap.get(e.getOptionGroup() + e.getOptionValue()))) {
                    e.setOptionDesc(optionItemMap.get(e.getOptionGroup() + e.getOptionValue()).get(0).getOptionDesc());
                }
            });
        }

        PageResponse<OptionItemLanguagePageResDTO> result = PageResponse.toResult(
                reqDTO.getIndex(),
                reqDTO.getSize(),
                (int) groupPage.getTotal(),
                resultList);

        return Result.ok(result);
    }


    /**
     * 字典组项条件查询
     *
     * @param reqDTO
     * @return
     */
    public Result<List<OptionItemLanguageExcelDTO>> findOptionItemLanguageList(OptionItemLanguagePageReqDTO reqDTO) {
        List<OptionItemLanguagePO> itemLanguagePOList = optionItemLanguageMapperService.lambdaQuery().eq(OptionItemLanguagePO::getOptionGroup, reqDTO.getOptionGroup())
                .eq(StringUtils.isNotBlank(reqDTO.getLanguage()), OptionItemLanguagePO::getLanguage, reqDTO.getLanguage())
                .like(StringUtils.isNotBlank(reqDTO.getOptionValue()), OptionItemLanguagePO::getOptionValue, reqDTO.getOptionValue())
                .orderByDesc(OptionItemLanguagePO::getOptionValue)
                .list();

        List<OptionItemLanguageExcelDTO> resultList = BeanUtil.copyToList(itemLanguagePOList, OptionItemLanguageExcelDTO.class);
        if (CollUtil.isNotEmpty(resultList)) {
            //获取字典项信息
            List<String> optionValueList = resultList.stream().map(OptionItemLanguageExcelDTO::getOptionValue).distinct().collect(Collectors.toList());
            List<OptionItemPO> optionItemPOList = optionItemMapperService.findByOptionItemList(optionValueList);
            Map<String, List<OptionItemPO>> optionItemMap = optionItemPOList.stream().collect(Collectors.groupingBy(item -> item.getOptionGroup() + item.getOptionValue()));

            //获取字典组信息
            List<String> optionGroupList = resultList.stream().map(OptionItemLanguageExcelDTO::getOptionGroup).distinct().collect(Collectors.toList());
            List<OptionGroupPO> optionGroupPOList = optionGroupMapperService.findByOptionGroupList(optionGroupList);
            Map<String, List<OptionGroupPO>> optionGroupMap = optionGroupPOList.stream().collect(Collectors.groupingBy(OptionGroupPO::getOptionGroup));

            resultList.forEach(e -> {
                e.setLanguage(OptionItemLanguageEnum.getDescByCode(e.getLanguage()));
                e.setIsDeletedText(OptionEnableEnum.getDescByCode(e.getIsDeleted()));
                if (CollUtil.isNotEmpty(optionItemMap.get(e.getOptionGroup() + e.getOptionValue()))) {
                    e.setOptionDesc(optionItemMap.get(e.getOptionGroup() + e.getOptionValue()).get(0).getOptionDesc());
                }
                if (CollUtil.isNotEmpty(optionGroupMap.get(e.getOptionGroup()))) {
                    e.setOptionGroupName(optionGroupMap.get(e.getOptionGroup()).get(0).getOptionGroupName());
                }
            });
        }
        return Result.ok(resultList);
    }

    /**
     * 字典组项停用或启用
     *
     * @param reqDTO
     * @return
     */
    public Result<String> enableOrDisable(OptionItemLanguageEnableReqDTO reqDTO) {
        boolean isUpdated = optionItemLanguageMapperService.lambdaUpdate()
                .eq(OptionItemLanguagePO::getOptionGroup, reqDTO.getOptionGroup())
                .eq(OptionItemLanguagePO::getLanguage, reqDTO.getLanguage())
                .eq(OptionItemLanguagePO::getOptionValue, reqDTO.getOptionValue())
                .eq(OptionItemLanguagePO::getOptionName, reqDTO.getOptionName())
                .set(OptionItemLanguagePO::getIsDeleted, reqDTO.getIsDelete())
                .set(OptionItemLanguagePO::getUpdatedBy, reqDTO.getCurrentUserId())
                .set(OptionItemLanguagePO::getUpdatedName, reqDTO.getCurrentUserName())
                .set(OptionItemLanguagePO::getUpdatedTime, LocalDateTime.now())
                .update();
        return isUpdated ? Result.ok() : Result.fail("操作失败");
    }

    /**
     * 新增字典语言项
     *
     * @param reqDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<String> optionItemLanguageSave(OptionItemLanguageSaveReqDTO reqDTO) {
        //校验字典组
        verifyOptionGroup(reqDTO.getOptionGroup());
        //校验字典项
        List<OptionItemPO> itemList = optionItemMapperService.findOptionItem(reqDTO.getOptionGroup(), reqDTO.getOptionValue());
        if (CollUtil.isEmpty(itemList)) {
            throw new BizException(ResultCode.FAIL.getCode(), "字典项信息异常");
        }
        //新增保存字典语言项
        boolean isSaved = saveItemLanguage(reqDTO);
        optionGroupMapperService.updateModifyUser(reqDTO.getOptionGroup(),reqDTO.getCurrentUserId(),reqDTO.getCurrentUserName());
        return isSaved ? Result.ok("添加成功") : Result.fail("添加失败");
    }


    /**
     * 编辑字典语言项
     *
     * @param reqDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<String> optionItemLanguageEdit(OptionItemLanguageUpdateReqDTO reqDTO) {
        //校验字典组
        verifyOptionGroup(reqDTO.getOptionGroup());
        //校验字典项
        List<OptionItemPO> itemList = optionItemMapperService.findOptionItem(reqDTO.getOptionGroup(), reqDTO.getOptionValue());
        if (CollUtil.isEmpty(itemList) || itemList.size() != 1) {
            throw new BizException(ResultCode.FAIL.getCode(), "字典项信息异常");
        }
        //更新字典语言项
        boolean isUpdated = updateItemLanguage(reqDTO);

        optionGroupMapperService.updateModifyUser(reqDTO.getOptionGroup(),reqDTO.getCurrentUserId(),reqDTO.getCurrentUserName());
        return isUpdated ? Result.ok() : Result.fail("操作失败");
    }

    /**
     * 校验字典组信息
     *
     * @param optionGroup
     */
    private void verifyOptionGroup(String optionGroup) {
        List<OptionGroupPO> groupList = optionGroupMapperService.lambdaQuery().eq(OptionGroupPO::getOptionGroup, optionGroup).list();
        if (CollUtil.isEmpty(groupList) || groupList.size() != 1) {
            throw new BizException(ResultCode.FAIL.getCode(), "字典组信息异常");
        }
    }


    /**
     * 保存信息
     *
     * @param reqDTO
     */
    private boolean saveItemLanguage(OptionItemLanguageSaveReqDTO reqDTO) {
        List<OptionItemLanguagePO> languagePOList = new ArrayList<>();
        reqDTO.getItemList().forEach(e -> {
            OptionItemLanguagePO languagePO = new OptionItemLanguagePO();
            languagePO.setLanguage(e.getLanguage());
            languagePO.setOptionGroup(reqDTO.getOptionGroup());
            languagePO.setOptionValue(reqDTO.getOptionValue());
            languagePO.setOptionName(e.getOptionName());
            languagePO.setCreatedBy(reqDTO.getCurrentUserId());
            languagePO.setCreatedName(reqDTO.getCurrentUserName());
            languagePO.setCreatedTime(LocalDateTime.now());
            languagePO.setUpdatedBy(reqDTO.getCurrentUserId());
            languagePO.setUpdatedName(reqDTO.getCurrentUserName());
            languagePO.setUpdatedTime(LocalDateTime.now());
            languagePO.setIsDeleted(0);
            languagePOList.add(languagePO);
        });
        return optionItemLanguageMapperService.saveBatch(languagePOList);
    }


    /**
     * 修改语言
     *
     * @param reqDTO 修改入参
     */
    private boolean updateItemLanguage(OptionItemLanguageUpdateReqDTO reqDTO) {
        return optionItemLanguageMapperService.lambdaUpdate()
                .eq(OptionItemLanguagePO::getOptionGroup, reqDTO.getOptionGroup())
                .eq(OptionItemLanguagePO::getOptionValue, reqDTO.getOptionValue())
                .eq(OptionItemLanguagePO::getLanguage, reqDTO.getLanguage())
                .set(OptionItemLanguagePO::getOptionName, reqDTO.getOptionName())
                .set(OptionItemLanguagePO::getUpdatedBy, reqDTO.getCurrentUserId())
                .set(OptionItemLanguagePO::getUpdatedName, reqDTO.getCurrentUserName())
                .set(OptionItemLanguagePO::getUpdatedTime, LocalDateTime.now())
                .update();
    }

}
