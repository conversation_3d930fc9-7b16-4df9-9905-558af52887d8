package com.trinasolar.trinax.basic.agreement.service.biz;

import com.trinasolar.trinax.basic.constants.enums.content.ContentEffectEnum;
import com.trinasolar.trinax.basic.dto.input.content.ContentEnumReqDTO;
import com.trinasolar.trinax.basic.dto.output.content.ContentEnumResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ContentEnumBiz {
    private static Map<String, List<ContentEnumResDTO>> enumMap;

    public Map<String, List<ContentEnumResDTO>> findContentEnum(ContentEnumReqDTO req) {
        if (ObjectUtils.isEmpty(enumMap)) {
            contentEnumInit();
        }
        String groupId = req.getTypeColum();
        if (StringUtils.isBlank(groupId)) {
            return enumMap;
        } else {
            Map<String, List<ContentEnumResDTO>> map = new HashMap<>();
            map.put(groupId, enumMap.get(groupId));
            return map;
        }

    }



    private void contentEnumInit() {
        enumMap = new HashMap<>();
        Map<String, List<ContentEffectEnum>> bannerImageMap = ContentEffectEnum.getContentMapList();
        bannerImageMap.forEach((key, value) -> {
            List<ContentEnumResDTO> contenEnumResList = new ArrayList<>();
            value.forEach(a -> {
                ContentEnumResDTO contenEnumRes = new ContentEnumResDTO();
                contenEnumRes.setKey(String.valueOf(a.getCode()));
                contenEnumRes.setKeyName(a.getDesc());
                contenEnumResList.add(contenEnumRes);
            });
            enumMap.put(key, contenEnumResList);
        });
    }
}
