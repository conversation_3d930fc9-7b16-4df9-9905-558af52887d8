package com.trinasolar.trinax.basic.domain.messageInmail.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.basic.domain.messageInmail.repository.atomicservice.MessageInmailMapperService;
import com.trinasolar.trinax.basic.domain.messageInmail.repository.atomicservice.MessageInmailUserMapperService;
import com.trinasolar.trinax.basic.domain.messageInmail.repository.mapper.MessageInmailMapper;
import com.trinasolar.trinax.basic.domain.messageInmail.repository.po.MessageInmailPO;
import com.trinasolar.trinax.basic.domain.messageInmail.repository.po.MessageInmailUserPO;
import com.trinasolar.trinax.basic.domain.messageInmail.service.manager.MessageBizManager;
import com.trinasolar.trinax.basic.dto.messageInmail.MessageInmailAddReqDTO;
import com.trinasolar.trinax.basic.dto.messageInmail.MessageInmailQueryReqDTO;
import com.trinasolar.trinax.basic.dto.messageInmail.MessageInmailQueryResDTO;
import com.trinasolar.trinax.basic.dto.messageInmail.MessageInmailStatusUpdateReqDTO;
import com.trinasolar.trinax.basic.event.MessageStatusEnum;
import com.trinasolar.trinax.basic.event.MessageTypeEnum;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class MessageBiz {

    @Autowired
    private MessageInmailMapperService messageInmailAtomicService;
    @Autowired
    private MessageInmailUserMapperService messageInmailUserMapperService;
    @Autowired
    private MessageInmailMapper messageInmailMapper;
    @Autowired
    MessageBizManager messageBizManager;

    public Result<PageResponse<MessageInmailQueryResDTO>> query(PageRequest<MessageInmailQueryReqDTO> queryDto) {
        MessageInmailQueryReqDTO reqDTO = queryDto.getQuery();
        List<String> idCondition = messageBizManager.idConditionAssemble(reqDTO.getCurrUserId());
        Page<MessageInmailQueryResDTO> page = new Page<>(queryDto.getIndex(), queryDto.getSize());
        IPage<MessageInmailQueryResDTO> orderIPage = messageInmailMapper.getMsgListByPage(page, reqDTO, idCondition);
        PageResponse<MessageInmailQueryResDTO> result = PageResponse.toResult(
                queryDto.getIndex(),
                queryDto.getSize(),
                (int) orderIPage.getTotal(),
                orderIPage.getRecords());
        return Result.ok(result);
    }

    public Result<Boolean> add(MessageInmailAddReqDTO reqDTO) {
        if (MessageTypeEnum.MSG_TYPE_PRIVATE.getValue().equals(reqDTO.getMessageType())
                && StringUtils.isBlank(reqDTO.getReceiveUserId())) {
            return Result.fail("接收人ID不能为空");
        }

        String currUserId = reqDTO.getCreatedBy();
        String currUserName = reqDTO.getCreatedName();

        MessageInmailPO msg = new MessageInmailPO();
        BeanUtils.copyProperties(reqDTO, msg);
        setMsgBaseInfo(msg, currUserId, currUserName);
        messageInmailAtomicService.save(msg);

        if (MessageTypeEnum.MSG_TYPE_PRIVATE.getValue().equals(reqDTO.getMessageType())) {
            MessageInmailUserPO user = new MessageInmailUserPO();
            user.setMessageInmailId(msg.getId());
            user.setReceiveUserId(reqDTO.getReceiveUserId());
            user.setReceiveUserType(reqDTO.getReceiveUserType());
            user.setStatus(MessageStatusEnum.MSG_STATUS_UNREAD.getValue());
            setMsgUserBaseInfo(user, currUserId, currUserName);
            messageInmailUserMapperService.save(user);
        }

        return Result.ok();
    }

    public Result<Boolean> updateStatus(MessageInmailStatusUpdateReqDTO reqDTO) {
        MessageTypeEnum msgType = MessageTypeEnum.get(reqDTO.getMessageType());
        MessageInmailPO msg = messageInmailAtomicService.getById(reqDTO.getMessageId());
        if (msg == null) {
            return Result.fail((msgType != null ? msgType.getDisplayName() : "") + "内容不存在");
        }

        String currUserId = reqDTO.getUpdatedBy();
        String currUserName = reqDTO.getUpdatedName();

        List<String> idCondition = messageBizManager.idConditionAssemble(currUserId);
        //查询未读消息
        LambdaQueryWrapper<MessageInmailUserPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MessageInmailUserPO::getMessageInmailId, reqDTO.getMessageId())
                .in(MessageInmailUserPO::getReceiveUserId, idCondition);
        MessageInmailUserPO user = messageInmailUserMapperService.getOne(wrapper);
        if (user != null && MessageStatusEnum.MSG_STATUS_UNREAD.getValue().equals(user.getStatus())) {
            user.setStatus(MessageStatusEnum.MSG_STATUS_READ.getValue());
            user.setReadTime(LocalDateTime.now());
            user.setUpdatedBy(currUserId);
            user.setUpdatedName(currUserName);
            user.setUpdatedTime(user.getReadTime());
            messageInmailUserMapperService.updateById(user);
        } else if (user == null) {
            if (MessageTypeEnum.MSG_TYPE_PUBLIC.getValue().equals(msg.getMessageType())) {
                user = new MessageInmailUserPO();
                user.setMessageInmailId(msg.getId());
                user.setReceiveUserId(currUserId);
                user.setStatus(MessageStatusEnum.MSG_STATUS_READ.getValue());
                setMsgUserBaseInfo(user, currUserId, currUserName);
                messageInmailUserMapperService.save(user);
            } else {
                return Result.fail((msgType != null ? msgType.getDisplayName() : "") + "内容不存在");
            }
        }
        return Result.ok();
    }

    public Result<Boolean> readAll(MessageInmailStatusUpdateReqDTO reqDTO) {

        String currUserId = reqDTO.getUpdatedBy();
        String currUserName = reqDTO.getUpdatedName();

//        List<String> idCondition = messageBizManager.idConditionAssemble(currUserId);
        //查询未读消息
        LambdaQueryWrapper<MessageInmailUserPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MessageInmailUserPO::getReceiveUserId, currUserId)
                .eq(MessageInmailUserPO::getStatus, MessageStatusEnum.MSG_STATUS_UNREAD.getValue());
        List<MessageInmailUserPO> user = messageInmailUserMapperService.list(wrapper);
        if (CollectionUtils.isNotEmpty(user)) {
            boolean isUpdated = messageInmailUserMapperService.lambdaUpdate()
                    .eq(MessageInmailUserPO::getReceiveUserId, currUserId)
                    .eq(MessageInmailUserPO::getStatus, MessageStatusEnum.MSG_STATUS_UNREAD.getValue())
                    .eq(MessageInmailUserPO::getIsDeleted, 0L)
                    .set(MessageInmailUserPO::getStatus, MessageStatusEnum.MSG_STATUS_READ.getValue())
                    .set(MessageInmailUserPO::getUpdatedBy, currUserId)
                    .set(MessageInmailUserPO::getUpdatedName, currUserName)
                    .set(MessageInmailUserPO::getUpdatedTime, LocalDateTime.now())
                    .update();
        }
        return Result.ok();
    }

    private void setMsgBaseInfo(MessageInmailPO msg, String createBy, String createName) {
        msg.setIsDeleted(0L);
        msg.setCreatedBy(createBy);
        msg.setCreatedName(createName);
        msg.setCreatedTime(LocalDateTime.now());
        msg.setUpdatedBy(msg.getCreatedBy());
        msg.setUpdatedName(msg.getCreatedName());
        msg.setUpdatedTime(msg.getCreatedTime());
    }

    private void setMsgUserBaseInfo(MessageInmailUserPO user, String createBy, String createName) {
        user.setIsDeleted(0L);
        user.setCreatedBy(createBy);
        user.setCreatedName(createName);
        user.setCreatedTime(LocalDateTime.now());
        user.setUpdatedBy(user.getCreatedBy());
        user.setUpdatedName(user.getCreatedName());
        user.setUpdatedTime(user.getCreatedTime());
    }
}
