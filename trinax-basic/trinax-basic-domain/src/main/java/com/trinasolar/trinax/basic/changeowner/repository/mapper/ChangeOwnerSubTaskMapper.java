package com.trinasolar.trinax.basic.changeowner.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.basic.changeowner.repository.po.ChangeOwnerSubTaskPO;
import com.trinasolar.trinax.basic.dto.input.changeOwner.ChangeSubTaskReqDTO;
import com.trinasolar.trinax.basic.dto.output.changeOwner.ChangeSubTaskQryResDTO;
import com.trinasolar.trinax.basic.dto.output.changeOwner.SubsTaskExportResDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * change owner任务子表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Mapper
public interface ChangeOwnerSubTaskMapper extends BaseMapper<ChangeOwnerSubTaskPO> {

    IPage<ChangeSubTaskQryResDTO> qryChangeSubTaskList(Page<ChangeSubTaskQryResDTO> page, @Param("query") ChangeSubTaskReqDTO query);

    List<ChangeSubTaskQryResDTO> qrySubTaskList(@Param("query") ChangeSubTaskReqDTO req);
}
