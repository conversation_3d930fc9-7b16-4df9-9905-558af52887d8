package com.trinasolar.trinax.basic.domain.sms.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 13/09/2023
 * @description
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sms_send_record")
public class SmsSendRecordPO extends BasePO {

    /**
     * 发送平台，Ali-阿里
     */
    private String platform;

    /**
     * 模板id
     */
    private String templateCode;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 短信接收手机号
     */
    private String phone;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 短信内容
     */
    private String messageContent;

    /**
     * 渠道响应内容
     */
    private String responseBody;

    /**
     * 发送状态 1-发送中，2-成功，3-失败
     */
    private String sendStatus;

    /**
     * 手机接收状态 1-成功，2-失败
     */
    private String receiveStatus;

    /**
     * 手机接收失败原因
     */
    private String receiveFailReason;

    /**
     * 手机接收时间
     */
    private LocalDateTime receiveTime;

    /**
     * 接收者(手机对应人的名称)
     */
    private String receiver;
}

