package com.trinasolar.trinax.basic.domain.messageInmail.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.basic.domain.messageInmail.repository.po.MessageInmailPO;
import com.trinasolar.trinax.basic.dto.input.MessageNotifyReqDTO;
import com.trinasolar.trinax.basic.dto.messageInmail.MessageInmailQueryReqDTO;
import com.trinasolar.trinax.basic.dto.messageInmail.MessageInmailQueryResDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MessageInmailMapper extends BaseMapper<MessageInmailPO> {

    IPage<MessageInmailQueryResDTO> getMsgListByPage(Page<MessageInmailQueryResDTO> page,
                                                     @Param("query") MessageInmailQueryReqDTO query,
                                                     @Param("idList") List<String> idCondition);

    List<MessageInmailQueryResDTO> getMsgListByDay(@Param("query") MessageNotifyReqDTO query,
                                                   @Param("idList") List<String> idCondition);
}
