package com.trinasolar.trinax.basic.domain.file.manager;

import com.trinasolar.trinax.basic.domain.file.repository.atomicservice.UploadFileMapperService;
import com.trinasolar.trinax.basic.domain.file.repository.po.UploadFilePO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class UploadFileManager {

    private final UploadFileMapperService uploadFileMapperService;

    public UploadFilePO getByFileId(String fileId, String readMode) {
        return uploadFileMapperService.lambdaQuery()
                .eq(UploadFilePO::getFileId, fileId)
                .eq(UploadFilePO::getReadMode, readMode)
                .one();
    }

}
