package com.trinasolar.trinax.basic.agreement.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@TableName(value = "content_file_relation")
@Data
public class ContentFileRelationPO {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "内容主键")
    private String contentId;

    @Schema(description = "文件主键 id")
    private String fileId;

    /**
     * 是否已删除;1：已删除 0：未删除
     */
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建人姓名
     */
    private String createdName;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新人姓名
     */
    private String updatedName;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

}
