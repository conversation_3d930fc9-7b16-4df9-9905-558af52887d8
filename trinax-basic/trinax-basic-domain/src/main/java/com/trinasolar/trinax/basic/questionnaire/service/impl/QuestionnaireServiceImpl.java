package com.trinasolar.trinax.basic.questionnaire.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.basic.constants.enums.QuestionnaireStatus;
import com.trinasolar.trinax.basic.dto.input.questionnaire.QuestionnaireModifyReqDTO;
import com.trinasolar.trinax.basic.dto.input.questionnaire.QuestionnaireQueryReqDTO;
import com.trinasolar.trinax.basic.dto.input.questionnaire.QuestionnaireReleaseReqDTO;
import com.trinasolar.trinax.basic.dto.input.questionnaire.QuestionnaireSaveReqDTO;
import com.trinasolar.trinax.basic.dto.output.questionnaire.QuestionnaireDetailResDTO;
import com.trinasolar.trinax.basic.dto.output.questionnaire.QuestionnairePcResDTO;
import com.trinasolar.trinax.basic.dto.questionnaire.QuestionnaireNoticeReqDTO;
import com.trinasolar.trinax.basic.questionnaire.manager.QuestionnaireNoticeManager;
import com.trinasolar.trinax.basic.questionnaire.repository.atomicservice.QuestionnaireMapperService;
import com.trinasolar.trinax.basic.questionnaire.repository.atomicservice.QuestionnaireUserMapperService;
import com.trinasolar.trinax.basic.questionnaire.repository.mapper.QuestionnaireMapper;
import com.trinasolar.trinax.basic.questionnaire.repository.po.QuestionnairePO;
import com.trinasolar.trinax.basic.questionnaire.repository.po.QuestionnaireUserPO;
import com.trinasolar.trinax.basic.questionnaire.service.QuestionnaireService;
import com.trinasolar.trinax.basic.questionnaire.service.biz.QuestionnaireFillBiz;
import com.trinasolar.trinax.basic.questionnaire.service.biz.QuestionnaireUserSaveBiz;
import com.trinasolar.trinax.basic.questionnaire.utils.QuestionnaireIdUtil;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static com.trinasolar.trinax.common.dto.output.ResultCode.FAIL;

/**
 * <AUTHOR> Yang
 */
@RequiredArgsConstructor
@Service
public class QuestionnaireServiceImpl implements QuestionnaireService {

    private final QuestionnaireIdUtil questionnaireIdUtil;
    private final QuestionnaireMapperService questionnaireMapperService;
    private final QuestionnaireUserMapperService questionnaireUserMapperService;
    private final QuestionnaireUserSaveBiz questionnaireUserSaveBiz;
    private final QuestionnaireMapper questionnaireMapper;
    private final QuestionnaireFillBiz questionnaireFillBiz;
    private final QuestionnaireNoticeManager questionnaireNoticeManager;

    @Transactional
    @Override
    public void addQuestionnaire(QuestionnaireSaveReqDTO saveReqDTO) {
        checkQuestionnaireUserIds(saveReqDTO.getUserIds());
        LocalDateTime now = LocalDateTime.now();
        QuestionnairePO questionnairePO = new QuestionnairePO();
        String questionnaireId = questionnaireIdUtil.generateId();
        questionnairePO.setQuestionnaireId(questionnaireId);
        questionnairePO.setTitle(saveReqDTO.getTitle());
        questionnairePO.setUrl(saveReqDTO.getUrl());
        questionnairePO.setEstimatedEndTime(saveReqDTO.getEstimatedEndTime());

        questionnairePO.setCreatedBy(saveReqDTO.getLoginUserId());
        questionnairePO.setCreatedName(saveReqDTO.getLoginUserName());
        questionnairePO.setCreatedTime(now);
        questionnairePO.setUpdatedBy(saveReqDTO.getLoginUserId());
        questionnairePO.setUpdatedName(saveReqDTO.getLoginUserName());
        questionnairePO.setUpdatedTime(now);
        questionnaireMapperService.save(questionnairePO);
        // 增加问卷-用户关系
        questionnaireUserSaveBiz.saveBatch(saveReqDTO.getUserIds(), saveReqDTO.getLoginUserId(), saveReqDTO.getLoginUserName(), questionnaireId);

    }

    private void checkQuestionnaireUserIds(List<String> userIds) {
        if (ObjectUtil.isEmpty(userIds)) {
            throw new BizException(FAIL.getCode(), "问卷调研对象不能为空");
        }
        long count = userIds.stream().distinct().count();
        if (count < userIds.size()) {
            throw new BizException(FAIL.getCode(), "调研对象重复");
        }
    }

    @Transactional
    @Override
    public void modifyQuestionnaire(QuestionnaireModifyReqDTO modifyReqDTO) {
        checkQuestionnaireUserIds(modifyReqDTO.getUserIds());
        LocalDateTime now = LocalDateTime.now();
        String questionnaireId = modifyReqDTO.getQuestionnaireId();
        QuestionnairePO questionnairePO = questionnaireMapperService.findByQuestionnaireId(questionnaireId);
        questionnairePO.setTitle(modifyReqDTO.getTitle());
        questionnairePO.setUrl(modifyReqDTO.getUrl());
        questionnairePO.setEstimatedEndTime(modifyReqDTO.getEstimatedEndTime());
        questionnairePO.setUpdatedBy(modifyReqDTO.getLoginUserId());
        questionnairePO.setUpdatedName(modifyReqDTO.getLoginUserName());
        questionnairePO.setUpdatedTime(now);
        questionnaireMapperService.updateById(questionnairePO);
        // 先删除问卷-用户
        questionnaireUserMapperService.removeByQuestionnaireId(questionnaireId);
        // 后增加问卷-用户
        questionnaireUserSaveBiz.saveBatch(modifyReqDTO.getUserIds(), modifyReqDTO.getLoginUserId(), modifyReqDTO.getLoginUserName(), questionnaireId);
    }

    @Transactional
    @Override
    public void deleteQuestionnaire(String questionnaireId) {
        questionnaireMapperService.removeByQuestionnaireId(questionnaireId);
        questionnaireUserMapperService.removeByQuestionnaireId(questionnaireId);
    }

    @Override
    public void releaseQuestionnaire(QuestionnaireReleaseReqDTO releaseReqDTO) {
        LocalDateTime now = LocalDateTime.now();
        String questionnaireId = releaseReqDTO.getQuestionnaireId();
        QuestionnairePO questionnairePO = questionnaireMapperService.findByQuestionnaireId(questionnaireId);
        questionnairePO.setStatus(QuestionnaireStatus.PUBLISHED.getCode());
        questionnairePO.setValidTime(now);

        questionnairePO.setUpdatedBy(releaseReqDTO.getLoginUserId());
        questionnairePO.setUpdatedName(releaseReqDTO.getLoginUserName());
        questionnairePO.setUpdatedTime(now);
        questionnaireMapperService.updateById(questionnairePO);

        // 发送系统通知
        List<QuestionnaireUserPO> questionnaireUserPOS = questionnaireUserMapperService.listByQuestionnaireId(questionnaireId);
        if (ObjectUtil.isNotEmpty(questionnaireUserPOS)) {
            List<String> userIds = questionnaireUserPOS.stream().map(QuestionnaireUserPO::getUserId).collect(Collectors.toList());
            QuestionnaireNoticeReqDTO questionnaireNoticeReqDTO = QuestionnaireNoticeReqDTO.builder()
                    .setUrl(questionnairePO.getUrl())
                    .setTitle(questionnairePO.getTitle())
                    .setDeadline(DateUtil.format(questionnairePO.getEstimatedEndTime(), DatePattern.NORM_DATETIME_PATTERN))
                    .setUserIdList(userIds)
                    .setCurrentUserId(releaseReqDTO.getLoginUserId())
                    .setCurrentUserName(releaseReqDTO.getLoginUserName());
            questionnaireNoticeManager.execute(questionnaireNoticeReqDTO);
        }

    }

    @Override
    public PageResponse<QuestionnairePcResDTO> pcPageQuestionnaire(PageRequest<QuestionnaireQueryReqDTO> pageReqDTO) {
        IPage<QuestionnairePcResDTO> ipage = questionnaireMapper.pcPageQuestionnaire(new Page<>(pageReqDTO.getIndex(), pageReqDTO.getSize()), pageReqDTO.getQuery());
        questionnaireFillBiz.fillQuestionnaireResPcDTO(ipage.getRecords());
        return PageResponse.toResult(
                pageReqDTO,
                (int) ipage.getTotal(),
                ipage.getRecords());
    }

    @Override
    public QuestionnaireDetailResDTO detailQuestionnaire(String questionnaireId) {
        QuestionnairePO questionnairePO = questionnaireMapperService.findByQuestionnaireId(questionnaireId);
        QuestionnaireDetailResDTO questionnaireDetailResDTO = BeanUtil.copyProperties(questionnairePO, QuestionnaireDetailResDTO.class);
        questionnaireFillBiz.fillQuestionnaireDetailResDTO(questionnaireDetailResDTO);
        return questionnaireDetailResDTO;
    }
}
