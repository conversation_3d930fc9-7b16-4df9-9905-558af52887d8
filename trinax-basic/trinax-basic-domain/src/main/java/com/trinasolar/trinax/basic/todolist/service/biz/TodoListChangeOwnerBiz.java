package com.trinasolar.trinax.basic.todolist.service.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoStatusEnum;
import com.trinasolar.trinax.basic.dto.input.TodoListChangeOwnerReqDTO;
import com.trinasolar.trinax.basic.todolist.repository.atomicservice.TodoListMapperService;
import com.trinasolar.trinax.basic.todolist.repository.po.TodoListPO;
import com.trinasolar.trinax.common.dto.output.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@RequiredArgsConstructor
@Component
@Slf4j
public class TodoListChangeOwnerBiz {

    private final TodoListMapperService todoListMapperService;
    private static int SIZE = 1000;

    /**
     * 提交变更：生成主任务，子任务，发送mq
     */
    public Result<String> changeOwner(TodoListChangeOwnerReqDTO reqDTO) {
        log.info("Todo change owner 入参TodoListChangeOwnerReqDTO：{}", reqDTO);
        updateOwner(reqDTO);
        return Result.ok();
    }


    private void updateOwner(TodoListChangeOwnerReqDTO reqDTO){
        if(CollUtil.isNotEmpty(reqDTO.getBizNoList()) && reqDTO.getBizNoList().size() > SIZE){
            List<List<String>> newList = ListUtil.partition(reqDTO.getBizNoList(),SIZE);
            newList.forEach(e-> todoListMapperService.lambdaUpdate()
                    .eq(TodoListPO::getTodoUserId,reqDTO.getOriginUserId())
                    .eq(TodoListPO::getTodoStatus,TodoStatusEnum.TODO.getCode())
                    .in(TodoListPO::getBizNo,e)
                    .likeRight(StringUtils.isNotEmpty(reqDTO.getBizPrefix()),TodoListPO::getBizCode,reqDTO.getBizPrefix())
                    .set(TodoListPO::getTodoUserId,reqDTO.getNewUserId())
                    .set(TodoListPO::getUpdatedBy,reqDTO.getUpdatedBy())
                    .set(TodoListPO::getUpdatedName,reqDTO.getUpdatedName())
                    .set(TodoListPO::getUpdatedTime,LocalDateTime.now())
                    .update());
        }else{
            todoListMapperService.lambdaUpdate()
                    .eq(TodoListPO::getTodoUserId,reqDTO.getOriginUserId())
                    .eq(TodoListPO::getTodoStatus,TodoStatusEnum.TODO.getCode())
                    .in(CollUtil.isNotEmpty(reqDTO.getBizNoList()),TodoListPO::getBizNo,reqDTO.getBizNoList())
                    .likeRight(StringUtils.isNotEmpty(reqDTO.getBizPrefix()),TodoListPO::getBizCode,reqDTO.getBizPrefix())
                    .set(TodoListPO::getTodoUserId,reqDTO.getNewUserId())
                    .set(TodoListPO::getUpdatedBy,reqDTO.getUpdatedBy())
                    .set(TodoListPO::getUpdatedName,reqDTO.getUpdatedName())
                    .set(TodoListPO::getUpdatedTime,LocalDateTime.now())
                    .update();
        }
    }


}
