package com.trinasolar.trinax.basic.feedback.service;

import com.trinasolar.trinax.basic.dto.input.feedback.FeedbackQueryReqDTO;
import com.trinasolar.trinax.basic.dto.input.feedback.FeedbackSaveReqDTO;
import com.trinasolar.trinax.basic.dto.output.feedback.FeedbackDetailResDTO;
import com.trinasolar.trinax.basic.dto.output.feedback.FeedbackResPcDTO;
import com.trinasolar.trinax.basic.dto.output.feedback.FeedbackResPcExcelDTO;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;

import java.util.List;

/**
 * <p>
 * 使用反馈信息 服务类
 * </p>
 *
 * <AUTHOR> Yang
 * @since 2024-01-02
 */
public interface FeedbackService {

    void addFeedback(FeedbackSaveReqDTO saveReqDTO);

    FeedbackDetailResDTO feedbackDetail(String feedbackId);

    PageResponse<FeedbackResPcDTO> pageFeedbackResPC(PageRequest<FeedbackQueryReqDTO> reqDTO);

    List<FeedbackResPcExcelDTO> listFeedbackResPcExcel(FeedbackQueryReqDTO reqDTO);

    FeedbackDetailResDTO feedbackDetailByReplyId(String replyId);
}
