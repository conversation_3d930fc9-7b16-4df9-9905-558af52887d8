package com.trinasolar.trinax.basic.domain.messageInmail.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("message_inmail")
public class MessageInmailPO extends BasePO {
    //消息类型: PRIVATE-私信 PUBLIC-公告
    @TableField("message_type")
    private String messageType;
    //操作类型: CANCEL-取消 CREATE-创建
    @TableField("op_type")
    private String opType;
    //业务编码
    @TableField("biz_code")
    private String bizCode;
    //业务编号
    @TableField("biz_no")
    private String bizNo;
    //消息标题
    @TableField("title")
    private String title;
    //消息内容
    @TableField("content")
    private String content;
    //发布时间
    @TableField("publish_time")
    private LocalDateTime publishTime;
}
