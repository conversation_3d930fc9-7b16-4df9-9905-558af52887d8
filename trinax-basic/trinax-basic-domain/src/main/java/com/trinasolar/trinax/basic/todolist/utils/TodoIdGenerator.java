package com.trinasolar.trinax.basic.todolist.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import dtt.segment.id.generator.service.IdService;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 待办id生成
 */
@Component
public class TodoIdGenerator {

    @Autowired
    private IdService idService;

    /**
     * 待办id生成
     * @return
     */
    public String generateTodoId() {
        String prefix = "TD" +  DateFormatUtils.format(new Date(), "yyyyMMdd");
        String date = DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        Long num =  idService.nextId("basic-todo-id-" + date,"basic-todo-id");
        String suffix = String.format("%06d",num);
        return prefix + suffix;
    }
}
