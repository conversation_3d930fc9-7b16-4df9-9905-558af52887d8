package com.trinasolar.trinax.basic.domain.file.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.trinasolar.trinax.basic.constants.enums.FileReadModeEnum;
import com.trinasolar.trinax.basic.domain.file.manager.UploadFileManager;
import com.trinasolar.trinax.basic.domain.file.repository.atomicservice.UploadFileMapperService;
import com.trinasolar.trinax.basic.domain.file.repository.po.UploadFilePO;
import com.trinasolar.trinax.basic.domain.file.service.UploadFileService;
import com.trinasolar.trinax.basic.dto.input.SaveUploadFileReqDTO;
import com.trinasolar.trinax.basic.dto.output.GetUploadFileResDTO;
import com.trinasolar.trinax.basic.dto.output.SaveUploadFileResDTO;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
@AllArgsConstructor
public class UploadFileServiceImpl implements UploadFileService {

    private final UploadFileMapperService uploadFileMapperService;
    private final UploadFileManager uploadFileManager;

    @Override
    public SaveUploadFileResDTO save(SaveUploadFileReqDTO req) {
        log.info("新增上传文件信息开始，fileName={}，req={}", req.getFileName(), JacksonUtil.bean2Json(req));
        LocalDateTime now = LocalDateTime.now();

        // 生成文件ID
        String fileId = IdUtil.fastSimpleUUID();

        UploadFilePO uploadFile = BeanUtil.copyProperties(req, UploadFilePO.class);
        uploadFile.setFileId(fileId);
        uploadFile.setCreatedBy(req.getUserId());
        uploadFile.setCreatedName(req.getUserName());
        uploadFile.setCreatedTime(now);
        uploadFile.setUpdatedBy(req.getUserId());
        uploadFile.setUpdatedName(req.getUserName());
        uploadFile.setUpdatedTime(now);
        uploadFileMapperService.save(uploadFile);

        SaveUploadFileResDTO result = new SaveUploadFileResDTO();
        result.setFileId(fileId);
        log.info("新增上传文件信息完成，fileName={}", req.getFileName());
        return result;
    }

    @Override
    public GetUploadFileResDTO getPubByFileId(String fileId) {
        UploadFilePO file = uploadFileManager.getByFileId(fileId, FileReadModeEnum.PUB.getCode());
        return BeanUtil.copyProperties(file, GetUploadFileResDTO.class);
    }

    @Override
    public GetUploadFileResDTO getPriByFileId(String fileId) {
        UploadFilePO file = uploadFileManager.getByFileId(fileId, FileReadModeEnum.PRI.getCode());
        return BeanUtil.copyProperties(file, GetUploadFileResDTO.class);
    }

}
