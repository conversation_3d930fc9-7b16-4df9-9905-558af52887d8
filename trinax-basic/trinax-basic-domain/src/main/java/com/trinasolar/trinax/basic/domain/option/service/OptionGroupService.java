package com.trinasolar.trinax.basic.domain.option.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.basic.domain.option.repository.atomicservice.OptionItemLanguageMapperService;
import com.trinasolar.trinax.basic.domain.option.repository.mapper.OptionGroupMapper;
import com.trinasolar.trinax.basic.domain.option.repository.mapper.OptionItemDOMapper;
import com.trinasolar.trinax.basic.domain.option.repository.po.OptionGroupPO;
import com.trinasolar.trinax.basic.domain.option.repository.po.OptionItemLanguagePO;
import com.trinasolar.trinax.basic.dto.input.OptionGroupPageReqDTO;
import com.trinasolar.trinax.basic.dto.input.OptionItemBatchReqDTO;
import com.trinasolar.trinax.basic.dto.output.*;
import com.trinasolar.trinax.basic.dto.output.option.OptionGroupResDTO;
import com.trinasolar.trinax.basic.dto.output.option.OptionInfoDownloadRTO;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OptionGroupService {
    @Autowired
    OptionGroupMapper optionGroupMapper;

    @Autowired
    OptionItemDOMapper optionItemMapper;

    @Autowired
    private OptionItemLanguageMapperService optionItemLanguageMapperService;


    /**
     * 获取取消原因
     *
     * @param language 语种
     * @return 获取对应语种的取消原因
     */
    public List<OptionItemDTO> getOptionItemList(String optionGroup, String language) {
        return optionGroupMapper.getOptionItemList(optionGroup, language);
    }


    public List<OptionItemLanguageResDTO> getOptionItemListBatch(OptionItemBatchReqDTO reqDTO) {
        List<OptionItemLanguagePO> dbList = optionItemLanguageMapperService.lambdaQuery()
                .eq(OptionItemLanguagePO::getLanguage, reqDTO.getLanguage())
                .in(OptionItemLanguagePO::getOptionGroup, reqDTO.getOptionGroupList())
                .eq(OptionItemLanguagePO::getIsDeleted, 0)
                .list();

        return BeanUtil.copyToList(dbList, OptionItemLanguageResDTO.class);
    }


    /**
     * 获取所有的行业属性
     * <p>
     * 数据量不大 200条内，全量查询后封装为2级层次结构
     *
     * @param language 语种
     * @return 2级层次行业信息
     */
    public List<IndustryOptionItemDTO> getIndustryOptionItemList(String language) {
        String industry = "industry_attribute";
        List<OptionItemLevelDTO> optionItemList = optionItemMapper.getAllItemLevelList(industry, language);
        // 获取1级行业
        List<OptionItemLevelDTO> topLevelIndustry = optionItemList.stream()
                .filter(e -> e.getLevel().equals(1))
                .sorted(Comparator.comparing(OptionItemLevelDTO::getSeq))
                .collect(Collectors.toList());

        List<IndustryOptionItemDTO> result = new ArrayList<>();
        for (OptionItemLevelDTO o : topLevelIndustry) {
            // 遍历筛选排序后重新组装对象
            List<OptionItemLevelDTO> secondLevelIndustry = optionItemList.stream()
                    .filter(e -> e.getParentValue().equals(o.getOptionValue()))
                    .distinct()
                    .sorted(Comparator.comparing(OptionItemLevelDTO::getSeq))
                    .collect(Collectors.toList());
            List<OptionItemDTO> optionItemDTOList = convert(secondLevelIndustry, language);
            result.add(new IndustryOptionItemDTO(o, o.getOptionName(), language, optionItemDTOList));
        }
        return result;
    }

    /**
     * 类型转换
     *
     * @param list     原始数据
     * @param language 对应的语种
     * @return 转换后的类型
     */
    private List<OptionItemDTO> convert(List<OptionItemLevelDTO> list, String language) {
        List<OptionItemDTO> optionList = new ArrayList<>();
        list.forEach(i -> optionList.add(new OptionItemDTO(i, language)));
        return optionList;
    }

    /**
     * 查询字典场景
     *
     * @return 字典场景
     */
    public List<OptionGroupDTO> getGroupList() {
        return optionItemMapper.getGroupList();
    }

    /**
     * 字典组分页查询
     *
     * @param reqDTO
     * @return
     */
    public Result<PageResponse<OptionGroupResDTO>> findOptionGroupPage(PageRequest<OptionGroupPageReqDTO> reqDTO) {
        LambdaQueryWrapper<OptionGroupPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getQuery().getOptionGroup()), OptionGroupPO::getOptionGroup, reqDTO.getQuery().getOptionGroup())
                .like(StringUtils.isNotBlank(reqDTO.getQuery().getOptionGroupName()), OptionGroupPO::getOptionGroupName, reqDTO.getQuery().getOptionGroupName())
                .orderByDesc(OptionGroupPO::getId);
        Page<OptionGroupPO> page = new Page<>(reqDTO.getIndex(), reqDTO.getSize());
        IPage<OptionGroupPO> groupPage = optionGroupMapper.selectPage(page, queryWrapper);

        List<OptionGroupPO> pageList = groupPage.getRecords();
        List<OptionGroupResDTO> resultList = BeanUtil.copyToList(pageList, OptionGroupResDTO.class);

        PageResponse<OptionGroupResDTO> result = PageResponse.toResult(
                reqDTO.getIndex(),
                reqDTO.getSize(),
                (int) groupPage.getTotal(),
                resultList);

        return Result.ok(result);
    }

    public Result<List<OptionInfoDownloadRTO>> qryOptionGroup(OptionGroupPageReqDTO request) {
        List<OptionInfoDownloadRTO> result = optionGroupMapper.qryOptionGroup(request);
        return Result.ok(result);
    }
}
