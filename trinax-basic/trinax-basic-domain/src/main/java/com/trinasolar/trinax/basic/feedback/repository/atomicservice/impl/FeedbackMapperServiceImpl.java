package com.trinasolar.trinax.basic.feedback.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.basic.feedback.repository.atomicservice.FeedbackMapperService;
import com.trinasolar.trinax.basic.feedback.repository.mapper.FeedbackMapper;
import com.trinasolar.trinax.basic.feedback.repository.po.FeedbackPO;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 使用反馈信息 服务实现类
 * </p>
 *
 * <AUTHOR> <PERSON>
 * @since 2024-01-02
 */
@Service
public class FeedbackMapperServiceImpl extends ServiceImpl<FeedbackMapper, FeedbackPO> implements FeedbackMapperService {

    @Override
    public FeedbackPO findByFeedbackId(String feedbackId) {
        return getOne(new LambdaQueryWrapper<FeedbackPO>().eq(FeedbackPO::getFeedbackId, feedbackId));
    }
}
