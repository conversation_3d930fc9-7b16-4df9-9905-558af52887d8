package com.trinasolar.trinax.basic.dto.input.region;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Schema(description = "region查询")
@Data
public class RegionQueryReqDTO {

    @Schema(description = "Region Name")
    private String name;

    @Schema(description = "模糊匹配，(nameLike > name) or (nameLike < name)")
    private String nameLike;

    @Schema(description = "区域类型 PROVINCE:省份 CITY:城市")
    @NotBlank
    private String regionType;

}
