package com.trinasolar.trinax.basic.dto.output;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class IndustryOptionItemDTO {
    private String optionGroup;
    private String optionGroupName;
    private String language;
    private String optionValue;
    private String optionName;

    private List<OptionItemDTO> optionItemList;

    public IndustryOptionItemDTO(OptionItemLevelDTO option, String optionGroupName,
                                 String language, List<OptionItemDTO> optionItemList) {
        this.optionGroup = option.getOptionGroup();
        this.optionGroupName = optionGroupName;
        this.language = language;
        this.optionValue = option.getOptionValue();
        this.optionName = option.getOptionName();
        this.optionItemList = optionItemList;
    }
}
