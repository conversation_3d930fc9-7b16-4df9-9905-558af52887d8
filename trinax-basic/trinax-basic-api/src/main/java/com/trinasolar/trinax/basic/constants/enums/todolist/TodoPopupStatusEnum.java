package com.trinasolar.trinax.basic.constants.enums.todolist;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 待办弹窗状态枚举
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum TodoPopupStatusEnum {

    NOT_POPUP("0", "未弹窗"),
    HAS_POPUP("1", "已弹窗"),
    ;

    private final String code;
    private final String desc;

    public static String getDescByCode(String code) {
        for (TodoPopupStatusEnum value : TodoPopupStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

}
