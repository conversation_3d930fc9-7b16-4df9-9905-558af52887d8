package com.trinasolar.trinax.basic.constants.enums.changeowner;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * change owner 修改任务状态枚举（主任务，子任务共用）
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ChangeTaskStatusEnum {

    SUCCESS("SUCCESS", "成功"),
    FAIL("FAIL", "失败"),
    IN_PROGRESS("IN_PROGRESS", "进行中"),
    //可能不需要“未开始”状态
//    NOT_START("NOT_START", "未开始"),
    ;
    private final String code;
    private final String desc;

    public static String getDescByCode(String code) {
        for (ChangeTaskStatusEnum value : ChangeTaskStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

}
