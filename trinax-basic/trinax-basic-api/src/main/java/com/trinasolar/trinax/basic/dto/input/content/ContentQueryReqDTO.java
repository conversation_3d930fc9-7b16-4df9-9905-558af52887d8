package com.trinasolar.trinax.basic.dto.input.content;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;

@Data
@Schema(description = "查找内容（协议）入参")
public class ContentQueryReqDTO {

    @Schema(description = "类型（隐私政策-POLICY、用户协议-AGREEMENT、BULLETIN-公告）")
    private String type;

    @Schema(description = "0失效，1有效，2待生效 为空查询所有")
    private Integer isEffect;

    @Schema(description = "用户ID")
    private Long userId;

}
