package com.trinasolar.trinax.basic.dto.output.questionnaire;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 分了类型的用户
 * <AUTHOR> yang
 */
@Data
public class QuestionnaireTypeUserResDTO {

    @Schema(description = "内部用户")
    private List<QuestionnaireTypeUserItemDTO> internalUsers;

    @Schema(description = "外部用户")
    private List<QuestionnaireTypeUserItemDTO> externalUsers;

}
