package com.trinasolar.trinax.basic.dto.input.email;

import com.trinasolar.trinax.basic.constants.enums.EmailTemplateEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@RequiredArgsConstructor(staticName = "builder")
@Schema(description = "发送邮件入参")
@Accessors(chain = true)
public class SendEmailReqDTO{

    @Schema(description = "客户名")
    private String enterpriseName;
    @Schema(description = "客户联系人")
    private String externalUsername;
    @Schema(description = "客户联系电话")
    private String externalUserMobile;

    @Schema(description = "申请编号")
    private String applyNo;
    @Schema(description = "公司抬头")
    private String enterpriseHeader;
    @Schema(description = "纳税人识别号")
    private String creditCode;

    @Schema(description = "供货方实体")
    private String supplyEntity;
    @Schema(description = "发票类型")
    private String invoiceType;
    @Schema(description = "开票单位")
    private String invoiceEnterprise;

    @Schema(description = "开户银行")
    private String bankName;
    @Schema(description = "银行账号")
    private String bankAccount;
    @Schema(description = "地址")
    private String bankAddress;
    @Schema(description = "电话")
    private String bankMobile;

    @Schema(description = "开票申请人")
    private String applyUserName;
    @Schema(description = "开票申请人联系电话")
    private String applyUserMobile;
    @Schema(description = "德勤联系人")
    private String trinaUserName;
    @Schema(description = "德勤联系人电话")
    private String trinaUserMobile;
    @Schema(description = "申请开票金额(元)")
    private String invoiceAmount;
    @Schema(description = "申请开票容量(MW)")
    private String invoiceVolume;
    @Schema(description = "申请创建日期")
    private String invoiceDate;
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "邮件接收邮箱")
    private List<String> toEmails;
    @Schema(description = "邮件主题")
    private String subject;
    @Schema(description = "抄送邮箱")
    private List<String> ccEmails;

    @Schema(description = "当前用户id")
    private String currentUserId;
    @Schema(description = "当前用户名称")
    private String currentUserName;
    @Schema(description = "邮件模版枚举:EmailTemplateEnum")
    private EmailTemplateEnum emailTemplateEnum;


}
