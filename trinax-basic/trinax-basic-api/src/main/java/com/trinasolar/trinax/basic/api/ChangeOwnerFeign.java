package com.trinasolar.trinax.basic.api;

import com.trinasolar.trinax.basic.constants.ServiceIds;
import com.trinasolar.trinax.basic.dto.input.changeOwner.*;
import com.trinasolar.trinax.basic.dto.output.changeOwner.ChangeSubTaskQryResDTO;
import com.trinasolar.trinax.basic.dto.output.changeOwner.ChangeTaskQryResDTO;
import com.trinasolar.trinax.basic.dto.output.changeOwner.SubsTaskExportResDTO;
import com.trinasolar.trinax.basic.dto.output.changeOwner.TaskExportResDTO;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Tag(name = "Change owner相关接口")
@FeignClient(value = ServiceIds.BASIC_SERVICE)
public interface ChangeOwnerFeign {

    @Operation(summary = "提交变更")
    @PostMapping("/change-owner/submitChange")
    Result<String> submitChange(@RequestBody ChangeOwnerSubmitReqDTO req);

    @Operation(summary = "更新changeOwner任务状态")
    @PostMapping("/change-owner/updateTaskStatus")
    Result<String> updateTaskStatus(@RequestBody ChangeOwnerStatusUpdateReqDTO req);

    @Operation(summary = "重试子任务")
    @PostMapping("/change-owner/retrySubTask")
    Result<String> retrySubTask(@RequestBody ChangeOwnerRetryReqDTO req);

    @Operation(summary = "查询主任务")
    @PostMapping("/change-owner/changeTaskList")
    Result<PageResponse<ChangeTaskQryResDTO>> changeTaskList(@RequestBody PageRequest<ChangeTaskQryReqDTO> req);

    @Operation(summary = "查询子任务")
    @PostMapping("/change-owner/changeSubTaskList")
    Result<PageResponse<ChangeSubTaskQryResDTO>> changeSubTaskList(@RequestBody PageRequest<ChangeSubTaskReqDTO> req);

    @Operation(summary = "查询主任务Export")
    @PostMapping("/change-owner/changeTaskExport")
    Result<List<TaskExportResDTO>> changeTaskExport(@RequestBody ChangeTaskQryReqDTO req);

    @Operation(summary = "查询子任务Export")
    @PostMapping("/change-owner/changeSubTaskExport")
    Result<List<SubsTaskExportResDTO>> changeSubTaskExport(@RequestBody ChangeSubTaskReqDTO req);

    @Operation(summary = "校验原任与继任是否有没成功的任务，存在则抛出异常")
    @PostMapping("/change-owner/validTaskUserExist")
    Result<Void> validTaskUserExist(@RequestParam String originUserId, @RequestParam String newUserId);

}
