package com.trinasolar.trinax.basic.dto.output.option;

import com.trinasolar.trinax.basic.dto.output.OptionItemLanguageResDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "查询字典item详情")
public class OptionItemDetailResDTO {

    private Long id;

    @Schema(description = "字典组编码")
    private String optionGroup;

    @Schema(description = "字典项编码")
    private String optionValue;

    @Schema(description = "字典项名称")
    private String optionDesc;

    @Schema(description = "字典上一级")
    private String parentValue;

    @Schema(description = "层级")
    private Integer level;

    @Schema(description = "排序号")
    private Integer seq;

    @Schema(description = "是否启用：0-启用；1-停用")
    private Integer isDeleted;

    private String createdBy;
    private String createdName;
    private LocalDateTime createdTime;
    private String updatedBy;
    private String updatedName;
    private LocalDateTime updatedTime;

    @Schema(description = "字典语言项")
    List<OptionItemLanguageResDTO> itemList;

}
