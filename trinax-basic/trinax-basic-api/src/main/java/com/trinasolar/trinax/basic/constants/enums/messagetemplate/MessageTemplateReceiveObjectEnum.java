package com.trinasolar.trinax.basic.constants.enums.messagetemplate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息通知对象枚举
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum MessageTemplateReceiveObjectEnum {

    DISTRIBUTION_SALES("DISTRIBUTION_SALES", "分销销售"),
    ECOLOGICAL_PARTNER("ECOLOGICAL_PARTNER", "生态伙伴"),
    DISTRIBUTION_OPERATIONS("DISTRIBUTION_OPERATIONS", "分销运营"),
    ADMINISTRATOR("ADMINISTRATOR", "管理员"),

    ;
    private final String code;
    private final String desc;

    public static String getDescByCode(String code) {
        for (MessageTemplateReceiveObjectEnum value : MessageTemplateReceiveObjectEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

}
