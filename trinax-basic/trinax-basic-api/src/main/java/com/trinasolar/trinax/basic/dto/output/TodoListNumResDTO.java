package com.trinasolar.trinax.basic.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TodoListNumResDTO {

    @Schema(description ="已完成数量")
    private Long done;

    @Schema(description ="未完成数量")
    private Long todo;


}
