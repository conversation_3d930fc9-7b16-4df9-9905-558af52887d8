package com.trinasolar.trinax.basic.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description ="待办响应参数")
public class TodoListResDTO {

    @Schema(description ="待办ID")
    private String todoId;

    @Schema(description ="待办标题")
    private String todoTitle;

    @Schema(description ="待办内容")
    private String todoContent;

    @Schema(description ="待办状态")
    private String todoStatus;

    @Schema(description ="待办状态-文本")
    private String todoStatusText;

    @Schema(description ="业务类型")
    private String bizCode;

    @Schema(description ="业务类型-文本")
    private String bizCodeText;

    @Schema(description ="业务编号")
    private String bizNo;

    @Schema(description ="待办人员ID")
    private String todoUserId;

    @Schema(description ="人员所属组织类型")
    private String organizationType;

    @Schema(description ="人员所属组织类型-文本")
    private String organizationTypeText;

    @Schema(description ="完成时间")
    private LocalDateTime doneTime;

    @Schema(description ="取消时间")
    private LocalDateTime cancelTime;

    @Schema(description ="是否已删除;1：已删除 0：未删除")
    private Integer isDeleted;

    @Schema(description ="创建人UserId")
    private String createdBy;

    @Schema(description ="创建人姓名")
    private String createdName;

    @Schema(description ="创建时间")
    private LocalDateTime createdTime;

    @Schema(description ="更新人userId")
    private String updatedBy;

    @Schema(description ="更新人姓名")
    private String updatedName;

    @Schema(description ="更新时间")
    private LocalDateTime updatedTime;




}
