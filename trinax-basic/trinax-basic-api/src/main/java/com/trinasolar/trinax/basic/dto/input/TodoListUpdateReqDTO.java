package com.trinasolar.trinax.basic.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Builder
@Data
@Schema(description ="修改待办请求参数")
public class TodoListUpdateReqDTO {

    @NotBlank(message = "业务编码不为空")
    @Schema(description ="业务编码：如意向单号；发货申请单号")
    private String bizNo;

    @NotBlank(message = "待办业务类型编码不为空")
    @Schema(description ="待办业务类型编码：TodoBizCodeEnum")
    private String bizCode;

    @NotBlank(message = "待办状态不为空")
    @Schema(description ="待办状态:TodoStatusEnum")
    private String todoStatus;

    @Schema(description ="完成时间")
    private LocalDateTime doneTime;

    @Schema(description ="取消时间")
    private LocalDateTime cancelTime;

    @NotBlank(message = "更新人userId不为空")
    @Schema(description ="更新人userId")
    private String updatedBy;

    @NotBlank(message = "更新人姓名不为空")
    @Schema(description ="更新人姓名")
    private String updatedName;


}
