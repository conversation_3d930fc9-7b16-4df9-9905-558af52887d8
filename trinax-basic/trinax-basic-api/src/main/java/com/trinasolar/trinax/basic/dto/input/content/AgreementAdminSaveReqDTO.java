package com.trinasolar.trinax.basic.dto.input.content;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 协议保存 Request DTO")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class AgreementAdminSaveReqDTO {
    @Schema(description = "修改时使用")
    private Long id;

    @Schema(description = "版本")
    private String version;

    @Schema(description = "协议类型 POLICY 隐私政策，AGREEMENT 用户协议，BULLETIN 公告")
    private String type;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "0失效，1有效，2待生效")
    private Integer isEffect;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "更新人id",hidden = true)
    private String updatedBy;

    /**
     * 更新人姓名,bff层提供
     */
    @Schema(description = "更新人姓名",hidden = true)
    private String updatedName;
    @Schema(description = "创建人姓名",hidden = true)
    private String createdName;
    @Schema(description = "创建人id",hidden = true)
    private String createdBy;

    @Schema(description = "保存的文件id")
    private List<String> fileIdList;

    @JsonFormat(pattern = "yyyy-MM-dd HH")
    @Schema(description = "生效时间")
    private LocalDateTime effectSj;
    @JsonFormat(pattern = "yyyy-MM-dd HH")
    @Schema(description = "失效时间")
    private LocalDateTime outSj;

    @Schema(description = "生效区域")
    private List<String> areaScopes;

}
