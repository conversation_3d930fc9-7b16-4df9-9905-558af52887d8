package com.trinasolar.trinax.basic.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 极光消息内容
 */
@Data
@NoArgsConstructor
@Schema(description = "发送系统消息参数")
public class MessageSendNoticeReqDTO {

    @Schema(description = "消息模板编码(通过该值找模板内容)")
    private String templateCode;

    @Schema(description = "消息通知人userIdList")
    private List<String> userIdList;

    @Schema(description = "消息接收角色 List")
    private List<String> roleIdList;

    /**
     * 根据 receiveType 取 userIdList 或 roleIdList
     */
    @Schema(description = "消息接收人类型（用户：USERID（默认）,角色：ROLEID）")
    private String receiveType;

    @Schema(description = "消息模板替换内容map")
    private Map<String, String> content;

    @Schema(description = "业务编码：如意向单编号")
    private String bizNo;

    @Schema(description = "消息创建人userId")
    private String createdBy;

    @Schema(description = "消息创建人姓名")
    private String createdName;
    //

    @Schema(description = "系统通知消息类型：MessageTypeEnum")
    private String messageType;

    @Schema(description = "系统通知操作类型：MessageOpTypeEnum")
    private String opType;

    @Schema(description = "业务场景编码：MessageBizCodeEnum")
    private String bizCode;


}
