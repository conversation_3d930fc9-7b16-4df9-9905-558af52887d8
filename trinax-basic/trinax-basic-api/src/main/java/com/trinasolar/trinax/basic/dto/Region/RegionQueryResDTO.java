package com.trinasolar.trinax.basic.dto.Region;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "交货区域-请求结果")
public class RegionQueryResDTO {
    @Schema(description = "区域编码（省/市）")
    private String code;
    @Schema(description = "区域名称")
    private String name;
    @Schema(description = "父级code")
    private String parentCode;
    @Schema(description = "SFDC区域编码")
    private String sfdcCode;
    @Schema(description = "A-Z排序字段")
    private String alphabetOrder;
    @Schema(description = "是否为直辖市")
    private Boolean isDirect;
    @Schema(description = "层级")
    private Integer level;

    @Schema(description = "是否有孩子")
    private boolean hasChildren;
}
