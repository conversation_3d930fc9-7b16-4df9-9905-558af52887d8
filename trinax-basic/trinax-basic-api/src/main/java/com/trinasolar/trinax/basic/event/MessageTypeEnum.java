package com.trinasolar.trinax.basic.event;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum MessageTypeEnum {

    MSG_TYPE_PRIVATE("PRIVATE", "私信"),
    MSG_TYPE_PUBLIC("PUBLIC", "公告");

    private String value;
    private String displayName;

    MessageTypeEnum(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private static Map<String, MessageTypeEnum> valueMap = new HashMap<String, MessageTypeEnum>();

    static {
        for (MessageTypeEnum e : MessageTypeEnum.values()) {
            valueMap.put(e.value, e);
        }
    }

    public static MessageTypeEnum get(String value) {
        return valueMap.get(value);
    }
}
