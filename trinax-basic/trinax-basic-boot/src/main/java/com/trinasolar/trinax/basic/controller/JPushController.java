package com.trinasolar.trinax.basic.controller;

import com.trinasolar.trinax.basic.api.JPushMessageFeign;
import com.trinasolar.trinax.basic.dto.input.JPushMessageReqDTO;
import com.trinasolar.trinax.integration.api.JPushFeign;
import com.trinasolar.trinax.integration.dto.input.JPushReqDTO;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 13/09/2023
 * @description
 */
@Api(tags = "极光推送Controller")
@Slf4j
@RestController
public class JPushController implements JPushMessageFeign {

    @Autowired
    private JPushFeign jPushFeign;

    @Autowired
    private SysUserFeign sysUserFeign;

    @Override
    public void senToUser(JPushMessageReqDTO jPushMessageReqDTO) {
        SysUserRespDTO userRespDTO = sysUserFeign.getUserByUserId(jPushMessageReqDTO.getUserId()).getData();
        JPushReqDTO jPushReqDTO = new JPushReqDTO();
        jPushReqDTO.setAlert(jPushMessageReqDTO.getAlert());
        jPushReqDTO.setRegistrationId(userRespDTO.getRegistrationId());
        jPushFeign.sendToRegistrationId(jPushReqDTO);
    }

}
