package com.trinasolar.trinax.basic.job;

import com.trinasolar.trinax.basic.agreement.service.ContentService;
import com.trinasolar.trinax.basic.constants.enums.AgreementTypeEnum;
import com.trinasolar.trinax.basic.constants.enums.content.ContentEffectEnum;
import com.trinasolar.trinax.basic.dto.input.content.AgreementAdminPageReqDTO;
import com.xxl.job.core.handler.annotation.XxlJob;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ContentXxlJob {

	@Autowired
	private MqManager mqManager;
	@Autowired
	private ContentService contentService;

	@XxlJob("effectcontentHandler")
	public void effectcontentHandler() throws Exception {
		log.info("--basic：定时任务修改公告生效start---.");
		contentService.getEffectContentList();
		log.info("--basic：定时任务修改公告生效end---.");
	}

	@XxlJob("unEffectContentHandler")
	public void unEffectContentHandler() throws Exception {
		log.info("--basic：定时任务修改公告失效start---.");
		contentService.getUnEffectContentList();
		log.info("--basic：定时任务修改公告失效end---.");
	}


	@XxlJob("contentRecordHandler")
	public void contentRecordHandler() throws Exception {
		log.info("--basic：定时任务删除公告记录start---.");
		contentService.contentRecordDeal();
		log.info("--basic：定时任务删除公告记录end---.");
	}
}
