package com.trinasolar.trinax.basic.controller;

import com.trinasolar.trinax.basic.api.SmsSendFeign;
import com.trinasolar.trinax.basic.domain.sms.service.SmsService;
import com.trinasolar.trinax.basic.dto.input.TrinaSolarSmsReqDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 13/09/2023
 * @description
 */
@Api(tags = "短信接口")
@Slf4j
@RestController
public class SmsSendController implements SmsSendFeign {

    @Autowired
    private SmsService smsService;

    @Override
    public Result<Boolean> trinaSolarSend(TrinaSolarSmsReqDTO reqDTO) {
        return smsService.trinaSolarSend(reqDTO);
    }

}