package com.trinasolar.trinax.basic.controller;

import com.trinasolar.trinax.basic.api.MessageTemplateFeign;
import com.trinasolar.trinax.basic.dto.input.MessageTemplateGenerateReqDTO;
import com.trinasolar.trinax.basic.dto.output.MessageTemplateGenerateResDTO;
import com.trinasolar.trinax.basic.messagetemplate.service.MessageTemplateService;
import com.trinasolar.trinax.common.dto.output.Result;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 08/11/2023
 * @description
 */
@Api(tags = "消息模板相关接口")
@Slf4j
@RestController
@AllArgsConstructor
public class MessageTemplateController implements MessageTemplateFeign {

    private final MessageTemplateService messageTemplateService;

    @Override
    public Result<MessageTemplateGenerateResDTO> findMessageTemplate(MessageTemplateGenerateReqDTO reqDTO) {
        MessageTemplateGenerateResDTO resDTO = messageTemplateService.findMessageTemplate(reqDTO);
        if(ObjectUtils.isEmpty(resDTO)){
            return Result.fail("消息模板不存在");
        }else{
            return Result.ok(resDTO);
        }

    }
}
