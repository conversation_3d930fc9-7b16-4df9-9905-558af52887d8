spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${DATASOURCE_URL}
    username: ${DATASOURCE_USERNAME}
    password: ${DATASOURCE_PASSWORD}
#    password: docker123456##
    hikari:
      minimum-idle: 10
      maximum-pool-size: 20
      idle-timeout: 10000
      max-lifetime: 1800000
      connection-timeout: 30000
  redis:
    # redis数据库索引（默认为0），我们使用索引为3的数据库，避免和其他数据库冲突
    database: 0
    # redis连接超时时间（单位为毫秒）
    timeout: 30000
    password: ${REDIS_PASSWORD}
    sentinel:
      master: redis-master
      nodes:
        - ${REDIS_NODES_1}
        - ${REDIS_NODES_2}
        - ${REDIS_NODES_3}

  mail:
    default-encoding: UTF-8
    host: mail.deloitte.com
    port: 25
    username: <EMAIL>
    password: ZHIZUNBAO1234.
    properties:
      mail:
        smtp:
          auth: false
          ssl:
            enable: false
          starttls:
            enable: false
            required: false
#日志上报环境地址
logging:
  collector:
    address: ${LOGGING_COLLECTOR_ADDRESS}



integration:
  log:
    on-off: false

xxl:
  job:
    adminAddress: ${XXLJOB_ADMINADDRESS}
    executorName: trinax-basic-service
    ip:
    port: 12000
    accessToken:
    logPath: /apps/logs/${spring.application.name}/xxl-job
    logRetentionDays: -1

rocketmq:
  name-server: ${ROCKETMQ_HOST}
  producer:
    group: basic
  consumer:
    group:
    topic:

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志

application:
  website: https://trinax-uat.trinasolar.com/choose