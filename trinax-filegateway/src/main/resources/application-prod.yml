spring:
  cloud:
    kubernetes:
      enabled: false
    service-registry:
      auto-registration:
        enabled: true
    gateway:
      routes:
        - id: filebff
          uri: lb://trinax-filebff-service
          predicates:
            - Path=/filebff/**
          filters:
            - StripPrefix=1

request:
  limit:
    enabled: true
    max-connections: 2000
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeout-in-milliseconds: 20000
ribbon:
  ConnectTimeout: 60000
  ReadTimeout: 60000
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 1

#日志上报环境地址
logging:
  collector:
    address: ${LOGGING_COLLECTOR_ADDRESS}