package com.trinasolar.trinax.partnerbff.controller.delivery;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.trinasolar.trinax.auth.core.details.AuthUserDetails;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.excel.ExcelDownloadUtil;
import com.trinasolar.trinax.delivery.api.ExternalDeliverFeign;
import com.trinasolar.trinax.delivery.api.SharedDeliverFeign;
import com.trinasolar.trinax.delivery.dto.input.delivery.cancel.CancelDeliverReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.delete.DeleteDeliverReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverListReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.save.SaveDeliveryReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.submit.SubmitPartnerListReqDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverListRespDTO;
import com.trinasolar.trinax.log.behavior.support.BehaviorLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
//import com.trinasolar.cloud.lapetus.logback.appender.aspect.IgnoreLogging;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverListExcelDTO;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

@RestController
@Slf4j
@RequiredArgsConstructor
@Tag(name = "发货接口")
public class DeliveryController {
    final ExternalDeliverFeign externalDeliverFeign;
    final SharedDeliverFeign sharedDeliverFeign;

    @BehaviorLog(ignore = false,ignoreReq = false,bizNo = "#result.data")
    @Operation(summary = "detail 详情页面提交发货申请")
    @PostMapping("/delivery/submit/detail")
    public Result<Object> detailSubmitDelivery(@Validated @RequestBody SaveDeliveryReqDTO req) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        req.setUserId(String.valueOf(authUser.getUserIdStr()));
        req.setUserName(authUser.getName());

        return externalDeliverFeign.submitPartnerDetailDelivery(req);
    }


    @BehaviorLog(ignore = false,ignoreReq = false,bizNo = "#req.deliveryNo")
    @Operation(summary = "list 列表页面提交发货申请")
    @PostMapping("/delivery/submit/list")
    public Result<Object> listSubmitDelivery(@Validated @RequestBody SubmitPartnerListReqDTO req) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        req.setUserId(String.valueOf(authUser.getUserIdStr()));
        // 触发编译
        return externalDeliverFeign.submitPartnerListDelivery(req);
    }

    @Operation(summary = "PC Customer查询发货申请列表 list")
    @PostMapping("/pc-customer/delivery/list")
//    @IgnoreLogging
    Result<PageResponse<DeliverListRespDTO>> pcCustomerListDeliver(@RequestBody PageRequest<DeliverListReqDTO> req) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        req.getQuery().setUserId(String.valueOf(authUser.getUserIdStr()));
        req.getQuery().setUserType(authUser.getUserType());
        return sharedDeliverFeign.listDeliver(req);
    }

    @BehaviorLog(ignore = false,ignoreReq = false,bizNo = "#result.data")
    @Operation(summary = "PC Customer 保存发货申请")
    @PostMapping("/pc-customer/delivery/save")
    public Result<Object> pcCustomerSaveDeliver(@Validated @RequestBody SaveDeliveryReqDTO req) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        req.setUserId(String.valueOf(authUser.getUserIdStr()));
        return sharedDeliverFeign.saveDeliver(req);
    }

    @BehaviorLog(ignore = false,ignoreReq = false,bizNo = "#req.deliveryNo")
    @Operation(summary = "PC Customer list 列表页面提交发货申请")
    @PostMapping("/pc-customer/delivery/submit/list")
    public Result<Object> pcCustomerListSubmitDelivery(@Validated @RequestBody SubmitPartnerListReqDTO req) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        req.setUserId(String.valueOf(authUser.getUserIdStr()));
        // 触发编译
        return externalDeliverFeign.submitPartnerListDelivery(req);
    }

    @BehaviorLog(ignore = false,ignoreReq = false,bizNo = "#result.data")
    @Operation(summary = "PC Customer detail 详情页面提交发货申请")
    @PostMapping("/pc-customer/delivery/submit/detail")
    public Result<Object> pcCustomerDetailSubmitDelivery(@Validated @RequestBody SaveDeliveryReqDTO req) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        req.setUserId(String.valueOf(authUser.getUserIdStr()));
        req.setUserName(authUser.getName());

        return externalDeliverFeign.submitPartnerDetailDelivery(req);
    }

    @BehaviorLog(ignore = false,ignoreReq = false,bizNo = "#req.deliverNo")
    @Operation(summary = "PC Customer删除发货申请")
    @DeleteMapping("/pc-customer/delivery/delete")
    Result<String> pcCustomerDeleteDeliver(@Validated @RequestBody DeleteDeliverReqDTO req) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        req.setUserId(String.valueOf(authUser.getUserIdStr()));
        return sharedDeliverFeign.deleteDeliver(req);
    }

    @BehaviorLog(ignore = false,ignoreReq = false,bizNo = "#req.deliverNo")
    @Operation(summary = "PC Customer取消发货申请")
    @PostMapping("/pc-customer/delivery/cancel")
    Result<Object> pcCustomerCancelDeliver(@Validated @RequestBody CancelDeliverReqDTO req) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        req.setUserId(String.valueOf(authUser.getUserIdStr()));
        return sharedDeliverFeign.cancelDeliver(req);
    }

    @Operation(summary = "PC Customer查询发货申请列表导出")
    @PostMapping("/pc-customer/delivery/exportDeliver")
//    @IgnoreLogging
    public void pcCustomerExportDeliver(@RequestBody DeliverListReqDTO req, HttpServletResponse response) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        req.setUserId(String.valueOf(authUser.getUserIdStr()));
        req.setUserType(authUser.getUserType());

        Result<List<DeliverListRespDTO>> result = sharedDeliverFeign.externalDeliverList(req);
        List<DeliverListExcelDTO> excelResult = BeanUtil.copyToList(result.getData(),DeliverListExcelDTO.class);
        String filename = "发货申请" + DateFormatUtils.format(new Date(), "yyyyMMdd_HH_mm_ss");
        ExcelDownloadUtil.download(response, DeliverListExcelDTO.class, excelResult, filename, ExcelTypeEnum.XLSX);
    }
}
