package com.trinasolar.trinax.partnerbff.controller.partner;

import com.trinasolar.trinax.auth.core.details.AuthUserDetails;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.partner.api.EnterpriseHeaderFeign;
import com.trinasolar.trinax.partner.dto.input.EnterpriseHeaderQueryReqDTO;
import com.trinasolar.trinax.partner.dto.input.EnterpriseHeaderSaveOrUpdateReqDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseHeaderQueryResDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

@RestController
@RequiredArgsConstructor
@Slf4j
@Tag(name = "企业抬头接口")
@RequestMapping("/enterprise-header")
public class EnterpriseHeaderController {
    final EnterpriseHeaderFeign enterpriseHeaderFeign;

    @Operation(summary = "外部用户查询企业抬头列表 ")
    @PostMapping("/authPassedEnterpriseHeaderByDealer")
    Result<List<EnterpriseHeaderQueryResDTO>> authPassedEnterpriseHeaderByDealer(@RequestBody EnterpriseHeaderQueryReqDTO reqDTO) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        reqDTO.setDealerUserId(authUser.getUserIdStr());
        reqDTO.setCurrentUserId(authUser.getUserIdStr());
        reqDTO.setCurrentUserName(authUser.getName());
        reqDTO.setCurrentUserPhone(authUser.getMobile());
        return enterpriseHeaderFeign.authPassedEnterpriseHeaderByDealer(reqDTO);
    }

    @Operation(summary = "外部用户新增或修改企业抬头 ")
    @PostMapping("/saveOrUpdateEnterpriseHeader")
    Result<String> saveOrUpdateEnterpriseAddress(@RequestBody EnterpriseHeaderSaveOrUpdateReqDTO reqDTO) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        reqDTO.setCurrentUserId(authUser.getUserIdStr());
        reqDTO.setCurrentUserName(authUser.getName());
        reqDTO.setCurrentUserPhone(authUser.getMobile());
        return enterpriseHeaderFeign.saveOrUpdateEnterpriseAddress(reqDTO);
    }

    @Operation(summary = "删除企业抬头")
    @DeleteMapping({"/removeById"})
    Result<String> removeById(@RequestParam("id") @NotBlank Long id) {
        return enterpriseHeaderFeign.removeById(id);
    }

    @Operation(summary = "通过headerId获取企业抬头详情")
    @GetMapping({"/getDetailByHeaderId"})
    Result<EnterpriseHeaderQueryResDTO> getDetailById(@RequestParam("headerId") @NotBlank String headerId) {
        return enterpriseHeaderFeign.getDetailByHeaderId(headerId);
    }

}
