# Tomcat
server:
  port: 80

#日志组件
trinasolar:
  team: dtt-team
  project:
    name: trinax-partnerbff-service

springdoc:
  api-docs:
    enabled: true
    path: v3/api-docs
knife4j:
  enable: true

# Spring
spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  application:
    # 应用名称
    name: trinax-partnerbff-service
    version: 1.0
  profiles:
    # 环境配置
    active: ${ACTIVE_PROFILE:local}
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
#      username: trinasolarx
#      password: ${NACOS_PASSWORD}
      discovery:
        # 使用德勤分配的 namespace 代替，如没有则注释掉这一样
        namespace: trinax
        # 服务注册地址
        server-addr: ${NACOS:localhost}:8848
#        server-addr: mynacos:8848
      config:
       # 使用德勤分配的 namespace 代替，如没有则注释掉这一样
        namespace: trinax
         # 使用德勤分配的 group 代替，如没有则注释掉这一样
#        group: trinax-public
        # 配置中心地址
        server-addr: ${NACOS:localhost}:8848
#        server-addr: mynacos:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-dataids: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: mysentinel:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: ${NACOS:localhost}:8848
#            server-addr: mynacos:8848
            dataId: sentinel-masterdata-service
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: flow
#  mail:
#    host: smtp.163.com
#    username: <EMAIL>
#    password: SPLTIJVCNPCCCP
#    default-encoding: UTF-8
#    protocol: smtp
#    test-connection: true
#    properties:
#      mail:
#        smtp:
#          auth: true
#          starttls:
#            enable: true
#            required: true
#          socketFactory:
#            class: javax.net.ssl.SSLSocketFactory
#            port: 465
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  global-config:
    db-config:
      id-type: none

management:
  health.defaults.enabled: false

  #SPLTIJVCNPCCCPFU