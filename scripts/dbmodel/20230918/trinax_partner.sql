DROP TABLE IF EXISTS partner_contact_person;
CREATE TABLE partner_contact_person(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT  COMMENT '' ,
    `full_name` VARCHAR(64)    COMMENT '联系人名字' ,
    `nick_name` VARCHAR(64)    COMMENT '联系人昵称' ,
    `mobile` VARCHAR(16)    COMMENT '手机号' ,
    `phone` VARCHAR(16)    COMMENT '固定电话' ,
    `email` VARCHAR(128)    COMMENT '邮件地址' ,
    `function` VARCHAR(128)    COMMENT '功能角色' ,
    `is_active` TINYINT   DEFAULT 1 COMMENT '是否有效' ,
    `is_deleted` TINYINT   DEFAULT 0 COMMENT '是否删除' ,
    `tenant_id` VARCHAR(32)   DEFAULT '0' COMMENT '租户号' ,
    `revision` INT   DEFAULT 0 COMMENT '乐观锁' ,
    `created_by` BIGINT UNSIGNED   DEFAULT 0 COMMENT '创建人' ,
    `created_name` VARCHAR(128)    COMMENT '创建人姓名' ,
    `created_time` DATETIME   DEFAULT now() COMMENT '创建时间' ,
    `updated_by` BIGINT UNSIGNED   DEFAULT 0 COMMENT '更新人' ,
    `updated_name` VARCHAR(128)    COMMENT '更新人姓名' ,
    `updated_time` DATETIME   DEFAULT now() COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '联系人';

DROP TABLE IF EXISTS partner_ent;
CREATE TABLE partner_ent(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT  COMMENT '' ,
    `tax_number` VARCHAR(256)    COMMENT '税号' ,
    `staff_num_range` VARCHAR(200)    COMMENT '人员规模' ,
    `from_time` TIMESTAMP    COMMENT '经营开始时间' ,
    `type` TINYINT   DEFAULT 1 COMMENT '法⼈类型，1 ⼈ 2 公司' ,
    `bond_name` VARCHAR(128) NOT NULL   COMMENT '股票名;股票名称' ,
    `tianyan_id` BIGINT   DEFAULT 0 COMMENT '天眼查自增id' ,
    `is_micro_ent` TINYINT    COMMENT '是否是⼩微企业 0不是 1是' ,
    `reg_number` VARCHAR(32)    COMMENT '注册号' ,
    `percentile_score` DECIMAL(10)   DEFAULT 0 COMMENT '企业评分' ,
    `reg_capital` VARCHAR(50)    COMMENT '注册资本' ,
    `name` VARCHAR(256) NOT NULL   COMMENT '企业名称' ,
    `reg_institue` VARCHAR(256)    COMMENT '登记机关' ,
    `reg_location` VARCHAR(256)    COMMENT '注册地址' ,
    `industry` VARCHAR(256)    COMMENT '⾏业' ,
    `approved_time` TIMESTAMP    COMMENT '核准时间' ,
    `social_staff_num` INT   DEFAULT 0 COMMENT '参保⼈数' ,
    `tags` VARCHAR(256)    COMMENT '企业标签' ,
    `business_scope` VARCHAR(4091)    COMMENT '经营范围' ,
    `property3` VARCHAR(128)    COMMENT '英⽂名' ,
    `alias` VARCHAR(128)    COMMENT '简称' ,
    `org_number` VARCHAR(64)    COMMENT '组织机构代码' ,
    `reg_status` VARCHAR(32)    COMMENT '企业状态' ,
    `establish_time` TIMESTAMP    COMMENT '创建时间' ,
    `update_times` TIMESTAMP    COMMENT '更新时间' ,
    `bond_type` VARCHAR(32)    COMMENT '股票类型' ,
    `legal_person_name` VARCHAR(128)    COMMENT '法人' ,
    `to_time` TIMESTAMP    COMMENT '经营结束时间' ,
    `company_org_type` VARCHAR(128)    COMMENT '企业类型' ,
    `comp_form` VARCHAR(64)    COMMENT '组成形式，1-个人经营、2-家庭经营' ,
    `base` VARCHAR(32)    COMMENT '省份简称' ,
    `credit_code` VARCHAR(32)    COMMENT '统一社会信用代码' ,
    `history_names` VARCHAR(256)    COMMENT '曾用名' ,
    `history_name_list` TEXT    COMMENT '曾用名' ,
    `bond_num` VARCHAR(32)    COMMENT '股票号' ,
    `reg_capital_currency` VARCHAR(32)    COMMENT '注册资本币种 人民币 美元 欧元 等' ,
    `actual_captital_currency` VARCHAR(32)    COMMENT '实收注册资本币种 人民币 美元 欧元 等' ,
    `revoke_date` TIMESTAMP    COMMENT '吊销日期' ,
    `revoke_reason` VARCHAR(256)    COMMENT '吊销原因' ,
    `cancel_date` TIMESTAMP    COMMENT '注销日期' ,
    `cancel_reason` VARCHAR(128)    COMMENT '注销原因' ,
    `city` VARCHAR(32)    COMMENT '市' ,
    `district` VARCHAR(32)    COMMENT '区' ,
    `used_bond_name` VARCHAR(512)    COMMENT '' ,
    `actual_capital` VARCHAR(64)    COMMENT '' ,
    `organization_nature` VARCHAR(64) NOT NULL   COMMENT '企业性质;Company,Government,Other Social Organization,Person' ,
    `business_line` VARCHAR(64) NOT NULL   COMMENT '业务线 多选;Module,Tracker' ,
    `account_role` VARCHAR(64) NOT NULL   COMMENT 'Project Developer,Distributor,Installer,EPC,O&M,IPP (Utility),OEM Customer,Site,Owner,Investor' ,
    `region` VARCHAR(64) NOT NULL   COMMENT '' ,
    `sub_region` VARCHAR(64) NOT NULL   COMMENT '' ,
    `area` VARCHAR(32) NOT NULL   COMMENT '' ,
    `account_currency` VARCHAR(16) NOT NULL   COMMENT '' ,
    `tax_vat_code` VARCHAR(64) NOT NULL   COMMENT '同步SF 税码Id' ,
    `main_ou` VARCHAR(64) NOT NULL   COMMENT '同步SF 账套Id' ,
    `billing_street` VARCHAR(256) NOT NULL   COMMENT '' ,
    `billing_city` VARCHAR(32) NOT NULL   COMMENT '' ,
    `billing_postalcode` VARCHAR(16) NOT NULL   COMMENT '' ,
    `is_platform` VARCHAR(16) NOT NULL  DEFAULT 'N' COMMENT '是否平台方;p区分是德勤平台方还是其他企业' ,
    `tenant_id` VARCHAR(32)   DEFAULT '0' COMMENT '租户号' ,
    `revision` INT   DEFAULT 0 COMMENT '乐观锁' ,
    `created_by` BIGINT UNSIGNED   DEFAULT 0 COMMENT '创建人' ,
    `created_name` VARCHAR(128)    COMMENT '创建人姓名' ,
    `created_time` DATETIME   DEFAULT now() COMMENT '创建时间' ,
    `updated_by` BIGINT UNSIGNED   DEFAULT 0 COMMENT '更新人' ,
    `updated_name` VARCHAR(128)    COMMENT '更新人姓名' ,
    `updated_time` DATETIME   DEFAULT now() COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '合作伙伴企业信息';

DROP TABLE IF EXISTS partner_ent_contact_person_rela;
CREATE TABLE partner_ent_contact_person_rela(
    `id` BIGINT UNSIGNED NOT NULL   COMMENT '' ,
    `ent_id` BIGINT UNSIGNED   DEFAULT 0 COMMENT '企业ID' ,
    `person_id` BIGINT UNSIGNED   DEFAULT 0 COMMENT '联系人id' ,
    `is_deleted` TINYINT   DEFAULT 0 COMMENT '是否删除' ,
    `tenant_id` VARCHAR(32)   DEFAULT '0' COMMENT '租户号' ,
    `revision` INT   DEFAULT 0 COMMENT '乐观锁' ,
    `created_by` BIGINT UNSIGNED   DEFAULT 0 COMMENT '创建人' ,
    `created_name` VARCHAR(128)    COMMENT '创建人姓名' ,
    `created_time` DATETIME   DEFAULT now() COMMENT '创建时间' ,
    `updated_by` BIGINT UNSIGNED   DEFAULT 0 COMMENT '更新人' ,
    `updated_name` VARCHAR(128)    COMMENT '更新人姓名' ,
    `updated_time` DATETIME   DEFAULT now() COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '企业联系人关系';

DROP TABLE IF EXISTS partner_user_ent_rela;
CREATE TABLE partner_user_ent_rela(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT  COMMENT '' ,
    `user_id` BIGINT UNSIGNED   DEFAULT 0 COMMENT '用户id' ,
    `ent_id` BIGINT UNSIGNED   DEFAULT 0 COMMENT '企业id' ,
    `user_ent_rela_type` VARCHAR(32)    COMMENT '用户与企业的关系类型;经销商用户绑定企业
销售或合作伙伴用户跟进企业
' ,
    `enabled` TINYINT   DEFAULT 1 COMMENT '是否启用' ,
    `tenant_id` VARCHAR(32)   DEFAULT '0' COMMENT '租户号' ,
    `revision` INT   DEFAULT 0 COMMENT '乐观锁' ,
    `created_by` BIGINT UNSIGNED   DEFAULT 0 COMMENT '创建人' ,
    `created_name` VARCHAR(128)    COMMENT '创建人姓名' ,
    `created_time` DATETIME   DEFAULT now() COMMENT '创建时间' ,
    `updated_by` BIGINT UNSIGNED   DEFAULT 0 COMMENT '更新人' ,
    `updated_name` VARCHAR(128)    COMMENT '更新人姓名' ,
    `updated_time` DATETIME   DEFAULT now() COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '用户企业绑定关系';

DROP TABLE IF EXISTS partner_biz_follower_rela;
CREATE TABLE partner_biz_follower_rela(
    `id` BIGINT UNSIGNED NOT NULL   COMMENT '自增id;可能需要按业务对象拆成多张表' ,
    `biz_slip_number` VARCHAR(64) NOT NULL   COMMENT '业务顺序号外键' ,
    `followed_biz_id` BIGINT UNSIGNED   DEFAULT 0 COMMENT '被跟进的业务对象id' ,
    `followed_biz_type` VARCHAR(64)    COMMENT '被跟进的对象类型;跟进企业，跟进需求，跟进意向单，跟进合同，跟进发货单;' ,
    `follower_user_id` BIGINT UNSIGNED   DEFAULT 0 COMMENT '跟进人id' ,
    `follower_username` VARCHAR(128)   DEFAULT '' COMMENT '跟进人名称' ,
    `follower_user_mobile` VARCHAR(16)   DEFAULT '' COMMENT '跟进人手机号' ,
    `follower_user_email` VARCHAR(128)   DEFAULT '' COMMENT '跟进人email' ,
    `follower_user_staffcode` VARCHAR(32)   DEFAULT '' COMMENT '跟进人工号' ,
    `follower_type` VARCHAR(64) NOT NULL   COMMENT '跟进者类型;区域负责人（自动分派），销售人员，销售组织，合作伙伴
Direct Sales Team
Distribution Sales Team
Distribution Key Account
销售团队的组织结构是怎样的' ,
    `assigned_by_user_id` BIGINT UNSIGNED   DEFAULT 0 COMMENT '指派人id;可能是按区域mapping自动指派，也可能是由大区经理指派' ,
    `assigned_by_username` VARCHAR(128)   DEFAULT '' COMMENT '指派人id名称' ,
    `assigned_by_user_mobile` VARCHAR(16)   DEFAULT '' COMMENT '指派人id手机号' ,
    `assigned_by_user_email` VARCHAR(128)   DEFAULT '' COMMENT '指派人idemail' ,
    `assigned_by_user_staffcode` VARCHAR(32)   DEFAULT '' COMMENT '指派人id工号' ,
    `permission_json` JSON    COMMENT '权限配置;{"read":true,"edit":true,"delete":true}' ,
    `is_deleted` VARCHAR(255) NOT NULL  DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除' ,
    `tenant_id` VARCHAR(32)   DEFAULT '0' COMMENT '租户号' ,
    `revision` VARCHAR(255)   DEFAULT 0 COMMENT '乐观锁' ,
    `created_by` VARCHAR(255)   DEFAULT 0 COMMENT '创建人' ,
    `created_name` VARCHAR(255)   DEFAULT '' COMMENT '创建人姓名' ,
    `created_time` DATETIME   DEFAULT now() COMMENT '创建时间' ,
    `updated_by` VARCHAR(255)   DEFAULT 0 COMMENT '更新人' ,
    `updated_name` VARCHAR(255)   DEFAULT '' COMMENT '更新人姓名' ,
    `updated_time` DATETIME   DEFAULT now() COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '业务跟进关系';

DROP TABLE IF EXISTS partner_make_biz_request;
CREATE TABLE partner_make_biz_request(
    `id` BIGINT UNSIGNED NOT NULL   COMMENT '' ,
    `ent_id` BIGINT UNSIGNED NOT NULL   COMMENT '' ,
    `tax_number` VARCHAR(128)   DEFAULT '' COMMENT '企业税号' ,
    `ent_name` VARCHAR(255)   DEFAULT '' COMMENT '企业名字' ,
    `ent_json` JSON    COMMENT '建商请求包含的企业信息' ,
    `status` VARCHAR(64)   DEFAULT '' COMMENT '' ,
    `permission_json` JSON    COMMENT '权限配置;{"read":true,"edit":true,"delete":true}' ,
    `is_deleted` INT NOT NULL  DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除' ,
    `tenant_id` VARCHAR(32)   DEFAULT '0' COMMENT '租户号' ,
    `revision` INT   DEFAULT 0 COMMENT '乐观锁' ,
    `created_by` BIGINT UNSIGNED   DEFAULT 0 COMMENT '创建人' ,
    `created_name` VARCHAR(128)   DEFAULT '' COMMENT '创建人姓名' ,
    `created_time` DATETIME   DEFAULT now() COMMENT '创建时间' ,
    `updated_by` BIGINT UNSIGNED   DEFAULT 0 COMMENT '更新人' ,
    `updated_name` VARCHAR(128)   DEFAULT '' COMMENT '更新人姓名' ,
    `updated_time` DATETIME   DEFAULT now() COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '建商申请';

