DROP TABLE IF EXISTS delivery_address;
CREATE TABLE delivery_address(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT  COMMENT '' ,
    `ent_id` BIGINT UNSIGNED NOT NULL   COMMENT '企业id' ,
    `address_code` VARCHAR(64)    COMMENT '地址编码（来自MDM)' ,
    `is_default` INT NOT NULL  DEFAULT 0 COMMENT '是否默认;1:默认，0：非默认' ,
    `seq` INT NOT NULL  DEFAULT 0 COMMENT '排序号' ,
    `country` VARCHAR(16)   DEFAULT 'China' COMMENT '国家' ,
    `province_code` VARCHAR(16) NOT NULL   COMMENT '省编码;省编码' ,
    `province` VARCHAR(128)    COMMENT '省名称;省名称' ,
    `city_code` VARCHAR(16) NOT NULL   COMMENT '城市编码;城市编码' ,
    `city` VARCHAR(128)    COMMENT '城市名称' ,
    `district_code` VARCHAR(16) NOT NULL   COMMENT '区编码' ,
    `district` VARCHAR(128)    COMMENT '区名称' ,
    `street_code` VARCHAR(16) NOT NULL   COMMENT '街道编码' ,
    `street` VARCHAR(256)    COMMENT '街道名称' ,
    `zip_code` VARCHAR(16)    COMMENT '邮编' ,
    `detail_address` VARCHAR(512) NOT NULL   COMMENT '详细地址;详细地址，具体到门牌号' ,
    `is_deleted` TINYINT    COMMENT '是否已删除' ,
    `tenant_id` VARCHAR(32)   DEFAULT '0' COMMENT '租户号' ,
    `revision` INT   DEFAULT 0 COMMENT '乐观锁' ,
    `created_by` BIGINT UNSIGNED   DEFAULT 0 COMMENT '创建人' ,
    `created_name` VARCHAR(128)    COMMENT '创建人姓名' ,
    `created_time` DATETIME   DEFAULT now() COMMENT '创建时间' ,
    `updated_by` BIGINT UNSIGNED   DEFAULT 0 COMMENT '更新人' ,
    `updated_name` VARCHAR(128)    COMMENT '更新人姓名' ,
    `updated_time` DATETIME   DEFAULT now() COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '';

DROP TABLE IF EXISTS invoice_request;
CREATE TABLE invoice_request(
    `id` BIGINT UNSIGNED NOT NULL   COMMENT '自增id' ,
    `slip_number` VARCHAR(64) NOT NULL   COMMENT '业务顺序号' ,
    `requirement_id` BIGINT UNSIGNED    COMMENT '需求id' ,
    `intentorder_id` BIGINT UNSIGNED   DEFAULT 0 COMMENT '意向单id' ,
    `salescontract_id` BIGINT UNSIGNED    COMMENT '合同id' ,
    `sf_contract_code` VARCHAR(64)   DEFAULT '' COMMENT 'SF合同号' ,
    `bpm_contract_code` VARCHAR(64)   DEFAULT '' COMMENT 'BPM返回合同号' ,
    `seller_ent_id` BIGINT UNSIGNED    COMMENT '销售方企业id' ,
    `seller_tax_number` VARCHAR(64)    COMMENT '销售方企业税号' ,
    `seller_ent_name` VARCHAR(128)    COMMENT '销售方企业名称' ,
    `buyer_ent_id` BIGINT UNSIGNED NOT NULL   COMMENT '买方企业id' ,
    `buyer_tax_number` VARCHAR(64) NOT NULL   COMMENT '买方企业税号' ,
    `buyer_ent_name` VARCHAR(128) NOT NULL   COMMENT '买方企业名称' ,
    `buyer_contact_person_id` BIGINT UNSIGNED NOT NULL   COMMENT '联系人id' ,
    `buyer_contact_person_name` VARCHAR(128) NOT NULL   COMMENT '联系人名称' ,
    `buyer_contact_person_mobile` VARCHAR(16)    COMMENT '联系人手机号' ,
    `buyer_contact_person_email` VARCHAR(128)    COMMENT '联系人email' ,
    `placeticket_user_id` BIGINT UNSIGNED NOT NULL   COMMENT '单据创建人id;下单的用户id 可能是客户用户或内部用户' ,
    `placeticket_username` VARCHAR(128)   DEFAULT '' COMMENT '单据创建人名称' ,
    `placeticket_user_mobile` VARCHAR(16)   DEFAULT '' COMMENT '单据创建人手机号' ,
    `placeticket_user_email` VARCHAR(128)   DEFAULT '' COMMENT '单据创建人email' ,
    `placeticket_user_staffcode` VARCHAR(32)   DEFAULT '' COMMENT '单据创建人工号' ,
    PRIMARY KEY (id)
)  COMMENT = '开票申请';

DROP TABLE IF EXISTS logistic_order;
CREATE TABLE logistic_order(
    `id` BIGINT UNSIGNED NOT NULL   COMMENT '自增id' ,
    PRIMARY KEY (id)
)  COMMENT = '物流订单';

DROP TABLE IF EXISTS delivery_ent_address;
CREATE TABLE delivery_ent_address(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT  COMMENT '自增id' ,
    `ent_id` BIGINT UNSIGNED NOT NULL   COMMENT '企业id' ,
    `address_id` BIGINT UNSIGNED NOT NULL   COMMENT '地址id外键' ,
    `address_code` VARCHAR(64)    COMMENT '地址编码（来自MDM)' ,
    `is_default` INT NOT NULL  DEFAULT 0 COMMENT '是否默认;1:默认，0：非默认' ,
    `seq` INT NOT NULL  DEFAULT 0 COMMENT '排序号' ,
    `is_deleted` INT NOT NULL  DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除' ,
    `created_by` BIGINT UNSIGNED   DEFAULT 0 COMMENT '创建人' ,
    `tenant_id` VARCHAR(32)   DEFAULT '0' COMMENT '租户号' ,
    `revision` INT   DEFAULT 0 COMMENT '乐观锁' ,
    `created_name` VARCHAR(128)   DEFAULT '' COMMENT '创建人姓名' ,
    `created_time` DATETIME   DEFAULT now() COMMENT '创建时间' ,
    `updated_by` BIGINT UNSIGNED   DEFAULT 0 COMMENT '更新人' ,
    `updated_name` VARCHAR(128)   DEFAULT '' COMMENT '更新人姓名' ,
    `updated_time` DATETIME   DEFAULT now() COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '企业地址关系';

DROP TABLE IF EXISTS delivery_request;
CREATE TABLE delivery_request(
    `id` BIGINT UNSIGNED NOT NULL   COMMENT '自增id' ,
    `slip_number` VARCHAR(64) NOT NULL   COMMENT '业务顺序号' ,
    `requirement_id` BIGINT UNSIGNED    COMMENT '需求id' ,
    `intentorder_id` BIGINT UNSIGNED   DEFAULT 0 COMMENT '意向单id' ,
    `salescontract_id` BIGINT UNSIGNED    COMMENT '合同id' ,
    `sf_contract_code` VARCHAR(64)   DEFAULT '' COMMENT 'SF合同号' ,
    `bpm_contract_code` VARCHAR(64)   DEFAULT '' COMMENT 'BPM返回合同号' ,
    `ent_address_rela_id` BIGINT UNSIGNED NOT NULL  DEFAULT 0 COMMENT '企业收货地址id' ,
    `request_delivery_date` DATETIME NOT NULL   COMMENT '预计发货日期' ,
    `sales_region` VARCHAR(64) NOT NULL  DEFAULT 'CHN' COMMENT '销售所在区域;待确认怎么取值' ,
    `owning_bu` VARCHAR(64) NOT NULL  DEFAULT 'Non-Utility' COMMENT '所属BU;待确认怎么取值' ,
    `owning_department` VARCHAR(64) NOT NULL  DEFAULT 'Distribution' COMMENT '所属部门;待确认怎么取值' ,
    `transportation_type` VARCHAR(64) NOT NULL  DEFAULT 'land transportation' COMMENT '运输方式;陆运Land transportation，自提self pick-up' ,
    `seller_ent_id` BIGINT UNSIGNED    COMMENT '销售方企业id' ,
    `seller_tax_number` VARCHAR(64)    COMMENT '销售方企业税号' ,
    `seller_ent_name` VARCHAR(128)    COMMENT '销售方企业名称' ,
    `buyer_ent_id` BIGINT UNSIGNED NOT NULL   COMMENT '买方企业id' ,
    `buyer_tax_number` VARCHAR(64) NOT NULL   COMMENT '买方企业税号' ,
    `buyer_ent_name` VARCHAR(128) NOT NULL   COMMENT '买方企业名称' ,
    `buyer_contact_person_id` BIGINT UNSIGNED NOT NULL   COMMENT '联系人id' ,
    `buyer_contact_person_name` VARCHAR(128) NOT NULL   COMMENT '联系人名称' ,
    `buyer_contact_person_mobile` VARCHAR(16)    COMMENT '联系人手机号' ,
    `buyer_contact_person_email` VARCHAR(128)    COMMENT '联系人email' ,
    `placeticket_user_id` BIGINT UNSIGNED NOT NULL   COMMENT '单据创建人id;下单的用户id 可能是客户用户或内部用户' ,
    `placeticket_username` VARCHAR(128)   DEFAULT '' COMMENT '单据创建人名称' ,
    `placeticket_user_mobile` VARCHAR(16)   DEFAULT '' COMMENT '单据创建人手机号' ,
    `placeticket_user_email` VARCHAR(128)   DEFAULT '' COMMENT '单据创建人email' ,
    `placeticket_user_staffcode` VARCHAR(32)   DEFAULT '' COMMENT '单据创建人工号' ,
    PRIMARY KEY (id)
)  COMMENT = '发货申请';

DROP TABLE IF EXISTS delivery_biz_follower_rela;
CREATE TABLE delivery_biz_follower_rela(
    `id` BIGINT UNSIGNED NOT NULL   COMMENT '自增id;可能需要按业务对象拆成多张表' ,
    `biz_slip_number` VARCHAR(64) NOT NULL   COMMENT '业务顺序号外键' ,
    `followed_biz_id` BIGINT UNSIGNED   DEFAULT 0 COMMENT '被跟进的业务对象id' ,
    `followed_biz_type` VARCHAR(64)    COMMENT '被跟进的对象类型;跟进企业，跟进需求，跟进意向单，跟进合同，跟进发货单;' ,
    `follower_user_id` BIGINT UNSIGNED   DEFAULT 0 COMMENT '跟进人id' ,
    `follower_username` VARCHAR(128)   DEFAULT '' COMMENT '跟进人名称' ,
    `follower_user_mobile` VARCHAR(16)   DEFAULT '' COMMENT '跟进人手机号' ,
    `follower_user_email` VARCHAR(128)   DEFAULT '' COMMENT '跟进人email' ,
    `follower_user_staffcode` VARCHAR(32)   DEFAULT '' COMMENT '跟进人工号' ,
    `follower_type` VARCHAR(64) NOT NULL   COMMENT '跟进者类型;区域负责人（自动分派），销售人员，销售组织，合作伙伴
Direct Sales Team
Distribution Sales Team
Distribution Key Account
销售团队的组织结构是怎样的' ,
    `assigned_by_user_id` BIGINT UNSIGNED   DEFAULT 0 COMMENT '指派人id;可能是按区域mapping自动指派，也可能是由大区经理指派' ,
    `assigned_by_username` VARCHAR(128)   DEFAULT '' COMMENT '指派人id名称' ,
    `assigned_by_user_mobile` VARCHAR(16)   DEFAULT '' COMMENT '指派人id手机号' ,
    `assigned_by_user_email` VARCHAR(128)   DEFAULT '' COMMENT '指派人idemail' ,
    `assigned_by_user_staffcode` VARCHAR(32)   DEFAULT '' COMMENT '指派人id工号' ,
    `permission_json` JSON    COMMENT '权限配置;{"read":true,"edit":true,"delete":true}' ,
    `is_deleted` VARCHAR(255) NOT NULL  DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除' ,
    `tenant_id` VARCHAR(32)   DEFAULT '0' COMMENT '租户号' ,
    `revision` VARCHAR(255)   DEFAULT 0 COMMENT '乐观锁' ,
    `created_by` VARCHAR(255)   DEFAULT 0 COMMENT '创建人' ,
    `created_name` VARCHAR(255)   DEFAULT '' COMMENT '创建人姓名' ,
    `created_time` DATETIME   DEFAULT now() COMMENT '创建时间' ,
    `updated_by` VARCHAR(255)   DEFAULT 0 COMMENT '更新人' ,
    `updated_name` VARCHAR(255)   DEFAULT '' COMMENT '更新人姓名' ,
    `updated_time` DATETIME   DEFAULT now() COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '业务跟进关系';

