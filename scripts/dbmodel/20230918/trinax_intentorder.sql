DROP TABLE IF EXISTS product_detail_pbi;
CREATE TABLE product_detail_pbi(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT  COMMENT '自增主键' ,


    `raw_data` VARCHAR(16000) NOT NULL   COMMENT '产品原始信息' ,
    `sync_status` VARCHAR(32) NOT NULL   COMMENT '同步状态（INIT、COMPLETED、CANCELED）' ,
    `sync_succ_time` DATETIME    COMMENT '同步成功时间' ,
    `is_deleted` INT NOT NULL  DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除' ,
    `created_by` BIGINT UNSIGNED    COMMENT '创建人' ,
    `created_name` VARCHAR(128)    COMMENT '创建人姓名' ,
    `created_time` DATETIME NOT NULL   COMMENT '创建时间' ,
    `updated_by` BIGINT UNSIGNED    COMMENT '更新人' ,
    `updated_name` VARCHAR(128)    COMMENT '更新人姓名' ,
    `updated_time` DATETIME NOT NULL   COMMENT '更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '产品信息（PBI）';

意向单：
意向单单号
SF商机编号
客户方联系人
客户ID
配送方式
是否代客下单
期望交货时间
产品需求量（W）
合计金额（参考价）
合计金额（销售价）
销售ID
提货类型（自提还是非自提）
期望交货时间
应用场景（户用或者商用）
交货区域（省）
交货区域（城市）
意向单备注
行业属性（是否需要保存两级）
状态
业务机会渠道分类
预计交货时间-确认环节更新或代客下单直接提交
销售账套-确认环节更新或代客下单直接提交


意向单行：
意向单单号
意向单行行号
SF商机行编号
产品ID
主栅数 产品的这个值是否会变，变化了对于这个数是否需要自动更新？
功率
购买方式
片数量
需求量（W）
产品需求量（W）-计算得出
安装方式（默认竖装，用户不可见、不可改）
正极电缆长度（M）
负极电缆长度（M）
端子规格
EFC (默认false，用户不可见、不可改)我记得聊接口时，这个是不能修改的，目前前端页面UI显示能修改
产品类型
参考价（元）
销售价（元）-确认环节更新或代客下单直接提交
合计价格（参考价）
合计价格（销售价）
赠品数量-确认环节更新或代客下单直接提交