version: '3.5'
services:

  elasticsearch7.6.2:
    image: elasticsearch:7.6.2
    container_name: es-data02
    restart: always
    environment:
      - "ES_JAVA_OPS=-Xms2048m -Xmx2048m"
      - TAKE_FILE_OWNERSHIP=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - '9200:9200'
      - '9300:9300'
    volumes:
      - /data/data/elasticsearch/plugins:/usr/share/elasticsearch/plugins
      - /data/data/elasticsearch/data:/usr/share/elasticsearch/data
      - /data/data/elasticsearch/conf/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
      - /data/data/elasticsearch/logs:/usr/share/elasticsearch/logs


