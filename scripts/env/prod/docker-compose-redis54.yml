version: '3.5'

services:
  slave1:
    image: redis:6.2.6
    container_name: redis-slave-1
    privileged: true
    restart: always
    command: [ 'redis-server','/etc/redis/redis.conf' ]
    volumes:
      - /data/data/redis/redis.conf:/etc/redis/redis.conf
      - /data/data/redis/data:/data
    ports:
      - '6379:6379'
  sentinel2:
    image: redis:6.2.6
    container_name: redis-sentinel-2
    command: [ 'redis-sentinel','/etc/redis/sentinel/sentinel.conf' ]
    restart: always
    privileged: true
    ports:
      - '26379:26379'
    volumes:
      - /data/data/redis/sentinel/sentinel.conf:/etc/redis/sentinel/sentinel.conf
