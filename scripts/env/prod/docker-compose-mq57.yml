version: '3.5'
services:
  rmqbroker-slave-b:
    image: foxiswho/rocketmq:broker-4.5.2
    container_name: rmqbroker-slave-b
    ports:
      - 10911:10911
      - 10912:10912
      - 10909:10909
    volumes:
      - /data/data/rocketmq/broker-logs:/root/logs/rocketmqlogs
      - /data/data/rocketmq/broker-store:/root/store
      - /data/data/rocketmq/conf/broker.conf:/etc/rocketmq/broker.conf
    restart: always
    privileged: true # 拥有容器内命令执行的权限
    user: root
    environment:
      JAVA_OPTS: "-Duser.home=/opt"
      JAVA_OPT_EXT: "-server -Xms512m -Xmx512m -Xmn512m"
    command: sh mqbroker -c /etc/rocketmq/broker.conf autoCreateTopicEnable=false &



