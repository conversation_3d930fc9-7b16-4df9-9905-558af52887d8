version: '3.5'
services:
  elasticsearch7.6.2:
    image: elasticsearch:7.6.2
    container_name: es-master
    restart: always
    environment:
      - "ES_JAVA_OPS=-Xms2048m -Xmx2048m"
      - TAKE_FILE_OWNERSHIP=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - '9200:9200'
      - '9300:9300'
    volumes:
      - /data/data/elasticsearch/plugins:/usr/share/elasticsearch/plugins
      - /data/data/elasticsearch/data:/usr/share/elasticsearch/data
      - /data/data/elasticsearch/conf/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
      - /data/data/elasticsearch/logs:/usr/share/elasticsearch/logs

  xxl-job-admin:
    image: xuxueli/xxl-job-admin:2.4.0
    restart: always
    privileged: true # 拥有容器内命令执行的权限
    hostname: xxl-job-admin
    container_name: xxl-job-admin
    ports:
      - 9997:9997
    volumes:
      - /data/data/xxljob/logs:/data/applogs
      - /data/data/xxljob/conf/application.properties:/opt/xxljob/conf/application.properties
    environment:
      JAVA_OPTS: "-Xms512m -Xmx512m"
      PARAMS: "--spring.config.location=/opt/xxljob/conf/application.properties"



