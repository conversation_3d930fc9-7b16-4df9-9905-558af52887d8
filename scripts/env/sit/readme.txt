#mysql连接信息
DB版本：mysql8.0
集群域名地址：sharesitdb.trinasolar.com
端口：7001
德勤通业务库名: thtdb
德勤通业务用户: thtuser
密码 tht23Admtest

#rocketmq连接信息：
控制面板地址：http://*************:9101/
name-server: *************:9876;*************:9876

#redis连接信息
    sentinel:
      master: redis-master
      nodes:
        - *************:26379
        - *************:26379
        - *************:26379
password: Trina@123

#es连接信息
host: *************:9200,*************:9200,*************:9200

#xxl-job集群连接信息
addresses:http://*************:9997/xxl-job-admin,http://*************:9997/xxl-job-admin


elk日志地址：http://kibana-test.trinasolar.com


nacos测试环境	https://nacos-test.trinasolar.com/nacos/index.html
nacos 开发测试账号密码：trinamall/trinamall123
一、部署redis
###############################部署Master节点和Sentinel1###################
# 登录堡垒机选择sit01 ssh客户端部署redis
1.创建需要挂载的数据目录
cd /data
# 在data目录执行
mkdir -p data/redis
# 选择自己的软件安装目录，新建挂载文件目录
cd data/redis
mkdir data sentinel
vim redis.conf
蒋以下配置放入redis.conf文件中保存退出（:wq）
#修改连接为所有ip
bind 0.0.0.0
#允许外网访问
protected-mode no
port 6379
timeout 0
databases 16
save 900 1
save 300 10
save 60 10000
dbfilename dump.rdb
dir ./
# 配置master密码
requirepass Trina@123
# 宕机后成为从要连接master的密码
masterauth Trina@123
daemonize no
appendonly yes
appendfilename "appendonly.aof"

1.1切换到sentinel目录
cd sentinel
vim sentinel.conf
蒋以下配置放入sentinel.conf文件中保存退出（:wq）
port 26379
daemonize no
sentinel monitor redis-master ************* 6379 2
sentinel auth-pass redis-master Trina@123
sentinel down-after-milliseconds redis-master 6000
sentinel parallel-syncs redis-master 1
sentinel failover-timeout redis-master 6000
# sentinel announce-ip <ip>
# sentinel announce-port <port>

2.创建dockercompose目录并将编写好的docker-compose.yml文件放在该目录下
cd /data
mkdir dockercompose
cd dockercompose
vim docker-compose.yml
将编写好的docker-compose redis脚本放入保存退出
#########################注意事项############################################
编写的redis-master和redis-sentinel必须指定在同一个网络不然无法连接

3.在dockercompose目录下执行启动命令
cd /data/dockercompose
docker-compose up -d
#查看容器是否启动
docker-compose ps
#查看容器日志
docker-compose logs -f

#############################部署slave1节点和Sentinel2#######################
# 登录堡垒机选择sit02 ssh客户端部署redis
1.创建需要挂载的数据目录
cd /data
# 在data目录执行
mkdir -p data/redis
# 选择自己的软件安装目录，新建挂载文件目录
cd data/redis
mkdir data sentinel
vim redis.conf
蒋一下配置放入redis.conf文件中保存退出（:wq）
# 配置master的ip和端口号
replicaof ************* 6379
#修改连接为所有ip
bind 0.0.0.0
#允许外网访问
protected-mode no
port 6379
timeout 0
databases 16
save 900 1
save 300 10
save 60 10000
dbfilename dump.rdb
dir ./
# 成为master后的密码
requirepass Trina@123
# 连接master密码
masterauth Trina@123
daemonize no
appendonly yes
appendfilename "appendonly.aof"

1.1切换到sentinel目录
cd sentinel
vim sentinel.conf
蒋以下配置放入sentinel.conf文件中保存退出（:wq）
port 26379
daemonize no
sentinel monitor redis-master ************* 6379 2
sentinel auth-pass redis-master Trina@123
sentinel down-after-milliseconds redis-master 6000
sentinel parallel-syncs redis-master 1
sentinel failover-timeout redis-master 6000
sentinel announce-ip *************
sentinel announce-port 26379

2.创建dockercompose目录并将编写好的docker-compose.yml文件放在该目录下
cd /data
mkdir dockercompose
cd dockercompose
vim docker-compose.yml
将编写好的docker-compose redis脚本放入保存退出

3.在dockercompose目录下执行启动命令
cd /data/dockercompose
docker-compose up -d
#查看容器是否启动
docker-compose ps
#查看容器日志
docker-compose logs -f

#############################部署slave2节点和Sentinel3#######################
# 登录堡垒机选择sit02 ssh客户端部署redis
1.创建需要挂载的数据目录
cd /data
# 在data目录执行
mkdir -p data/redis
# 选择自己的软件安装目录，新建挂载文件目录
cd data/redis
mkdir data
vim redis.conf
蒋一下配置放入redis.conf文件中保存退出（:wq）
# 配置master的ip和端口号
replicaof ************* 6379
#修改连接为所有ip
bind 0.0.0.0
#允许外网访问
protected-mode yes
port 6379
timeout 0
databases 16
save 900 1
save 300 10
save 60 10000
dbfilename dump.rdb
dir ./
# 成为master后的密码
requirepass Trina@123
# 连接master密码
masterauth Trina@123
daemonize no
appendonly yes
appendfilename "appendonly.aof"

1.1切换到sentinel目录
cd sentinel
vim sentinel.conf
蒋以下配置放入sentinel.conf文件中保存退出（:wq）
port 26379
daemonize no
sentinel monitor redis-master ************* 6379 2
sentinel auth-pass redis-master Trina@123
sentinel down-after-milliseconds redis-master 6000
sentinel parallel-syncs redis-master 1
sentinel failover-timeout redis-master 6000
sentinel announce-ip *************
sentinel announce-port 26379

2.创建dockercompose目录并将编写好的docker-compose.yml文件放在该目录下
cd /data
mkdir dockercompose
cd dockercompose
vim docker-compose.yml
将编写好的docker-compose redis脚本放入保存退出

3.在dockercompose目录下执行启动命令
cd /data/dockercompose
docker-compose up -d
#查看容器是否启动
docker-compose ps
#查看容器日志
docker-compose logs -f


二、部署rocketmq
###############################部署Master-a节点###################
# 登录堡垒机选择test01 ssh客户端部署rocketmq4.5.2
1.创建需要挂载的数据目录
cd /data
# 在data目录执行
mkdir -p data/rocketmq
# 选择自己的软件安装目录，新建挂载文件目录
cd data/rocketmq
mkdir conf logs store broker-logs broker-store
2.进入conf目录。新建broker.conf 文件

vi conf/broker.conf
#集群名称
brokerClusterName=DefaultCluster
#broker名称
brokerName=broker-a
#brokerId master用0 slave用其他
brokerId=0
#清理时机
deleteWhen=04
#文件保留时长 48小时
fileReservedTime=48
#broker角色 -ASYNC_MASTER异步复制 -SYNC_MASTER同步双写 -SLAVE
brokerRole=SYNC_MASTER
#刷盘策略 - ASYNC_FLUSH 异步刷盘 - SYNC_FLUSH 同步刷盘
flushDiskType=ASYNC_FLUSH
#主机ip
brokerIP1=*************
#存在broker主从时,在broker主节点上配置了brokerIP2的话,broker从节点会连接主节点配置的brokerIP2来同步
brokerIP2=*************
# nameServer地址;分号分割

namesrvAddr=*************:9876;*************:9876
#对外服务的监听接口，同一台机器上部署多个broker,端口号要不相同
listenPort=10911



3.创建dockercompose目录并将编写好的docker-compose.yml文件放在该目录下
cd /data
mkdir dockercompose
cd dockercompose
vim docker-compose.yml
将编写好的docker-compose脚本放入保存退出
mqnamesrv 主机服务
mqbroker 控制端
mqconsole 视图工具
4.在dockercompose目录下执行启动命令
cd /data/dockercompose
docker-compose up -d
#查看容器是否启动
docker-compose ps
#查看容器日志
docker-compose logs -f

###############################部署Master-b节点###################
# 登录堡垒机选择test02 ssh客户端部署rocketmq4.5.2
1.创建需要挂载的数据目录
cd /data
# 在data目录执行
mkdir -p data/rocketmq
# 选择自己的软件安装目录，新建挂载文件目录
cd data/rocketmq
mkdir conf logs store broker-logs broker-store
2.进入conf目录。新建broker.conf 文件

vi conf/broker.conf
#集群名称
brokerClusterName=DefaultCluster
#broker名称
brokerName=broker-b
#brokerId master用0 slave用其他
brokerId=0
#清理时机
deleteWhen=04
#文件保留时长 48小时
fileReservedTime=48
#broker角色 -ASYNC_MASTER异步复制 -SYNC_MASTER同步双写 -SLAVE
brokerRole=SYNC_MASTER
#刷盘策略 - ASYNC_FLUSH 异步刷盘 - SYNC_FLUSH 同步刷盘
flushDiskType=ASYNC_FLUSH
#主机ip
brokerIP1=*************
#存在broker主从时,在broker主节点上配置了brokerIP2的话,broker从节点会连接主节点配置的brokerIP2来同步
brokerIP2=*************
# nameServer地址;分号分割

namesrvAddr=*************:9876;*************:9876
#对外服务的监听接口，同一台机器上部署多个broker,端口号要不相同
listenPort=10911



3.创建dockercompose目录并将编写好的docker-compose.yml文件放在该目录下
cd /data
mkdir dockercompose
cd dockercompose
vim docker-compose.yml
将编写好的docker-compose脚本放入保存退出
mqnamesrv 主机服务
mqbroker 控制端
4.在dockercompose目录下执行启动命令
cd /data/dockercompose
docker-compose up -d
#查看容器是否启动
docker-compose ps
#查看容器日志
docker-compose logs -f


###############################部署Slave-a节点###################
# 登录堡垒机选择test03 ssh客户端部署rocketmq4.5.2
1.创建需要挂载的数据目录
cd /data
# 在data目录执行
mkdir -p data/rocketmq
# 选择自己的软件安装目录，新建挂载文件目录
cd data/rocketmq
mkdir conf logs store broker-logs broker-store
2.进入conf目录。新建broker.conf 文件

vi conf/broker.conf
#集群名称
brokerClusterName=DefaultCluster
#broker名称
brokerName=broker-a
#brokerId master用0 slave用其他
brokerId=1
#清理时机
deleteWhen=04
#文件保留时长 48小时
fileReservedTime=48
#broker角色 -ASYNC_MASTER异步复制 -SYNC_MASTER同步双写 -SLAVE
brokerRole=SLAVE
#刷盘策略 - ASYNC_FLUSH 异步刷盘 - SYNC_FLUSH 同步刷盘
flushDiskType=ASYNC_FLUSH
#主机ip
brokerIP1=*************
#存在broker主从时,在broker主节点上配置了brokerIP2的话,broker从节点会连接主节点配置的brokerIP2来同步
brokerIP2=*************
# nameServer地址;分号分割


namesrvAddr=*************:9876;*************:9876
#对外服务的监听接口，同一台机器上部署多个broker,端口号要不相同
listenPort=10911



3.创建dockercompose目录并将编写好的docker-compose.yml文件放在该目录下
cd /data
mkdir dockercompose
cd dockercompose
vim docker-compose.yml
将编写好的docker-compose脚本放入保存退出
mqnamesrv 主机服务
mqbroker 控制端
4.在dockercompose目录下执行启动命令
cd /data/dockercompose
docker-compose up -d
#查看容器是否启动
docker-compose ps
#查看容器日志
docker-compose logs -f

###############################部署Slave-b节点###################
# 登录堡垒机选择test04 ssh客户端部署rocketmq4.5.2
1.创建需要挂载的数据目录
cd /data
# 在data目录执行
mkdir -p data/rocketmq
# 选择自己的软件安装目录，新建挂载文件目录
cd data/rocketmq
mkdir conf logs store broker-logs broker-store
2.进入conf目录。新建broker.conf 文件

vi conf/broker.conf
#集群名称
brokerClusterName=DefaultCluster
#broker名称
brokerName=broker-b
#brokerId master用0 slave用其他
brokerId=1
#清理时机
deleteWhen=04
#文件保留时长 48小时
fileReservedTime=48
#broker角色 -ASYNC_MASTER异步复制 -SYNC_MASTER同步双写 -SLAVE
brokerRole=SLAVE
#刷盘策略 - ASYNC_FLUSH 异步刷盘 - SYNC_FLUSH 同步刷盘
flushDiskType=ASYNC_FLUSH
#主机ip
brokerIP1=*************
#存在broker主从时,在broker主节点上配置了brokerIP2的话,broker从节点会连接主节点配置的brokerIP2来同步
brokerIP2=*************
# nameServer地址;分号分割


namesrvAddr=*************:9876;*************:9876
#对外服务的监听接口，同一台机器上部署多个broker,端口号要不相同
listenPort=10911



3.创建dockercompose目录并将编写好的docker-compose.yml文件放在该目录下
cd /data
mkdir dockercompose
cd dockercompose
vim docker-compose.yml
将编写好的docker-compose脚本放入保存退出
mqnamesrv 主机服务
mqbroker 控制端
4.在dockercompose目录下执行启动命令
cd /data/dockercompose
docker-compose up -d
#查看容器是否启动
docker-compose ps
#查看容器日志
docker-compose logs -f

一、部署es
集群规划
节点目录	           节点名称	协调端口号	        说明	       查询端口号	    节点IP
docker-es-master	master	9300	master节点，非数据节点	    9200	    *************
docker-es-data01	data01	9300	数据节点1，非master节点	    9200	    *************
docker-es-data02	data02	9300	数据节点2，非master节点	    9200	    *************

###############################部署Master节点###################
# 登录堡垒机选择test01 ssh客户端部署ES
1.创建需要挂载的数据目录
cd /data
# 在data目录执行
mkdir -p data/elasticsearch
# 选择自己的软件安装目录，新建挂载文件目录
cd data/elasticsearch
mkdir data logs plugins conf
cd conf
vim elasticsearch.yml
蒋以下配置放入elasticsearch.yml文件中
# ======================== Elasticsearch Configuration =========================
cluster.name: es-cluster
node.name: master
node.master: true
node.data: false
node.attr.rack: r1
bootstrap.memory_lock: true
http.port: 9200
network.host: 0.0.0.0
network.publish_host: *************
transport.tcp.port: 9300
discovery.seed_hosts: ["*************:9300","*************:9300"]
cluster.initial_master_nodes: ["master"]
gateway.recover_after_nodes: 2
# 是否支持跨域，默认为false
http.cors.enabled: true
# 当设置允许跨域，默认为*,表示支持所有域名，如果我们只是允许某些网站能访问，那么可以使用正则表达式。比如只允许本地地址。 /https?://localhost(:[0-9]+)?/
http.cors.allow-origin: "*"

2.创建dockercompose目录并将编写好的docker-compose.yml文件放在该目录下
cd /data
mkdir dockercompose
cd dockercompose
vim docker-compose.yml
将编写好的docker-compose es脚本放入保存退出

3.在dockercompose目录下执行启动命令
cd /data/dockercompose
docker-compose up -d
#查看容器是否启动
docker-compose ps
#查看容器日志
docker-compose logs -f
##############################注意事项############################
不加下面配置启动ES会报错
[1]: memory locking requested for elasticsearch process but memory is not locked
解决方案：修改文件/etc/security/limits.conf，最后添加以下内容

* soft nproc 32000

* hard nproc 32000

* hard memlock unlimited

* soft memlock unlimited
修改文件 /etc/systemd/system.conf ，分别修改以下内容。
DefaultLimitNOFILE=65536

DefaultLimitNPROC=32000

DefaultLimitMEMLOCK=infinity

####################[1]需要重启系统才能生效################

另外一种方案是在docker-compose文件中加入以下解除内存限制相关设置
   ulimits:
     memlock:
       soft: -1
       hard: -1

[2]: max virtual memory areas vm.max_map_count [65530] is too low, increase to at least [262144]
解决方案：在 /etc/sysctl.conf文件最后添加一行，vm.max_map_count=262144 保存退出
sysctl -p 可以查看是否设置成功

配置好重启ES就成功了

使用curl http://localhost:9200/_cat/health?v 查看集群状态是否正常



###############################部署datanode1节点###################


# 登录堡垒机选择test02 ssh客户端部署ES
1.创建需要挂载的数据目录
cd /data
# 在data目录执行
mkdir -p data/elasticsearch
# 选择自己的软件安装目录，新建挂载文件目录
cd data/elasticsearch
mkdir data logs plugins conf
cd conf
vim elasticsearch.yml
蒋以下配置放入elasticsearch.yml文件中
# ======================== Elasticsearch Configuration =========================
cluster.name: es-cluster
node.name: data01
node.master: false
node.data: true
node.attr.rack: r1
bootstrap.memory_lock: true
http.port: 9200
network.host: 0.0.0.0
network.publish_host: *************
transport.tcp.port: 9300
discovery.seed_hosts: ["*************:9300","*************:9300"]
cluster.initial_master_nodes: ["master"]
gateway.recover_after_nodes: 2

2.创建dockercompose目录并将编写好的docker-compose.yml文件放在该目录下
cd /data
mkdir dockercompose
cd dockercompose
vim docker-compose.yml
将编写好的docker-compose es脚本放入保存退出

3.在dockercompose目录下执行启动命令
cd /data/dockercompose
docker-compose up -d
#查看容器是否启动
docker-compose ps
#查看容器日志
docker-compose logs -f
##############################注意事项############################
不加下面配置启动ES会报错
[1]: max virtual memory areas vm.max_map_count [65530] is too low, increase to at least [262144]
解决方案：在 /etc/sysctl.conf文件最后添加一行，vm.max_map_count=262144 保存退出
sysctl -p 可以查看是否设置成功

###############################部署datanode2节点###################

# 登录堡垒机选择test03 ssh客户端部署ES
1.创建需要挂载的数据目录
cd /data
# 在data目录执行
mkdir -p data/elasticsearch
# 选择自己的软件安装目录，新建挂载文件目录
cd data/elasticsearch
mkdir data logs plugins conf
cd conf
vim elasticsearch.yml
蒋以下配置放入elasticsearch.yml文件中
# ======================== Elasticsearch Configuration =========================
cluster.name: es-cluster
node.name: data02
node.master: false
node.data: true
node.attr.rack: r1
bootstrap.memory_lock: true
http.port: 9200
network.host: 0.0.0.0
network.publish_host: *************
transport.tcp.port: 9300
discovery.seed_hosts: ["*************:9300","*************:9300"]
cluster.initial_master_nodes: ["master"]
gateway.recover_after_nodes: 2

2.创建dockercompose目录并将编写好的docker-compose.yml文件放在该目录下
cd /data
mkdir dockercompose
cd dockercompose
vim docker-compose.yml
将编写好的docker-compose es脚本放入保存退出

3.在dockercompose目录下执行启动命令
cd /data/dockercompose
docker-compose up -d
#查看容器是否启动
docker-compose ps
#查看容器日志
docker-compose logs -f
##############################注意事项############################
不加下面配置启动ES会报错
[1]: max virtual memory areas vm.max_map_count [65530] is too low, increase to at least [262144]
解决方案：在 /etc/sysctl.conf文件最后添加一行，vm.max_map_count=262144 保存退出
sysctl -p 可以查看是否设置成功


###################################### xxl-job ##########################################
五、部署xxl-job多实例
前提：sql初始化
蒋xxl-job.sql在对应的mysql库执行见dev目录
# 登录堡垒机选择test03/test04 ssh客户端分别部署xxl-job
1.创建需要挂载的数据目录
cd /data
# 在data目录执行
mkdir -p data/xxljob
# 选择自己的软件安装目录，新建挂载文件目录
cd data/xxljob
mkdir logs conf

cd conf
vim application.properties

蒋以下配置放入xxljob.properties文件中的配置复制进去保存退出
需要根据对应环境替换配置文件中mysql数据库连接信息

2.创建dockercompose目录并将编写好的docker-compose.yml文件放在该目录下
cd /data
mkdir dockercompose
cd dockercompose
vim docker-compose.yml
将编写好的docker-compose xxl-job脚本放入保存退出


3.在dockercompose目录下执行启动命令
cd /data/dockercompose
docker-compose up -d
#查看容器是否启动
docker-compose ps
#查看容器日志
docker-compose logs -f



