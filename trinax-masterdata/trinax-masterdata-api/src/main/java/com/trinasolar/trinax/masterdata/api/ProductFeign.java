package com.trinasolar.trinax.masterdata.api;

import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.masterdata.constants.ServiceIds;
import com.trinasolar.trinax.masterdata.dto.input.*;
import com.trinasolar.trinax.masterdata.dto.input.app.ProductAppPageReqDTO;
import com.trinasolar.trinax.masterdata.dto.input.app.ProductDocReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.*;
import com.trinasolar.trinax.masterdata.dto.output.app.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@FeignClient(name = ServiceIds.MASTERDATA_SERVICE, fallbackFactory = Void.class)
public interface ProductFeign {

    @Operation(summary = "同步产品族信息")
    @PostMapping("/products/sync")
    Result<Void> sync(@RequestBody SyncProductReqDTO req);

    @Operation(summary = "同步编号-同步产品族信息")
    @PostMapping("/products/sync/syncOrderNo")
    Result<Void> syncProduct(@RequestParam String syncOrderNo);

    @Operation(summary = "管理-产品分页查询")
    @PostMapping("/products/admin/page")
    Result<PageResponse<ProductPageForAdminResDTO>> pageProductForAdmin(@RequestBody PageRequest<ProductPageForAdminReqDTO> req);

    @Operation(summary = "管理-产品不分页查询-导出")
    @PostMapping("/products/admin/list")
    Result<List<ProductPageForAdminResDTO>> listProductForAdmin(@RequestBody ProductPageForAdminReqDTO req);

    @Operation(summary = "App—产品列表页")
    @GetMapping("/products/storage/docs")
    Result<List<ProductDocAppRespDTO>> getStorageProductDocs(@RequestParam String productFamily);

    @Operation(summary = "管理—产品详情查看")
    @GetMapping("/products/admin-product/{productId}/{productCategory}")
    Result<ProductAppRespDTO> getProductAdmin(@PathVariable String productId, @PathVariable String productCategory);

    @Operation(summary = "App—产品列表页")
    @PostMapping("/products/app-page")
    Result<PageResponse<AppProductMainRecommendPageResDTO>> getProductAppPage(@RequestBody PageRequest<ProductAppPageReqDTO> request);

    @Operation(summary = "查询产品功率范围")
    @PostMapping("/products/getFamilyOutputPower")
    Result<Map<String,List<ProductModulePowerDTO>>> getFamilyOutputPower(@RequestBody List<String> productIdList);

    @Operation(summary = "App—产品首页")
    @GetMapping("/products/app-home")
    Result<ProductAppHomeRespDTO> getProductAppHome();

    @Operation(summary = "App—产品详情查看-组件")
    @GetMapping("/products/app-product/{productId}/{productCategory}")
    Result<AppProductDetailResDTO> getProductApp(@PathVariable @NotBlank String productId,
                                                 @PathVariable @NotBlank String productCategory,
                                                 @RequestParam(name = "userId",required = false) String userId,
                                                 @RequestParam(name = "userType",required = false) String userType);

    @Operation(summary = "App—产品详情查看-工商储")
    @GetMapping("/products/app-product/storage/{productId}")
    Result<AppProductStorageDetailResDTO> getProductStorageDetail(@PathVariable @NotBlank String productId,
                                                 @RequestParam(name = "userId",required = false) String userId,
                                                 @RequestParam(name = "userType",required = false) String userType);

    @Operation(summary = "App—产品文档列表")
    @GetMapping("/products/docs")
    @Parameters({
            @Parameter(name = "productId", required = true, description = "productId", example = "", schema = @Schema(type = "string")),
            @Parameter(name = "version", required = true, description = "version", example = "", schema = @Schema(type = "string")),
            @Parameter(name = "regions", required = true, description = "regions", example = "", schema = @Schema(example = "string"))
    })
    Result<List<ProductDocAppRespDTO>> getProductDocs(@Parameter(hidden = true) @SpringQueryMap ProductDocReqDTO reqDTO);

    @Operation(summary = "获取产品模块功率数据")
    @GetMapping("/products/module-power")
    Result<List<ProductModulePowerDTO>> getModulePower(@RequestParam(name = "productId") String productId, @RequestParam(name = "specificPower", required = false) String specificPower);

    @Operation(summary = "批量获取产品特定功率信息")
    @PostMapping("/products/module-power-batch")
    Result<List<ProductModulePowerDTO>> getModulePowerBatch(@NotEmpty @RequestBody Collection<ProductModulePowerStatusReqDTO> productModulePowerStatusReqDTO);

    @Operation(summary = "批量获取产品功率范围-根据产品版本号搜索")
    @PostMapping("/products/qryPowerRangeByProds")
    Result<List<ProductModulePowerDTO>> qryPowerRangeByProds(@NotEmpty @RequestBody List<String> productIds);

    @Operation(summary = "批量获取产品特定功率信息")
    @PostMapping("/products/queryProductModulePowerList")
    Result<List<ProductModulePowerDTO>> queryProductModulePowerList(@RequestBody ProductModulePowerQueryReqDTO req);

    @Operation(summary = "管理—上下架功率")
    @PostMapping("/products/module-power-shelf")
    Result<Boolean> shelfModulePower(@RequestBody ProductModulePowerShelfReqDTO reqDto);

    @Operation(summary = "管理—上下架产品")
    @PostMapping("/products/product-shelf")
    Result<List<String>> shelfProductBatch(@RequestBody ProductShelfReqDTO productShelfReq);

    @Operation(summary = "管理—上下架产品开关")
    @PutMapping("/products/product-shelf-switch")
    Result<Boolean> shelfProductSwitch(@RequestBody ProductShelfSwitchReqDTO productShelfReq);

    @Operation(summary = "管理—产品配置")
    @PostMapping("/products/admin-product")
    Result<String> saveProduct(@Valid @RequestBody ProductAdminSaveReqDTO req);

    @Operation(summary = "通过productId,批量获取productDetail")
    @PostMapping("/products/productDetailByProductId")
    Result<List<ProductDetailResDTO>> productDetailByProductId(@RequestBody List<String> productIdList);

    @Operation(summary = "通过productId,批量获取赠品附加信息")
    @PostMapping("/products/productGivenExtendByProductId")
    Result<List<ProductGivenExtendResDTO>> productGivenExtendByProductId(@RequestBody List<String> productIdList);

    @Operation(summary = "查询产品是否可售")
    @PostMapping("/products/salability")
    Result<ProductSalabilityResDTO> querySalability(@RequestBody ProductSalabilityQueryReqDTO req);

    @Operation(summary = "通过productId,批量获取图片")
    @PostMapping("/products/getImages")
    Result<List<ProductImageItemResDTO>> getImages(@RequestBody ProductImageQueryReqDTO req);

    @Operation(summary = "通过productId,批量获取图片")
    @PostMapping("/products/getSummary")
    Result<List<ProductSummaryDTO>> getSummaryByProductIds(@RequestBody ProductSummaryQueryReqDTO req);

    @Operation(summary = "通过产品名称,批量获取icon")
    @PostMapping("/products/release/ReleaseProductImageDTO")
    Result<List<ReleaseProductImageDTO>> getReleaseProductImageDTO(@RequestBody List<String> productNames);

    @Operation(summary = "查询产品里的所有regions")
    @GetMapping("/products/regions")
    Result<List<String>> queryAllRegion();

    @Operation(summary = "管理-工商储产品分页查询")
    @PostMapping("/products/admin/storage/page")
    Result<PageResponse<ProductStoragePageForAdminResDTO>> pageStorageProductForAdmin(@RequestBody PageRequest<ProductPageForAdminReqDTO> request);

    @Operation(summary = "管理-工商储产品导出")
    @PostMapping("/products/admin/storage/export")
    Result<List<ProductStoragePageForAdminResDTO>> exportStorageProductForAdmin(@RequestBody ProductPageForAdminReqDTO req);

    @Operation(summary = "同步工商储产品族信息")
    @PostMapping("/products/storage/sync")
    Result<Void> storageSync(@RequestBody SyncStorageProductReqDTO req);

    @Operation(summary = "获取产品详情")
    @GetMapping("/products/detailByStorage")
    Result<ProductDetailStorageResDTO> detailByStorage(@RequestParam(name = "productId") String productId);

    @Operation(summary = "管理—产品保存")
    @PostMapping("/products/saveProductStorage")
    Result<String> saveProductStorage(@RequestBody ProductSaveReqStorageDTO req);

    @Operation(summary = "管理—工商储产品-上下架产品")
    @PostMapping("/products/shelfStorageProductBatch")
    Result<List<String>> shelfStorageProductBatch(@RequestBody ProductShelfReqDTO productShelfReq);
}
