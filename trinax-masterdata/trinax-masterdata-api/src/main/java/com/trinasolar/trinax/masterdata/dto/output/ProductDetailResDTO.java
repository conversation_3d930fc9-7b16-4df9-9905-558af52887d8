package com.trinasolar.trinax.masterdata.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ProductDetailResDTO {

    @Schema(description = "数据库主键")
    private Long id;
    @Schema(description = "产品ID")
    private String productId;
    @Schema(description = "产品名称")
    private String productName;
    @Schema(description = "上市区域")
    private String regions;
    @Schema(description = "国家")
    private String country;
    @Schema(description = "产品版本")
    private String productVersion;
    @Schema(description = "产品状态")
    private String productStatus;
    @Schema(description = "产品系列")
    private String productSeries;
    @Schema(description = "是否全球版")
    private String isGlobal;
    @Schema(description = "是否是最新版本;0、1")
    private Integer isLatest;
    @Schema(description = "上架状态;TOBE_UP 未上架、UP 上架、DOWN 下架")
    private String upStatus;
    @Schema(description = "上架时间")
    private LocalDateTime upTime;
    @Schema(description = "下架时间")
    private LocalDateTime downTime;
    @Schema(description = "是否新品")
    private String isNew;
    @Schema(description = "是否热销")
    private String isHot;
    @Schema(description = "产品经理")
    private String productManager;
    @Schema(description = "产品类别")
    private String productCategory;
    @Schema(description = "产品族")
    private String productFamily;
    @Schema(description = "产品描述（短）")
    private String descriptionShort;
    @Schema(description = "产品描述（长）")
    private String descriptionLong;
    @Schema(description = "预计上架时间")
    private LocalDateTime preActiveTime;
    @Schema(description = "预计下架时间")
    private LocalDateTime preEopTime;
    @Schema(description = "参考价格")
    private BigDecimal referencePrice;
    @Schema(description = "产品更新时间")
    private LocalDateTime productUpdateTime;
    @Schema(description = "是否已删除;1：已删除 0：未删除")
    private int isDeleted;
    @Schema(description = "创建人")
    private String createdBy;
    @Schema(description = "创建人姓名")
    private String createdName;
    @Schema(description = "创建时间")
    private LocalDateTime createdTime;
    @Schema(description = "更新人")
    private String updatedBy;
    @Schema(description = "更新人姓名")
    private String updatedName;
    @Schema(description = "更新时间")
    private LocalDateTime updatedTime;

}
