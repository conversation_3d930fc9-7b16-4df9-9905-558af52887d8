package com.trinasolar.trinax.masterdata.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * DESC：
 *
 * <AUTHOR>
 * @since 2023/11/07 16:42
 */
@Data
@Schema(description = "BannerImage 更新 Req DTO")
public class BannerImageUpdateReqDTO {

    /**
     * 自增主键
     */
    @NotNull
    private Long id;
    /**
     * 图片地址
     */
    private String imageUrl;
    /**
     * 排序号
     */
    private Integer seq;

    @Schema(description = "链接类型")
    private String url;
    /**
     * 标题
     */
    private String title;
    /**
     * 首页顶部-HOME_HEADER
     */
    @Schema(description = "类型")
    private String type;

    @Schema(description = "业务数据 id")
    private String bizId;

    @Schema(description = "链接界面")
    private String bizType;

    @Schema(description = "链接类型")
    private String linkType;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新人姓名
     */
    private String updatedName;

}
