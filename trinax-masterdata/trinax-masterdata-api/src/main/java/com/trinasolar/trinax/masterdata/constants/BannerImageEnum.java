package com.trinasolar.trinax.masterdata.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * banner_image表枚举
 */
@AllArgsConstructor
@Getter
public enum BannerImageEnum {
    HOME_HEADER("type", "HOME_HEADER", "首页轮播图"),
    MODULE("biz_type", "Module", "组件产品详情界面"),
    LINK_TYPE_IN("link_type", "LINK_TYPE_IN", "内部链接"),
    LINK_TYPE_EX("link_type", "LINK_TYPE_EX", "外部链接"),
    HTTP_URL("url_biz_type", "HTTP_URL", "外部网页"),
    WX_MINI_APP("url_biz_type", "WX_MINI_APP", "微信小程序"),
    ;


    private final String key;

    private final String value;

    private final String desc;

    private static Map<String, List<BannerImageEnum>> mapList;

    private static Map<String, Map<String, BannerImageEnum>> bannerImageMap;


    public static Map<String, List<BannerImageEnum>> getBannerImageMapList() {
        if (ObjectUtils.isEmpty(mapList)) {
            bannerImageMapListInit();
        }
        return mapList;
    }

    private static void bannerImageMapListInit() {
        mapList = Arrays.stream(BannerImageEnum.values()).collect(Collectors.groupingBy(BannerImageEnum::getKey));
    }

    public static Map<String, Map<String, BannerImageEnum>> getBannerImageMap() {
        if (ObjectUtils.isEmpty(bannerImageMap)) {
            bannerImageMapInit();
        }
        return bannerImageMap;
    }

    private static void bannerImageMapInit() {
        bannerImageMap = new HashMap<>();
        getBannerImageMapList();
        mapList.forEach((key, value) -> {
            Map<String, BannerImageEnum> curBannerMap = new HashMap<>();
            value.forEach(bannerImage -> {
                curBannerMap.put(bannerImage.getValue(), bannerImage);
            });
            bannerImageMap.put(key, curBannerMap);
        });
    }

}
