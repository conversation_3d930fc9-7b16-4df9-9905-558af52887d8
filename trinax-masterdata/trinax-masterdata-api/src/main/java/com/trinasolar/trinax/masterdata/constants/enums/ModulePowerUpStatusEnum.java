package com.trinasolar.trinax.masterdata.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 组件功率上架状态
 */
@AllArgsConstructor
@Getter
public enum ModulePowerUpStatusEnum {

    UP("UP", "已上架"),
    DOWN("DOWN", "已下架");

    /** 产品类别 */
    private final String code;
    /** 名称 */
    private final String desc;

    public boolean equalsCode(String code) {
        return this.code.equals(code);
    }

}
