package com.trinasolar.trinax.masterdata.dto.input.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

@Schema(description = "APP - 产品列表分页 Request DTO")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class ProductAppPageReqDTO  {
    private BigDecimal moduleLengthMin;
    private BigDecimal moduleLengthMax;
    /** 组件宽（mm） */
    private BigDecimal moduleWidthMin;
    private BigDecimal moduleWidthMax;

    @Schema(description = "产品名称")
    private String productName;
    /** 最小输出功率 */
    private Long outputPowerMin;
    /** 最大输出功率 */
    private Long outputPowerMax;

    @Schema(description = "用户ID")
    private String userId;
    @Schema(description = "用户类型")
    private String userType;

    @Schema(description = "标签类型0全部1组件2工商储")
    private String type;
}
