package com.trinasolar.trinax.masterdata.dto.output.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "APP - 产品卡片列表 Response DTO")
public class ProductAppPageRespDTO {
    private String productId;
    private String productName;
    @Schema(description = "产品类别，目前应该都是module")
    private String productCategory;
    @Schema(description = "小图")
    private String icon;
    @Schema(description = "首页图片")
    private String homeImage;
    @Schema(description = "主栅数")
    private String busBar;
    @Schema(description = "最大电压")
    private String maxSystemVoltage;
    private BigDecimal referencePrice;
    @Schema(description = "边框厚度")
    private String  frameThickness;
    @Schema(description = "产品描述（至尊宝维护）")
    private String description;

    //组件长（mm）
    @Schema(description = "组件长")
    private BigDecimal moduleLength;
    @Schema(description = "组件宽")
    private BigDecimal moduleWidth;
    @Schema(description = "组件重量")
    private BigDecimal moduleWeight;
    @Schema(description = "组件重量（带单位）")
    private String moduleWeightWithUnit;
    @Schema(description = "组件范围")
    private String moduleSize;
    //功率
    @Schema(description = "最小功率")
    private Long minOutputPower;
    @Schema(description = "最大功率")
    private Long maxOutputPower;
    @Schema(description = "功率范围")
    private String powerRange;
    @Schema(description = " 版本 - 提供给详情中查询文档列表")
    private String productVersion;
    @Schema(description = " 地区 - 提供给详情中查询文档列表")
    private String regions;

}
