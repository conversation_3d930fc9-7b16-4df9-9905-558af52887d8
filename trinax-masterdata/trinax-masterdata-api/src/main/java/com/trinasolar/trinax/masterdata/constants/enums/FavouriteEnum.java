package com.trinasolar.trinax.masterdata.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否收藏标识
 */
@AllArgsConstructor
@Getter
public enum FavouriteEnum {

    YES("1", "收藏"),
    NO("0", "未收藏");

    /** 是否收藏标识 */
    private final String code;
    /** 名称 */
    private final String desc;

    public boolean equalsCode(Integer code) {
        if(code == null){
            return false;
        }
        return this.code.equals(String.valueOf(code));
    }

}
