package com.trinasolar.trinax.masterdata.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "赠品关系分页查询结果")
public class ProductGivenRelationPageResDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "赠品类型：组件-Module、备件-Part")
    private String givenType;

    @Schema(description = "赠品类型名称")
    private String givenTypeName;

    @Schema(description = "意向产品ID")
    private String intentProductId;

    @Schema(description = "意向产品名称")
    private String intentProductName;

    @Schema(description = "赠品ID（实际可为产品ID、赠品ID）")
    private String givenProductId;

    @Schema(description = "赠品名称")
    private String givenProductName;

    @Schema(description = "每满多少")
    private BigDecimal perBuyQuantity;

    @Schema(description = "每满多少-单位：瓦 W、片 P、个 N")
    private String perBuyUnit;

    @Schema(description = "每满多少-单位类型名称")
    private String perBuyUnitName;

    @Schema(description = "赠送数量")
    private Long givenQuantity;

    @Schema(description = "赠送单位类型（片、瓦、个）")
    private String givenUnit;

    @Schema(description = "赠送单位类型名称")
    private String givenUnitName;

    @Schema(description = "赠送关系状态")
    private String relationStatus;

    @Schema(description = "赠送关系状态名称")
    private String relationStatusName;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "创建人姓名")
    private String createdName;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "修改人")
    private String updatedBy;

    @Schema(description = "修改人姓名")
    private String updatedName;

    @Schema(description = "更新时间")
    private LocalDateTime updatedTime;

}
