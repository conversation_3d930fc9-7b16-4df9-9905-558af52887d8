spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${DATASOURCE_URL}
    username: ${DATASOURCE_USERNAME}
    password: ${DATASOURCE_PASSWORD}
    hikari:
      minimum-idle: 10
      maximum-pool-size: 20
      idle-timeout: 10000
      max-lifetime: 1800000
      connection-timeout: 30000
  redis:
    # redis数据库索引（默认为0），我们使用索引为3的数据库，避免和其他数据库冲突
    database: 0
    # redis服务器地址（默认为localhost）
    host: ${REDIS_HOST}
    # redis端口（默认为6379）
    port: ${REDIS_PORT}
    # redis访问密码（默认为空）
    password: ${REDIS_PASSWORD}
    # redis连接超时时间（单位为毫秒）
    timeout: 30000

rocketmq:
  name-server: ${ROCKETMQ_HOST}
  producer:
    # 发送同一类消息的设置为同一个group，保证唯一
    group: masterdata
    # 发送消息失败重试次数，默认2
    retry-times-when-send-failed: 2
    # 异步消息重试此处，默认2
    retry-times-when-send-async-failed: 2

xxl:
  job:
    adminAddress: ${XXLJOB_ADMINADDRESS}
    executorName: trinax-masterdata-service
    ip:
    port: 12000
    accessToken:
    logPath: /apps/logs/${spring.application.name}/xxl-job
    logRetentionDays: -1

#日志上报环境地址
logging:
  collector:
    #dev
    address: ${LOGGING_COLLECTOR_ADDRESS}

message:
  templateCode:
    upNotice: PBI_PRODUCT_UP_NOTICE
    downNotice: PBI_PRODUCT_DOWN_NOTICE