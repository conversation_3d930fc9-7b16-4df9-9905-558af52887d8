package com.trinasolar.trinax.masterdata.endpoint.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.masterdata.api.ProductFeign;
import com.trinasolar.trinax.masterdata.constants.ProductResultCode;
import com.trinasolar.trinax.masterdata.constants.UpStatusEnum;
import com.trinasolar.trinax.masterdata.dto.input.*;
import com.trinasolar.trinax.masterdata.dto.input.app.ProductAppPageReqDTO;
import com.trinasolar.trinax.masterdata.dto.input.app.ProductDocReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.*;
import com.trinasolar.trinax.masterdata.dto.output.app.*;
import com.trinasolar.trinax.masterdata.repository.po.ProductDetailPO;
import com.trinasolar.trinax.masterdata.service.ProductDetailService;
import com.trinasolar.trinax.masterdata.service.ProductGivenExtendService;
import com.trinasolar.trinax.masterdata.service.ProductImageService;
import com.trinasolar.trinax.masterdata.service.ProductModulePowerService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.trinasolar.trinax.common.dto.output.Result.ok;

/**
 * <AUTHOR> Hongyi
 * created on 2023/9/25
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@Tag(name = "产品管理")
public class ProductController implements ProductFeign {

    private final ProductDetailService productDetailService;
    private final ProductModulePowerService productModulePowerService;
    private final ProductGivenExtendService productGivenExtendService;
    private final ProductImageService productImageService;

    @Override
    public Result<Void> sync(SyncProductReqDTO req) {
        productDetailService.saveSyncReq(req);
        return Result.ok();
    }

    @Override
    public Result<Void> syncProduct(String syncOrderNo) {
        productDetailService.syncProduct(syncOrderNo);
        return Result.ok();
    }

    @Override
    public Result<PageResponse<ProductPageForAdminResDTO>> pageProductForAdmin(PageRequest<ProductPageForAdminReqDTO> req) {
        return Result.ok(productDetailService.pageProductForAdmin(req));
    }

    @Override
    public Result<List<ProductPageForAdminResDTO>> listProductForAdmin(ProductPageForAdminReqDTO req) {
        return Result.ok(productDetailService.listProductForAdmin(req));
    }

    @Override
    public Result<PageResponse<AppProductMainRecommendPageResDTO>> getProductAppPage(PageRequest<ProductAppPageReqDTO> req) {
        return Result.ok(productDetailService.pageProductForApp(req));
    }

    @Override
    public Result<Map<String, List<ProductModulePowerDTO>>> getFamilyOutputPower(List<String> productIdList) {
        return productDetailService.getFamilyOutputPower(productIdList);
    }

    @Override
    public Result<ProductAppHomeRespDTO> getProductAppHome() {
        return Result.ok(productDetailService.getProductAppHome());
    }

    @Override
    public Result<AppProductDetailResDTO> getProductApp(String productId, String productCategory, String userId, String userType) {
        return Result.ok(productDetailService.getProductForApp(productId, productCategory, userId, userType));
    }

    @Override
    public Result<AppProductStorageDetailResDTO> getProductStorageDetail(String productId,String userId, String userType) {
        return Result.ok(productDetailService.getProductStorageDetail(productId,userId, userType));
    }

    @Override
    public Result<List<ProductDocAppRespDTO>> getProductDocs(ProductDocReqDTO reqDTO) {
        return Result.ok(productDetailService.getProductDocs(reqDTO));
    }

    @Override
    public Result<List<ProductDocAppRespDTO>> getStorageProductDocs(String productFamily) {
        return Result.ok(productDetailService.getStorageProductDocs(productFamily));
    }

    @Override
    public Result<ProductAppRespDTO> getProductAdmin(String productId, String productCategory) {
        return Result.ok(productDetailService.getProductAdmin(productId, productCategory));
    }

    @Override
    public Result<List<ProductModulePowerDTO>> getModulePower(String productId, String specificPower) {
        return Result.ok(productModulePowerService.getModulePower(productId, specificPower));
    }

    @Override
    public Result<List<ProductModulePowerDTO>> getModulePowerBatch(@Valid Collection<ProductModulePowerStatusReqDTO> productModulePowerStatusReqDtos) {
        return Result.ok(productModulePowerService.getModulePowerBatch(productModulePowerStatusReqDtos));
    }

    @Override
    public Result<List<ProductModulePowerDTO>> qryPowerRangeByProds(List<String> productIds) {
        return Result.ok(productModulePowerService.qryPowerRangeByProds(productIds));
    }

    @Override
    public Result<List<ProductModulePowerDTO>> queryProductModulePowerList(ProductModulePowerQueryReqDTO req) {
        return Result.ok(productModulePowerService.queryProductModulePowerList(req));
    }

    @Override
    public Result<Boolean> shelfModulePower(ProductModulePowerShelfReqDTO reqDto) {
        productModulePowerService.shelfModulePower(reqDto);
        return ok(true);
    }

    @Override
    public Result<List<String>> shelfProductBatch(ProductShelfReqDTO productShelfReq) {
        String operation = productShelfReq.getOperation();
        if (UpStatusEnum.UP.getCode().equals(operation)) {
            Result.ok(productDetailService.upShelfProductBatch(productShelfReq));
        } else if (UpStatusEnum.DOWN.getCode().equalsIgnoreCase(operation)) {
            Result.ok(productDetailService.offShelfProductBatch(productShelfReq));
        } else {
            throw new BizException(ProductResultCode.SHELF_STATUS.getCode(), ProductResultCode.SHELF_STATUS.getMessage());
        }

        return ok(new ArrayList<>());
    }

    @Override
    public Result<Boolean> shelfProductSwitch(ProductShelfSwitchReqDTO productShelfReq) {
        LambdaUpdateWrapper<ProductDetailPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProductDetailPO::getProductId, productShelfReq.getProductId());
        updateWrapper.set(ProductDetailPO::getIsAbleUp, productShelfReq.getIsAbleUp());
        updateWrapper.set(ProductDetailPO::getUpdatedTime, LocalDateTime.now());
        productDetailService.update(updateWrapper);
        return ok(true);
    }

    /***
     * 产品保存接口
     * @param req
     * @return Result<String>
     */
    @Override
    public Result<String> saveProduct(ProductAdminSaveReqDTO req) {
        return productDetailService.saveProduct(req);
    }

    @Override
    public Result<List<ProductDetailResDTO>> productDetailByProductId(List<String> productIdList) {
        return Result.ok(productDetailService.productDetailByProductId(productIdList));
    }

    @Override
    public Result<List<ProductGivenExtendResDTO>> productGivenExtendByProductId(List<String> productIdList) {
        return Result.ok(productGivenExtendService.productGivenExtendByProductId(productIdList));
    }

    @Override
    public Result<ProductSalabilityResDTO> querySalability(ProductSalabilityQueryReqDTO req) {
        return Result.ok(productDetailService.querySalability(req));
    }

    @Override
    public Result<List<ProductImageItemResDTO>> getImages(ProductImageQueryReqDTO req) {
        return Result.ok(productImageService.getImages(req));
    }

    @Override
    public Result<List<ProductSummaryDTO>> getSummaryByProductIds(ProductSummaryQueryReqDTO req) {
        List<String> productIds = req.getProductIds();
        List<ProductSummaryDTO> productSummaryList = productImageService.getSummaryByProductIds(productIds);
        return Result.ok(productSummaryList);
    }

    @Override
    public Result<List<ReleaseProductImageDTO>> getReleaseProductImageDTO(List<String> productNames) {
        return Result.ok(productImageService.getReleaseProductImageDTO(productNames));
    }

    @Override
    public Result<List<String>> queryAllRegion() {
        return Result.ok(productDetailService.queryAllRegion());
    }

    @Override
    public Result<PageResponse<ProductStoragePageForAdminResDTO>> pageStorageProductForAdmin(PageRequest<ProductPageForAdminReqDTO> req) {
        return Result.ok(productDetailService.pageStorageProductForAdmin(req));
    }

    @Override
    public Result<List<ProductStoragePageForAdminResDTO>> exportStorageProductForAdmin(ProductPageForAdminReqDTO req) {
        return Result.ok(productDetailService.listStorageProductForAdmin(req));
    }

    @Override
    public Result<Void> storageSync(SyncStorageProductReqDTO req) {
        log.info("ThirdPbiController.storage.sync req={}", JacksonUtil.bean2Json(req));
        productDetailService.saveSyncReq(req);
        return Result.ok();
    }

    @Override
    public Result<ProductDetailStorageResDTO> detailByStorage(String productId) {
        return Result.ok(productDetailService.detailByStorage(productId));
    }

    @Override
    public Result<String> saveProductStorage(ProductSaveReqStorageDTO req) {
        return productDetailService.saveProductStorage(req);
    }

    @Override
    public Result<List<String>> shelfStorageProductBatch(ProductShelfReqDTO productShelfReq) {
        String operation = productShelfReq.getOperation();
        if (UpStatusEnum.UP.getCode().equals(operation)) {
            Result.ok(productDetailService.upShelfProductStorage(productShelfReq));
        } else if (UpStatusEnum.DOWN.getCode().equalsIgnoreCase(operation)) {
            Result.ok(productDetailService.offShelfProductStorage(productShelfReq));
        } else {
            throw new BizException(ProductResultCode.SHELF_STATUS.getCode(), ProductResultCode.SHELF_STATUS.getMessage());
        }
        return ok(new ArrayList<>());
    }
}
