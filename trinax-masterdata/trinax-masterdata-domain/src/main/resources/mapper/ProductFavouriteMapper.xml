<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trinasolar.trinax.masterdata.repository.mapper.ProductFavouriteMapper">

    <select id="appPageProductFavourite"
            resultType="com.trinasolar.trinax.masterdata.dto.output.favourite.AppProductFavouriteRespDTO">
        select pf.product_favourite_id, pf.user_id, pf.favourite_type, pf.product_id, pf.product_name
        from product_favourite pf
        where 1 = 1
        <if test="query.loginUserId != null and query.loginUserId != ''">
            and pf.user_id = #{query.loginUserId}
        </if>
        <if test="query.productName != null and query.productName != ''">
            and pf.product_name like concat('%', #{query.productName}, '%')
        </if>
    </select>

    <select id="statisticFavourite"
            resultType="com.trinasolar.trinax.masterdata.dto.output.favourite.StatisticFavouriteRespDTO">
        select
            pf.product_name,
            count(1) num
        from
            product_favourite pf
                join product_family pf2 on
                pf2.product_name = pf.product_name
                join product_detail pd on
                pd.product_id = pf2.main_recommend_version
        where 1= 1
        <if test="query.mainRecommendVersionUpStatus != null and query.mainRecommendVersionUpStatus != ''">
            and pd.up_status = #{query.mainRecommendVersionUpStatus}
        </if>
        group by
            pf.product_name;
    </select>
</mapper>
