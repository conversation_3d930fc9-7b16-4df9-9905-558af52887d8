package com.trinasolar.trinax.masterdata.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trinasolar.trinax.masterdata.dto.input.ProductModulePowerStatusReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductModulePowerDTO;
import com.trinasolar.trinax.masterdata.repository.po.ProductModulePowerPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * created on 2023/10/23
 */
@Mapper
public interface ProductModulePowerMapper extends BaseMapper<ProductModulePowerPO> {
    List<ProductModulePowerDTO> findByProductIdAndPower(Collection<ProductModulePowerStatusReqDTO> productModulePower);
}
