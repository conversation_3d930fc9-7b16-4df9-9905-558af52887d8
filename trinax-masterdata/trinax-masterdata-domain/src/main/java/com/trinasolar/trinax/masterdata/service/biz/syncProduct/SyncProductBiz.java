package com.trinasolar.trinax.masterdata.service.biz.syncProduct;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.masterdata.constants.UpStatusEnum;
import com.trinasolar.trinax.masterdata.constants.enums.*;
import com.trinasolar.trinax.masterdata.constants.enums.ProductEms.ProductPropsEm;
import com.trinasolar.trinax.masterdata.constants.enums.ProductEms.SyncStatusEm;
import com.trinasolar.trinax.masterdata.dto.input.SyncProductReqDTO;
import com.trinasolar.trinax.masterdata.dto.input.SyncStorageProductReqDTO;
import com.trinasolar.trinax.masterdata.dto.mq.SyncProductCompletedMqDTO;
import com.trinasolar.trinax.masterdata.manager.ProductManager;
import com.trinasolar.trinax.masterdata.repository.atomicservice.*;
import com.trinasolar.trinax.masterdata.repository.po.*;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 同步产品族信息
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SyncProductBiz {

    private final static BigDecimal THOUSAND = new BigDecimal("1000");

    private final ProductDetailPbiMapperService productDetailPbiMapperService;
    private final ProductDetailMapperService productDetailMapperService;
    private final ProductModuleMapperService productModuleMapperService;
    private final ProductModulePowerMapperService productModulePowerMapperService;
    private final ProductFamilyMapperService productFamilyMapperService;
    private final ProductManager productManager;
    private final MqManager mqManager;
    private final ProductStorageMapperService productStorageMapperService;

    @Transactional
    public void execute(String syncOrderNo) {
        ProductDetailPbiPO productDetailPbi = productDetailPbiMapperService.lambdaQuery().eq(ProductDetailPbiPO::getSyncOrderNo, syncOrderNo).one();
        log.info("同步产品开始，syncOrderNo={}，productId={}", syncOrderNo, productDetailPbi.getProductId());

        if(ProductCategoryEnum.STORAGE.equalsCode(productDetailPbi.getProductCategory())){
            // 1.校验同步状态；2.校验产品状态；3.校验产品更新时间。(1未通过则结束流程；2、3未通过，则更新同步状态为已取消后结束流程)
            ProductDetailPO existsProduct = productDetailMapperService.getByProductId(productDetailPbi.getProductId());
            SyncStorageProductReqDTO req = JacksonUtil.json2Bean(productDetailPbi.getRawData(), SyncStorageProductReqDTO.class);
            //暂时注释掉
//            if (!checkStorage(productDetailPbi, existsProduct, req, syncOrderNo)) {
//                return;
//            }

            LocalDateTime now = LocalDateTime.now();

            // 字段映射，计算是否允许上架
            Pair<ProductDetailPO, ProductStoragePO> pair = convertStorage(req);
            ProductDetailPO productDetail = pair.getKey();
            productDetail.setUpdatedTime(now);
            ProductStoragePO module = pair.getValue();
            module.setUpdatedTime(now);

            // 产品存在则更新，不存在则新增
            Boolean autoDown = Boolean.FALSE;
            if (existsProduct == null) {
                // 保存产品-主信息表
                productDetail.setIsModified(ModifiedEnum.NO.getCode());
                productDetail.setUpStatus(UpStatusEnum.TOBE_UP.getCode());
                productDetail.setCreatedTime(now);
                productDetailMapperService.save(productDetail);

                // 保存产品-组件-扩展信息表
                module.setCreatedTime(now);
                productStorageMapperService.save(module);

                // 保存产品-组件-功率表（此表数据，只有新的productId刚进入时才会往这张表操作数据，更新逻辑不在此同步流程里）
//                productModulePowerMapperService.saveBatch(splitOutputPower(module, now));
            } else {
                // 更新产品-主信息表
                updateProductDetail(productDetail);
                // 更新产品-组件-扩展信息表
                LambdaQueryWrapper<ProductStoragePO> wrapper = Wrappers.<ProductStoragePO>lambdaQuery().eq(ProductStoragePO::getProductId, module.getProductId());
                productStorageMapperService.update(module, wrapper);
                // 如果产品状态为EOM、EOS则自动下架
//                autoDown = autoDown(req, now);
            }

            // 产品族不存在，则新增产品族
            productFamilyMapperService.saveWhenNotExist(req.getProductName(), productDetail.getProductCategory(), now);

            // 更新同步状态为已完成
            updateSyncStatusToCompleted(productDetailPbi.getId(), now);
        }else{
            // 1.校验同步状态；2.校验产品状态；3.校验产品更新时间。(1未通过则结束流程；2、3未通过，则更新同步状态为已取消后结束流程)
            ProductDetailPO existsProduct = productDetailMapperService.getByProductId(productDetailPbi.getProductId());
            SyncProductReqDTO req = JacksonUtil.json2Bean(productDetailPbi.getRawData(), SyncProductReqDTO.class);
            if (!check(productDetailPbi, existsProduct, req, syncOrderNo)) {
                return;
            }

            LocalDateTime now = LocalDateTime.now();

            // 字段映射，计算是否允许上架
            Pair<ProductDetailPO, ProductModulePO> pair = convert(req);
            ProductDetailPO productDetail = pair.getKey();
            productDetail.setUpdatedTime(now);
            ProductModulePO module = pair.getValue();
            module.setUpdatedTime(now);

            // 产品存在则更新，不存在则新增
            Boolean autoDown = Boolean.FALSE;
            if (existsProduct == null) {
                // 保存产品-主信息表
                productDetail.setIsModified(ModifiedEnum.NO.getCode());
                productDetail.setUpStatus(calcUpStatusForNew(req.getStatus()));
                productDetail.setCreatedTime(now);
                productDetailMapperService.save(productDetail);

                // 保存产品-组件-扩展信息表
                module.setCreatedTime(now);
                productModuleMapperService.save(module);

                // 保存产品-组件-功率表（此表数据，只有新的productId刚进入时才会往这张表操作数据，更新逻辑不在此同步流程里）
                productModulePowerMapperService.saveBatch(splitOutputPower(module, now));
            } else {
                // 更新产品-主信息表
                updateProductDetail(productDetail);
                // 更新产品-组件-扩展信息表
                updateProductModule(module);
                // 如果产品状态为EOM、EOS则自动下架
                autoDown = autoDown(req, now);
            }

            // 产品族不存在，则新增产品族
            productFamilyMapperService.saveWhenNotExist(req.getCodeName(), productDetail.getProductCategory(), now);

            // 更新同步状态为已完成
            updateSyncStatusToCompleted(productDetailPbi.getId(), now);

            // 发送同步产品完成MQ（如果产品是首次到达NPI_P、ACTIVE状态，则给产品管理员发送站内信）
            SyncProductCompletedMqDTO completedMq = new SyncProductCompletedMqDTO();
            completedMq.setSyncOrderNo(syncOrderNo);
            completedMq.setProductId(productDetail.getProductId());
            completedMq.setProductName(productDetail.getProductName());
            completedMq.setProductStatus(productDetail.getProductStatus());
            completedMq.setFirstActive(isFirstActive(existsProduct, req));
            completedMq.setAutoDown(autoDown);
            //mqManager.sendTopic(MasterdataTopic.SYNC_PRODUCT_COMPLETED, JacksonUtil.bean2Json(completedMq), true);
            log.info("同步产品发送完成MQ，syncOrderNo={}，mq={}", syncOrderNo, JacksonUtil.bean2Json(completedMq));
        }
    }

    private Pair<ProductDetailPO, ProductStoragePO> convertStorage(SyncStorageProductReqDTO req) {
        Map<String, String> props = req.getProps();

        ProductDetailPO productDetail = new ProductDetailPO();
        productDetail.setProductId(req.getProductName());
        productDetail.setProductName(req.getProductName());
        productDetail.setRegions(req.getSalesArea());
//        productDetail.setCountry("");
        productDetail.setProductVersion("1.0");
        productDetail.setProductStatus(req.getProductStatus());
        productDetail.setProductSeries(req.getsProductSeries());
        productDetail.setIsGlobal("false");
//        productDetail.setIsLatest(StrUtil.isNotEmpty(req.getIsLatest())?Integer.valueOf(req.getIsLatest()):null);
//        productDetail.setProductManager(req.getProductManager());
        productDetail.setProductCategory(ProductCategoryEnum.STORAGE.getCode());
        productDetail.setProductFamily(req.getNumber());
        productDetail.setDescriptionShort(req.getBriefIntroduction());
//        productDetail.setDescriptionLong(MapUtil.getStr(props, ProductPropsEm.DESCRIPTION_LONG.getCode()));
        productDetail.setPreActiveTime(LocalDateTime.now());
        productDetail.setPreEopTime(LocalDateTime.now());
        productDetail.setProductUpdateTime(LocalDateTime.now());
        productDetail.setIsModified(ModifiedEnum.NO.getCode());
        productDetail.setIsAbleUp(calcStorageAbleUp(req.getProductStatus()));

        ProductStoragePO module = JSONObject.parseObject(JSONObject.toJSONString(req.getProps()),ProductStoragePO.class);
        module.setApplication(req.getApplication());
        module.setSProductType(req.getsProductType());
        module.setProductId(req.getProductName());
        module.setCreatedTime(LocalDateTime.now());
        module.setUpdatedTime(LocalDateTime.now());
        return new Pair(productDetail, module);
    }

    private Integer calcStorageAbleUp(String storageStatus) {
        return ProductEms.AbleUpEm.TRUE.getCode();
    }

    private boolean checkStorage(ProductDetailPbiPO productDetailPbi, ProductDetailPO existsProduct, SyncStorageProductReqDTO req, String syncOrderNo) {
        if(!StrUtil.equalsAny(productDetailPbi.getProductId(),"蓝海 Gen 1.0","蓝海 Gen 2.0")){
            return false;
        }
        return true;
    }

    /**
     * 如果产品状态为EOM、EOS则自动下架
     */
    private Boolean autoDown(SyncProductReqDTO req, LocalDateTime now) {
        if (!StrUtil.equalsAny(req.getStatus(), PbiProductStatusEnum.EOM.getCode(), PbiProductStatusEnum.EOS.getCode())) {
            return Boolean.FALSE;
        }

        productManager.downProduct(CollectionUtil.newArrayList(req.getProductId()), now);
        return Boolean.TRUE;
    }

    private String calcUpStatusForNew(String productStatus) {
        if (StrUtil.equalsAny(productStatus, PbiProductStatusEnum.EOM.getCode(), PbiProductStatusEnum.EOS.getCode())) {
            return UpStatusEnum.DOWN.getCode();
        }
        return UpStatusEnum.TOBE_UP.getCode();
    }

    private boolean check(ProductDetailPbiPO productDetailPbi, ProductDetailPO existsProduct, SyncProductReqDTO req, String syncOrderNo) {
        // 1.校验同步状态
        if (!SyncStatusEm.INIT.equalsCode(productDetailPbi.getSyncStatus())) {
            return false;
        }

        // 2.状态为NA、NPI-D的产品不接受同步
        if (StrUtil.equalsAny(req.getStatus(), PbiProductStatusEnum.NA.getCode(), PbiProductStatusEnum.NPI_D.getCode())) {
            updateSyncStatusToCanceled(productDetailPbi.getId(), LocalDateTime.now());
            log.info("同步产品取消-PBI产品状态，syncOrderNo={}，PBI产品状态={}", syncOrderNo, req.getStatus());
            return false;
        }

        // 3.如果本次同步请求的产品更新时间不比数据库里的晚，则放弃更新
        if (existsProduct != null
                && !req.getUpdateTime().isAfter(existsProduct.getProductUpdateTime())) {
            updateSyncStatusToCanceled(productDetailPbi.getId(), LocalDateTime.now());
            log.info("同步产品取消-PBI更新时间，syncOrderNo={}，productUpdateTime={}", syncOrderNo, existsProduct.getProductUpdateTime());
            return false;
        }
        return true;
    }

    /**
     * 更新同步状态为已取消
     */
    private void updateSyncStatusToCanceled(Long id, LocalDateTime time) {
        updateSyncStatus(id, SyncStatusEm.CANCELED, time);
    }

    /**
     * 更新同步状态为已完成
     */
    private void updateSyncStatusToCompleted(Long id, LocalDateTime time) {
        updateSyncStatus(id, SyncStatusEm.COMPLETED, time);
    }

    /**
     * 更新同步状态
     */
    private void updateSyncStatus(Long id, SyncStatusEm syncStatusEm, LocalDateTime time) {
        ProductDetailPbiPO updateStatus = new ProductDetailPbiPO();
        updateStatus.setId(id);
        updateStatus.setSyncStatus(syncStatusEm.getCode());
        updateStatus.setUpdatedTime(time);
        productDetailPbiMapperService.updateById(updateStatus);
    }

    /**
     * 判断产品是否是首次到达NPI_P、ACTIVE状态
     * 如果产品状态已经是NPI_P，再更新成ACTIVE，也不算首次到达。
     */
    private boolean isFirstActive(ProductDetailPO existsProduct, SyncProductReqDTO req) {
        if (!StrUtil.equalsAny(req.getStatus(), PbiProductStatusEnum.NPI_P.getCode(), PbiProductStatusEnum.ACTIVE.getCode())) {
            return false;
        }

        if (!(existsProduct == null || StrUtil.equalsAny(existsProduct.getProductStatus(),
                PbiProductStatusEnum.NA.getCode(), PbiProductStatusEnum.NPI_D.getCode()))) {
            return false;
        }
        return true;
    }

    /**
     * 拆分输出功率（组件功率）
     * @param module
     * @return
     */
    private List<ProductModulePowerPO> splitOutputPower(ProductModulePO module, LocalDateTime now) {
        String outputPower = module.getOutputPower();
        String[] outputPowerList = outputPower.split("~");
        Long minOutputPower = Long.valueOf(outputPowerList[0]);
        Long maxOutputPower = Long.valueOf(outputPowerList[1]);
        String produduId = module.getProductId();

        Long currentOutputPower = minOutputPower;
        List<ProductModulePowerPO> modulePowerList = new ArrayList<>();
        while (currentOutputPower <= maxOutputPower) {
            ProductModulePowerPO modulePower = buildProductModulePower(produduId, currentOutputPower + "", now);
            modulePowerList.add(modulePower);

            currentOutputPower = currentOutputPower + 5;
        }

        if (currentOutputPower - 5 != maxOutputPower) {
            ProductModulePowerPO modulePower = buildProductModulePower(produduId, maxOutputPower + "", now);
            modulePowerList.add(modulePower);
        }
        return modulePowerList;
    }

    private ProductModulePowerPO buildProductModulePower(String productId, String outputPower, LocalDateTime now) {
        ProductModulePowerPO minModulePower = new ProductModulePowerPO();
        minModulePower.setProductId(productId);
        minModulePower.setOutputPower(outputPower);
        minModulePower.setHardToGet(HardToGetEnum.UN_HARD.getCode());
        minModulePower.setUpStatus(ModulePowerUpStatusEnum.DOWN.getCode());
        minModulePower.setUpTime(now);
        minModulePower.setCreatedTime(now);
        minModulePower.setUpdatedTime(now);
        return minModulePower;
    }

    private void updateProductDetail(ProductDetailPO productDetail) {
        LambdaQueryWrapper<ProductDetailPO> wrapper = Wrappers.<ProductDetailPO>lambdaQuery().eq(ProductDetailPO::getProductId, productDetail.getProductId());
        productDetailMapperService.update(productDetail, wrapper);
    }

    private void updateProductModule(ProductModulePO module) {
        LambdaQueryWrapper<ProductModulePO> wrapper = Wrappers.<ProductModulePO>lambdaQuery().eq(ProductModulePO::getProductId, module.getProductId());
        productModuleMapperService.update(module, wrapper);
    }

    private Pair<ProductDetailPO, ProductModulePO> convert(SyncProductReqDTO req) {
        Map<String, String> props = req.getProps();

        ProductDetailPO productDetail = new ProductDetailPO();
        productDetail.setProductId(req.getProductId());
        productDetail.setProductName(req.getCodeName());
        productDetail.setRegions(MapUtil.getStr(props, ProductPropsEm.REGIONS.getCode()));
        productDetail.setCountry(req.getCountry());
        productDetail.setProductVersion(req.getProductVersion());
        productDetail.setProductStatus(req.getStatus());
        productDetail.setProductSeries(req.getProductSeries());
        productDetail.setIsGlobal(req.getIsGlobal());
        productDetail.setIsLatest(StrUtil.isNotEmpty(req.getIsLatest())?Integer.valueOf(req.getIsLatest()):null);
        productDetail.setProductManager(req.getProductManager());
        productDetail.setProductCategory(ProductCategoryEnum.MODULE.getCode());
        productDetail.setProductFamily(MapUtil.getStr(props, ProductPropsEm.PRODUCT_FAMILY.getCode()));
        productDetail.setDescriptionShort(MapUtil.getStr(props, ProductPropsEm.DESCRIPTION_SHORT.getCode()));
        productDetail.setDescriptionLong(MapUtil.getStr(props, ProductPropsEm.DESCRIPTION_LONG.getCode()));
        productDetail.setPreActiveTime(LocalDateTime.now());
        productDetail.setPreEopTime(LocalDateTime.now());
        productDetail.setProductUpdateTime(req.getUpdateTime());
        productDetail.setIsAbleUp(calcAbleUp(req.getStatus()));

        ProductModulePO module = new ProductModulePO();
        module.setProductId(req.getProductId());
        module.setMaxSystemVoltage(MapUtil.getStr(props, ProductPropsEm.MAX_SYSTEM_VOLTAGE.getCode()));
        module.setModuleType(MapUtil.getStr(props, ProductPropsEm.MODULE_TYPE.getCode()));
        module.setProductStructure(MapUtil.getStr(props, ProductPropsEm.PRODUCT_STRUCTURE.getCode()));
        module.setCellQuantity(MapUtil.getStr(props, ProductPropsEm.CELL_QUANTITY.getCode()));
        module.setBusBar(MapUtil.getStr(props, ProductPropsEm.BUS_BAR.getCode()));
        module.setDiodesNumber(MapUtil.getStr(props, ProductPropsEm.TOTAL_NUMBER_OF_DIODES.getCode()));
        module.setModuleWeight(getBigDecimal(MapUtil.getStr(props, ProductPropsEm.MODULE_WEIGHT.getCode())));
        module.setModuleLength(getBigDecimal(MapUtil.getStr(props, ProductPropsEm.MODULE_LENGTH.getCode())));
        module.setModuleWidth(getBigDecimal(MapUtil.getStr(props, ProductPropsEm.MODULE_WIDTH.getCode())));
        module.setBacksheet(MapUtil.getStr(props, ProductPropsEm.BACKSHEET.getCode()));
        module.setModuleColor(MapUtil.getStr(props, ProductPropsEm.MODULE_COLOR.getCode()));
        module.setRearSideColor(MapUtil.getStr(props, ProductPropsEm.COLOR_REAR_SIDE.getCode()));
        module.setFrame(MapUtil.getStr(props, ProductPropsEm.FRAME.getCode()));
        module.setFrameThickness(MapUtil.getInt(props, ProductPropsEm.FRAME_THICKNESS.getCode()));
        module.setFrameMaterial(MapUtil.getStr(props, ProductPropsEm.FRAME_MATERIAL.getCode()));
        module.setFrameColor(MapUtil.getStr(props, ProductPropsEm.FRAME_COLOR.getCode()));
        module.setCableLengthLandscape(MapUtil.getStr(props, ProductPropsEm.CABLE_LENGTH_LANDSCAPE.getCode()));
        module.setCableLengthPortrait(MapUtil.getStr(props, ProductPropsEm.CABLE_LENGTH_PORTRAIT.getCode()));
        module.setCableLengthPortraitPositive(getCableLengthPortraitPositive(module.getCableLengthPortrait()));
        module.setCableLengthPortraitCathode(getCableLengthPortraitCathode(module.getCableLengthPortrait()));
        module.setPlugConnector(MapUtil.getStr(props, ProductPropsEm.PLUG_CONNECTOR.getCode()));
        module.setComponentEfficiency(getBigDecimal(MapUtil.getStr(props, ProductPropsEm.MODULE_EFFICIENCY_RANGE.getCode())));
        module.setOutputPower(MapUtil.getStr(props, ProductPropsEm.POWER_OUTPUT_RANGE.getCode()));
        module.setApplication(MapUtil.getStr(props, ProductPropsEm.APPLICATION.getCode()));
        module.setWaferSize(MapUtil.getStr(props, ProductPropsEm.WAFER_SIZE.getCode()));
        return new Pair(productDetail, module);
    }

    /**
     * 计算是否允许上架
     * 同步的产品状态为ACTIVE则允许上架，否则不允许上架
     */
    private Integer calcAbleUp(String productStatus) {
        if (StrUtil.equalsAny(productStatus, PbiProductStatusEnum.ACTIVE.getCode())) {
            return ProductEms.AbleUpEm.TRUE.getCode();
        }
        return ProductEms.AbleUpEm.FALSE.getCode();
    }

    private BigDecimal getCableLengthPortraitPositive(String cableLengthPortrait) {
        BigDecimal positive = getBigDecimal(cableLengthPortrait, "/", 0);
        if (positive == null) {
            return null;
        }
        return positive.divide(THOUSAND);
    }

    private BigDecimal getCableLengthPortraitCathode(String cableLengthPortrait) {
        BigDecimal cathode = getBigDecimal(cableLengthPortrait, "/", 1);
        if (cathode == null) {
            return null;
        }
        return cathode.divide(THOUSAND);
    }

    private Long getLong(String originalVal) {
        if (StrUtil.isEmpty(originalVal)) {
            return null;
        }
        return Long.valueOf(originalVal.split(",")[0]);
    }

    public static void main(String[] args) {

    }

    private BigDecimal getBigDecimal(String originalVal) {
        return getBigDecimal(originalVal, ",", 0);
    }

    private BigDecimal getBigDecimal(String originalVal, String regex, int index) {
        if (StrUtil.isEmpty(originalVal)) {
            return null;
        }

        String[] arrs = originalVal.split(regex);
        if (arrs.length > index) {
            return new BigDecimal(originalVal.split(regex)[index]);
        }
        return null;
    }

}
