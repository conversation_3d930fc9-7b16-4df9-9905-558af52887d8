package com.trinasolar.trinax.masterdata.service.biz;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.trinasolar.trinax.common.exception.BizWithDataException;
import com.trinasolar.trinax.masterdata.constants.ProductResultCode;
import com.trinasolar.trinax.masterdata.constants.UpStatusEnum;
import com.trinasolar.trinax.masterdata.dto.input.ProductShelfReqDTO;
import com.trinasolar.trinax.masterdata.repository.atomicservice.ProductDetailMapperService;
import com.trinasolar.trinax.masterdata.repository.mapper.ProductDetailMapper;
import com.trinasolar.trinax.masterdata.repository.mapper.ProductModulePowerMapper;
import com.trinasolar.trinax.masterdata.repository.po.ProductDetailPO;
import com.trinasolar.trinax.masterdata.repository.po.ProductModulePowerPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 批量下架商品
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BatchOffShelfProductStorage {

    private final ProductDetailMapperService productDetailMapperService;
    private final ProductDetailMapper productDetailMapper;
    private final ProductModulePowerMapper productModulePowerMapper;

    @Transactional
    public List<String> execute(ProductShelfReqDTO req) {
        List<String> productIds = req.getProductIds();
        // 校验 1）产品状态是否为“PART_UP”或“UP”；
        check(productIds);

        // 1.更新所有的产品为的上架状态为”DOWN“
        LocalDateTime now = LocalDateTime.now();
        LambdaUpdateWrapper<ProductDetailPO> productWrapper = new LambdaUpdateWrapper<ProductDetailPO>()
                .set(ProductDetailPO::getUpdatedTime, now)
                .set(ProductDetailPO::getUpStatus, UpStatusEnum.DOWN.getCode())
                .set(ProductDetailPO::getDownTime, now)
                .in(ProductDetailPO::getProductId, productIds);
        productDetailMapper.update(null, productWrapper);

        // 2.更新所有的产品功率为的上架状态为”DOWN“
        LambdaUpdateWrapper<ProductModulePowerPO> powerWrapper = new LambdaUpdateWrapper<ProductModulePowerPO>()
                .set(ProductModulePowerPO::getUpStatus, UpStatusEnum.DOWN.getCode())
                .set(ProductModulePowerPO::getDownTime, now)
                .set(ProductModulePowerPO::getUpdatedTime, now)
                .in(ProductModulePowerPO::getProductId, productIds);
        productModulePowerMapper.update(null, powerWrapper);
        return new ArrayList<>();
    }

    /**
     * 1.产品状态是否为“PART_UP”或“UP”；
     */
    private void check(List<String> productIds) {
        List<ProductDetailPO> productDetailList = productDetailMapperService.lambdaQuery()
                .in(ProductDetailPO::getProductId, productIds)
                .in(ProductDetailPO::getUpStatus, CollectionUtil.newArrayList(UpStatusEnum.UP.getCode()))
                .list();

        if (productDetailList.size() < productIds.size()) {
            Map<String, String> productIdMap = productDetailList.stream()
                    .collect(Collectors.toMap(ProductDetailPO::getProductId, ProductDetailPO::getProductId, (oldVal, newVal) -> newVal));
            List<String> invalidProductIds = new ArrayList<>();
            for (String productId:productIds) {
                if (!productIdMap.containsKey(productId)) {
                    invalidProductIds.add(productId);
                }
            }
            throw new BizWithDataException(ProductResultCode.OFF_NOT_VALID.getCode(), "部分行不可执行该操作，请检查后再执行", invalidProductIds);
        }
    }

}