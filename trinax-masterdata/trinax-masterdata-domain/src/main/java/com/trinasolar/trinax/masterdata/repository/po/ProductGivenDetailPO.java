package com.trinasolar.trinax.masterdata.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName(value = "product_given_detail")
public class ProductGivenDetailPO {

    @TableId(type = IdType.AUTO)
    /** 自增主键 */
    private Long id;

    /** 备件数据来源 */
    private String dataSource;

    /** 备件ID */
    private String givenId;

    /** 备件名称 */
    private String name;

    /** 备件状态 */
    private String givenStatus;

    /** 启用时间 */
    private LocalDateTime enableTime;

    /** 停用时间 */
    private LocalDateTime disableTime;

    /** 备件描述 */
    private String description;

    /** 是否已删除;1：已删除 0：未删除 */
    private Integer isDeleted;

    /** 创建人 */
    private String createdBy;

    /** 创建人姓名 */
    private String createdName;

    /** 创建时间 */
    private LocalDateTime createdTime;

    /** 修改人 */
    private String updatedBy;

    /** 更新人姓名 */
    private String updatedName;

    /** 更新时间 */
    private LocalDateTime updatedTime;

}