package com.trinasolar.trinax.masterdata.service;

import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.masterdata.dto.input.*;
import com.trinasolar.trinax.masterdata.dto.output.ProductGivenDetailPageResDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductGivenDetailResDTO;

public interface ProductGivenDetailService {

    /**
     * 新增备件
     */
    String add(ProductGivenDetailAddReqDTO req);

    /**
     * 删除备件
     */
    void delete(String givenId);

    /**
     * 分页查询备件
     */
    PageResponse<ProductGivenDetailPageResDTO> page(PageRequest<ProductGivenDetailPageReqDTO> req);

    /**
     * 查询备件详情
     */
    ProductGivenDetailResDTO detail(String givenId);

    /**
     * 修改备件
     */
    void edit(ProductGivenDetailEditReqDTO req);

    /**
     * 启用备件
     */
    void enable(EnableGivenDetailReqDTO req);

    /**
     * 停用备件
     */
    void disable(DisableGivenDetailReqDTO req);

}
