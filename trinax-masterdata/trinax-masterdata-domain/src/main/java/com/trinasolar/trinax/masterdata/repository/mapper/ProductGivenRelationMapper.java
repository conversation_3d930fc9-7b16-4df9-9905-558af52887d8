package com.trinasolar.trinax.masterdata.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.masterdata.dto.input.ProductGivenRelationPageReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductGivenRelationPageResDTO;
import com.trinasolar.trinax.masterdata.repository.dto.ProductGivenInfoDTO;
import com.trinasolar.trinax.masterdata.repository.po.ProductGivenRelationPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductGivenRelationMapper extends BaseMapper<ProductGivenRelationPO> {

    List<ProductGivenInfoDTO> getGivenInfo(@Param("allProductId") List<String> allProductId,
                                           @Param("moduleGivenType") String moduleGivenType,
                                           @Param("partGivenType") String partGivenType);

    IPage<ProductGivenRelationPageResDTO> customPage(Page<ProductGivenRelationPageResDTO> page, @Param("query") ProductGivenRelationPageReqDTO query);

}
