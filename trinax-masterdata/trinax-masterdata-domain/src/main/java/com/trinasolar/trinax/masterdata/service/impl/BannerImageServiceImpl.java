package com.trinasolar.trinax.masterdata.service.impl;

import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.masterdata.constants.ProductResultCode;
import com.trinasolar.trinax.masterdata.dto.input.BannerImageEnumReqDTO;
import com.trinasolar.trinax.masterdata.dto.input.BannerImageQueryReqDTO;
import com.trinasolar.trinax.masterdata.dto.input.BannerImageSaveReqDTO;
import com.trinasolar.trinax.masterdata.dto.input.BannerImageUpdateReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.BannerImageEnumResDTO;
import com.trinasolar.trinax.masterdata.dto.output.BannerImageResDTO;
import com.trinasolar.trinax.masterdata.repository.atomicservice.BannerImageMapperService;
import com.trinasolar.trinax.masterdata.repository.po.BannerImagePO;
import com.trinasolar.trinax.masterdata.service.BannerImageService;
import com.trinasolar.trinax.masterdata.service.biz.BannerImageEnumBiz;
import com.trinasolar.trinax.masterdata.service.biz.banner.BannerImageQueryBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * DESC：
 *
 * <AUTHOR>
 * @since 2023/11/06 18:18
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class BannerImageServiceImpl implements BannerImageService {

    private final BannerImageMapperService bannerImageMapperService;
    private final BannerImageEnumBiz bannerImageEnumBiz;
    private final BannerImageQueryBiz bannerImageQueryBiz;

    @Override
    public List<BannerImageResDTO> list(BannerImageQueryReqDTO reqDTO) {
        return bannerImageQueryBiz.list(reqDTO);
    }

    @Override
    public PageResponse<BannerImageResDTO> listPage(PageRequest<BannerImageQueryReqDTO> reqDTO) {
        return bannerImageQueryBiz.listPage(reqDTO);
    }


    @Override
    public Long save(BannerImageSaveReqDTO reqDTO) {
        BannerImagePO entity = new BannerImagePO();
        BeanUtils.copyProperties(reqDTO, entity);
        LocalDateTime now = LocalDateTime.now();
        entity.setIsDeleted(0);
        entity.setCreatedTime(now);
        entity.setUpdatedTime(now);
        bannerImageMapperService.save(entity);
        return entity.getId();
    }

    @Override
    public Boolean update(BannerImageUpdateReqDTO reqDTO) {
        BannerImagePO imagePO = bannerImageMapperService.getById(reqDTO.getId());
        if (Objects.isNull(imagePO)) {
            throw new BizException(ProductResultCode.BANNERIMAGE_VALIDATE_FAILURE.getCode(), "BannerImage不存在");
        }

        BeanUtils.copyProperties(reqDTO, imagePO);
        imagePO.setUpdatedTime(LocalDateTime.now());
        return bannerImageMapperService.updateById(imagePO);
    }

    @Override
    public Boolean remove(Long id) {
        BannerImagePO imagePO = bannerImageMapperService.getById(id);
        if (Objects.isNull(imagePO)) {
            throw new BizException(ProductResultCode.BANNERIMAGE_VALIDATE_FAILURE.getCode(), "BannerImage不存在");
        }

        imagePO.setIsDeleted(1);
        imagePO.setUpdatedTime(LocalDateTime.now());
        return bannerImageMapperService.updateById(imagePO);
    }

    @Override
    public Map<String, List<BannerImageEnumResDTO>> bannerImageEnumList(BannerImageEnumReqDTO req) {
        return bannerImageEnumBiz.bannerImageEnumList(req);

    }
}
