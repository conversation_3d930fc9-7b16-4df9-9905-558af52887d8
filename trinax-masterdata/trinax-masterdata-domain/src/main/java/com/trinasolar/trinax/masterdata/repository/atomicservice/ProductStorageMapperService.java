package com.trinasolar.trinax.masterdata.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.trinax.masterdata.repository.po.ProductModulePO;
import com.trinasolar.trinax.masterdata.repository.po.ProductStoragePO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * created on 2023/10/20
 */
public interface ProductStorageMapperService extends IService<ProductStoragePO> {


    Map<String, ProductStoragePO> mapByProductId(List<String> productIds);
}
