package com.trinasolar.trinax.masterdata.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.masterdata.constants.enums.EnableEnum;
import com.trinasolar.trinax.masterdata.constants.enums.ProductEms;
import com.trinasolar.trinax.masterdata.repository.atomicservice.ProductGivenRelationMapperService;
import com.trinasolar.trinax.masterdata.repository.dto.ProductGivenInfoDTO;
import com.trinasolar.trinax.masterdata.repository.mapper.ProductGivenRelationMapper;
import com.trinasolar.trinax.masterdata.repository.po.ProductGivenRelationPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;


@Service
public class ProductGivenRelationMapperServiceImpl extends ServiceImpl<ProductGivenRelationMapper, ProductGivenRelationPO> implements ProductGivenRelationMapperService {

    @Autowired
    ProductGivenRelationMapper productGivenRelationMapper;

    @Override
    public List<ProductGivenInfoDTO> getGivenInfo(List<String> allProductId) {
        return productGivenRelationMapper.getGivenInfo(allProductId,
                ProductEms.ProductGivenTypeEm.MODULE.getCode(), ProductEms.ProductGivenTypeEm.PART.getCode());
    }

    @Override
    public void updateRelationStatus(Long id, String relationStatus, String updatedBy, String updatedName, LocalDateTime currentTime) {
        this.lambdaUpdate()
                .set(ProductGivenRelationPO::getRelationStatus, relationStatus)
                .set(EnableEnum.ENABLE.equalsCode(relationStatus), ProductGivenRelationPO::getEnableTime, currentTime)
                .set(EnableEnum.DISABLE.equalsCode(relationStatus), ProductGivenRelationPO::getDisableTime, currentTime)
                .set(ProductGivenRelationPO::getUpdatedTime, currentTime)
                .set(ProductGivenRelationPO::getUpdatedBy, updatedBy)
                .set(ProductGivenRelationPO::getUpdatedName, updatedName)
                .eq(ProductGivenRelationPO::getId, id)
                .update();
    }

}
