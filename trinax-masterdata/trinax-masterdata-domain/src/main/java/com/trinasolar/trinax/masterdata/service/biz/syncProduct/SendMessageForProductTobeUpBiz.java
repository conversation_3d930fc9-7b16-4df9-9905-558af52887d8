package com.trinasolar.trinax.masterdata.service.biz.syncProduct;

import cn.hutool.core.collection.CollectionUtil;
import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.messagetemplate.MessageTemplateNoticeTypeEnum;
import com.trinasolar.trinax.basic.dto.input.MessageSendCommonReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendNoticeReqDTO;
import com.trinasolar.trinax.basic.event.MessageBizCodeEnum;
import com.trinasolar.trinax.basic.event.MessageTypeEnum;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.masterdata.dto.mq.SyncProductCompletedMqDTO;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class SendMessageForProductTobeUpBiz {

    @Value("${message.role-id.sync.product.completed:1}")
    private String roleId;

    @Value("${message.templateCode.upNotice:PBI_PRODUCT_UP_NOTICE}")
    private String upTemplateCode;

    @Value("${message.templateCode.downNotice:PBI_PRODUCT_DOWN_NOTICE}")
    private String downTemplateCode;


    private final SysUserFeign sysUserFeign;
    private final MqManager mqManager;

    public void execute(SyncProductCompletedMqDTO req) {
        if (!req.getFirstActive()) {
            return;
        }
        String syncOrderNo = req.getSyncOrderNo();

        log.info("发送产品待上架提醒开始，syncOrderNo={}，roleId={}", syncOrderNo, roleId);
        Result<List<SysUserRespDTO>> result = sysUserFeign.getUsersByRoleId(roleId);
        if (!result.getSuccess()) {
            log.error("发送产品待上架提醒，根据角色查询用户失败，syncOrderNo={}，result={}", syncOrderNo, JacksonUtil.bean2Json(result));
            throw new RuntimeException("根据角色查询用户失败");
        }

        List<SysUserRespDTO> users = result.getData();
        if (CollectionUtil.isEmpty(users)) {
            log.info("发送产品待上架提醒放弃，用户列表为空");
            return;
        }

        MessageSendCommonReqDTO noticeMessageReqDTO = new MessageSendCommonReqDTO();
        noticeMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.NOTICE.getCode());
        List<MessageSendNoticeReqDTO> noticeList = new ArrayList<>();

        Map<String,String> content = new HashMap<>();
        content.put("productName",req.getProductName());
        content.put("status",req.getProductStatus());
        List<String> userIdList = users.stream().map(SysUserRespDTO::getUserId).distinct().collect(Collectors.toList());
        if(Boolean.TRUE.equals(req.getFirstActive())){
            MessageSendNoticeReqDTO noticeReqDTO = generateNotice(req.getProductId(),
                    "", MessageBizCodeEnum.PRODUCT_UP.getValue(), upTemplateCode,userIdList,content);
            noticeList.add(noticeReqDTO);
        }
        if(req.getAutoDown()){
            MessageSendNoticeReqDTO noticeReqDTO = generateNotice(req.getProductId(),
                    "",MessageBizCodeEnum.PRODUCT_DOWN.getValue(), downTemplateCode,userIdList,content);
            noticeList.add(noticeReqDTO);
        }
        if(!ObjectUtils.isEmpty(noticeList)){
            noticeMessageReqDTO.setNoticeList(noticeList);
            mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC,JacksonUtil.bean2Json(noticeMessageReqDTO));
            log.info("发送产品待上下架提醒完成，syncOrderNo={},消息内容:{}",syncOrderNo,JacksonUtil.bean2Json(noticeMessageReqDTO));
        }
    }


    /**
     * 组装通用参数
     * @param bizNo
     * @param opType
     * @param templateCode
     * @param userIdList
     * @param content
     * @return
     */
    private MessageSendNoticeReqDTO generateNotice(String bizNo, String opType,String bizCode, String templateCode,
                                                   List<String>userIdList, Map<String,String> content) {
        MessageSendNoticeReqDTO noticeReqDTO = new MessageSendNoticeReqDTO();
        noticeReqDTO.setMessageType(MessageTypeEnum.MSG_TYPE_PRIVATE.getValue());
        noticeReqDTO.setOpType(opType);
        noticeReqDTO.setBizCode(bizCode);
        noticeReqDTO.setTemplateCode(templateCode);
        noticeReqDTO.setUserIdList(userIdList);
        noticeReqDTO.setBizNo(bizNo);
        noticeReqDTO.setContent(content);
        return noticeReqDTO;
    }

}
