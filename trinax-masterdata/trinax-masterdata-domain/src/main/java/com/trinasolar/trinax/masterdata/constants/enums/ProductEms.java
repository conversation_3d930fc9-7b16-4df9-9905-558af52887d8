package com.trinasolar.trinax.masterdata.constants.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

public interface ProductEms {

    /**
     * 产品同步状态
     */
    @RequiredArgsConstructor
    public enum SyncStatusEm {
        INIT("INIT", "待同步"),
        COMPLETED("COMPLETED", "已完成"),
        CANCELED("CANCELED", "已取消"),
        ;

        @Getter
        private final String code;
        @Getter
        private final String desc;

        public boolean equalsCode(String code) {
            return this.code.equals(code);
        }
    }

    /**
     * 产品属性
     */
    @RequiredArgsConstructor
    public enum ProductPropsEm {
        MAX_SYSTEM_VOLTAGE("max.SystemVoltage", "最大系统电压"),
        MODULE_EFFICIENCY_RANGE("moduleEfficiencyRange", "组件最小效率"),
        MODULE_TYPE("moduleType", "产品类型（组件类型）"),
        PRODUCT_STRUCTURE("moduleStack", "产品结构"),
        PRODUCT_MANAGER("productManager", "产品经理"),
        PRODUCT_FAMILY("productFamily", "产品族"),
        REGIONS("regions", "销售区域（上市区域）"),
        DESCRIPTION_SHORT("descriptionShort", "产品描述（短）"),
        DESCRIPTION_LONG("descriptionLong", "产品描述（长）"),
        PRODUCT_SERIES("productSeries", "产品系列"),
        CELL_QUANTITY("cellQuantity", "电池片数量"),
        BUS_BAR("busBar", "主栅数"),
        TOTAL_NUMBER_OF_DIODES("totalNumberOfDiodes", "晶体管数量"),
        MODULE_WEIGHT("moduleWeight", "组件重量"),
        MODULE_LENGTH("moduleLength", "组件长"),
        MODULE_WIDTH("moduleWidth", "组件宽"),
        BACKSHEET("backsheet", "背板"),
        MODULE_COLOR("moduleColor", "组件颜色"),
        COLOR_REAR_SIDE("colorRearSide", "后侧颜色"),
        FRAME("frame", "Frame"),
        FRAME_THICKNESS("trameThickness", "边框厚度"),
        FRAME_MATERIAL("frameMaterial", "边框材料"),
        FRAME_COLOR("frameColor", "边框颜色"),
        PLUG_CONNECTOR("plugConnector", "连接器"),
        CABLE_LENGTH_LANDSCAPE("cableLengthLandscape", "线缆长度-非标"),
        CABLE_LENGTH_PORTRAIT("cableLengthPortrait", "线缆长度-竖装"),
        POWER_OUTPUT_RANGE("powerOutputRange", "输出功率"),
        APPLICATION("application", "应用场景"),
        WAFER_SIZE("waferSize", "电池片尺寸"),
        ;

        @Getter
        private final String code;
        @Getter
        private final String desc;

        public boolean equalsCode(String code) {
            return this.code.equals(code);
        }
    }

    @Getter
    @RequiredArgsConstructor
    enum ProductGivenTypeEm {
        MODULE("Module", "组件"),
        PART("Part", "备件");

        final String code;
        final String desc;

        public boolean equalsCode(String code) {
            return this.code.equals(code);
        }

        public static String getDescByCode(String code) {
            for (ProductGivenTypeEm em:values()) {
                if (em.equalsCode(code)) {
                    return em.getDesc();
                }
            }
            return null;
        }
    }

    /**
     *
     */
    @Getter
    @RequiredArgsConstructor
    enum GivenUnitEm {
        MWATT("MW", "兆瓦"),
        PIECE("P", "片/对"),
        NUM("N", "个");

        final String code;
        final String desc;

        public boolean equalsCode(String code) {
            return this.code.equals(code);
        }

        public static String getDescByCode(String code) {
            for (GivenUnitEm em:values()) {
                if (em.equalsCode(code)) {
                    return em.getDesc();
                }
            }
            return null;
        }
    }

    @Getter
    @RequiredArgsConstructor
    enum AbleUpEm {
        TRUE(1, "允许"),
        FALSE(0, "不允许");

        final Integer code;
        final String desc;

        public boolean equalsCode(Integer code) {
            return this.code.equals(code);
        }

        public static String getDescByCode(Integer code){
            for (AbleUpEm value : AbleUpEm.values()) {
                if (value.getCode().equals(code)){
                    return value.getDesc();
                }
            }
            return "";
        }
    }

    @Getter
    @RequiredArgsConstructor
    enum IsHotEm {
        TRUE(1, "是"),
        FALSE(0, "否");

        final Integer code;
        final String desc;

        public boolean equalsCode(Integer code) {
            return this.code.equals(code);
        }

        public static String getDescByCode(Integer code){
            for (IsHotEm value : IsHotEm.values()) {
                if (value.getCode().equals(code)){
                    return value.getDesc();
                }
            }
            return "";
        }
    }

    @Getter
    @RequiredArgsConstructor
    enum IsNewEm {
        TRUE(1, "是"),
        FALSE(0, "否");

        final Integer code;
        final String desc;

        public boolean equalsCode(Integer code) {
            return this.code.equals(code);
        }

        public static String getDescByCode(Integer code){
            for (IsNewEm value : IsNewEm.values()) {
                if (value.getCode().equals(code)){
                    return value.getDesc();
                }
            }
            return "";
        }
    }

    @Getter
    @RequiredArgsConstructor
    enum IsGlobalEm {
        TRUE("true", "是"),
        FALSE("false", "否");

        final String code;
        final String desc;

        public boolean equalsCode(Integer code) {
            return this.code.equals(String.valueOf(code));
        }

        public static String getDescByCode(String code){
            for (IsGlobalEm value : IsGlobalEm.values()) {
                if (value.getCode().equals(code)){
                    return value.getDesc();
                }
            }
            return "";
        }
    }

}
