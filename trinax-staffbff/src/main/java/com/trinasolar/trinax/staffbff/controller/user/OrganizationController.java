package com.trinasolar.trinax.staffbff.controller.user;

import com.trinasolar.trinax.auth.core.details.AuthUserDetails;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.log.behavior.support.BehaviorLog;
import com.trinasolar.trinax.user.api.SysOrganizationFeign;
import com.trinasolar.trinax.user.dto.input.SysOrganizationPageReqDTO;
import com.trinasolar.trinax.user.dto.input.SysOrganizationReqDTO;
import com.trinasolar.trinax.user.dto.output.SysAreaRespDTO;
import com.trinasolar.trinax.user.dto.output.SysOrganizationRespDTO;
import com.trinasolar.trinax.user.dto.output.SysOrganizationTreeRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

@RestController
@RequiredArgsConstructor
@Slf4j
@Tag(name = "组织管理接口")
@RequestMapping("/organization")
public class OrganizationController {

    private final SysOrganizationFeign organizationFeign;

    @Operation(summary = "查询区域列表")
    @GetMapping("/getAreasByCountry")
    Result<List<SysAreaRespDTO>> getAreasByCountry(@Parameter(description = "国家") @RequestParam("country") @NotBlank String country) {
        return organizationFeign.getAreasByCountry(country);
    }

    @BehaviorLog(ignore = false,ignoreReq = false,bizNo = "#result.data")
    @Operation(description = "创建或更新组织")
    @PostMapping("/createOrUpdate")
    Result<String> createOrUpdate(@RequestBody SysOrganizationReqDTO reqDTO) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        reqDTO.setUpdateBy(authUser.getUserIdStr());
        reqDTO.setUpdateName(authUser.getName());
        return organizationFeign.createOrUpdate(reqDTO);
    }

    @BehaviorLog(ignore = false,ignoreReq = false,bizNo = "#reqDTO.organizationCode")
    @Operation(description = "删除组织")
    @PostMapping("/delete")
    Result<String> delete(@RequestBody SysOrganizationReqDTO reqDTO) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        reqDTO.setUpdateBy(authUser.getUserIdStr());
        reqDTO.setUpdateName(authUser.getName());
        return organizationFeign.delete(reqDTO);
    }

    @Operation(description = "组织树查询")
    @PostMapping("/tree")
    Result<List<SysOrganizationTreeRespDTO>> getOrganizationTree() {
        return organizationFeign.getOrganizationTree();
    }

    @Operation(description = "分页查询组织")
    @PostMapping("/page")
    Result<PageResponse<SysOrganizationRespDTO>> getOrganizationPage(@RequestBody PageRequest<SysOrganizationPageReqDTO> request) {
        return organizationFeign.getOrganizationPage(request);
    }

    @Operation(description = "根据组织类型查询组织")
    @GetMapping("/getOrgListByType")
    Result<List<SysOrganizationRespDTO>> getOrgListByType(@RequestParam String organizationType) {
        return organizationFeign.getOrgListByType(organizationType);
    }
}
