package com.trinasolar.trinax.staffbff.controller.product;

import com.trinasolar.trinax.auth.core.details.AuthUserDetails;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.log.behavior.support.BehaviorLog;
import com.trinasolar.trinax.masterdata.api.ProductGivenDetailFeign;
import com.trinasolar.trinax.masterdata.dto.input.*;
import com.trinasolar.trinax.masterdata.dto.output.ProductGivenDetailPageResDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductGivenDetailResDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> Hongyi
 * created on 2024/01/03
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@Tag(name = "备件管理")
public class ProductGivenDetailController {

    private final ProductGivenDetailFeign productGivenDetailFeign;

    @Operation(summary = "新增备件")
    @PostMapping("/products/product-given-details/add")
    public Result<String> add(@RequestBody ProductGivenDetailAddReqDTO req) {
        AuthUserDetails user = AuthUserHelper.getAuthUser();
        req.setCreatedBy(user.getUserIdStr());
        req.setCreatedName(user.getName());
        return productGivenDetailFeign.add(req);
    }

    @Operation(summary = "删除备件")
    @GetMapping("/products/product-given-details/delete")
    public Result<Void> delete(@RequestParam(name = "givenId") String givenId) {
        return productGivenDetailFeign.delete(givenId);
    }

    @BehaviorLog(ignore = true)
    @Operation(summary = "分页查询备件")
    @PostMapping("/products/product-given-details/page")
    public Result<PageResponse<ProductGivenDetailPageResDTO>> page(@RequestBody PageRequest<ProductGivenDetailPageReqDTO> req) {
        return productGivenDetailFeign.page(req);
    }

    @Operation(summary = "查询备件详情")
    @GetMapping("/products/product-given-details/detail")
    Result<ProductGivenDetailResDTO> detail(@RequestParam(name = "givenId") String givenId) {
        return productGivenDetailFeign.detail(givenId);
    }

    @Operation(summary = "修改备件")
    @PostMapping("/products/product-given-details/edit")
    Result<Void> edit(@RequestBody ProductGivenDetailEditReqDTO req) {
        AuthUserDetails user = AuthUserHelper.getAuthUser();
        req.setUpdatedBy(user.getUserIdStr());
        req.setUpdatedName(user.getName());
        return productGivenDetailFeign.edit(req);
    }

    @Operation(summary = "启用备件")
    @PostMapping("/products/product-given-details/enable")
    Result<Void> enable(@RequestBody EnableGivenDetailReqDTO req) {
        AuthUserDetails user = AuthUserHelper.getAuthUser();
        req.setUpdatedBy(user.getUserIdStr());
        req.setUpdatedName(user.getName());
        return productGivenDetailFeign.enable(req);
    }

    @Operation(summary = "停用备件")
    @PostMapping("/products/product-given-details/disable")
    Result<Void> disable(@RequestBody DisableGivenDetailReqDTO req) {
        AuthUserDetails user = AuthUserHelper.getAuthUser();
        req.setUpdatedBy(user.getUserIdStr());
        req.setUpdatedName(user.getName());
        return productGivenDetailFeign.disable(req);
    }

}
