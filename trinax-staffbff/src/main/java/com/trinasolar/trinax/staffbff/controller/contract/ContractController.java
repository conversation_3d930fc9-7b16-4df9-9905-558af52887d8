package com.trinasolar.trinax.staffbff.controller.contract;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.trinasolar.trinax.auth.core.details.AuthUserDetails;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.cart.dto.input.ContractDeliveryQueryReqDTO;
import com.trinasolar.trinax.cart.dto.input.ContractQueryPcDetailReqDTO;
import com.trinasolar.trinax.cart.dto.input.ContractQueryPcReqDTO;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.excel.ExcelDownloadUtil;
import com.trinasolar.trinax.contract.api.ContractFeign;
import com.trinasolar.trinax.contract.api.ContractItemChangeFeign;
import com.trinasolar.trinax.contract.dto.input.DeliveryAgreementQueryDTO;
import com.trinasolar.trinax.contract.dto.input.EmailReqDTOByContract;
import com.trinasolar.trinax.contract.dto.input.SendEmailByExchangeDTOByContract;
import com.trinasolar.trinax.contract.dto.input.SendEmailInfoDTO;
import com.trinasolar.trinax.contract.dto.input.ContractModifyReqDTO;
import com.trinasolar.trinax.contract.dto.input.QuickOrderContractSubmitReqDTO;
import com.trinasolar.trinax.contract.dto.input.contract.sales.ContractItemsModifyReqDTO;
import com.trinasolar.trinax.contract.dto.input.contract.sales.ContractTrendReqDTO;
import com.trinasolar.trinax.contract.dto.output.*;
import com.trinasolar.trinax.contract.dto.output.change.ContractItemInfoResDTO;
import com.trinasolar.trinax.contract.dto.output.contract.sales.ContractTrendResDTO;
import com.trinasolar.trinax.integration.dto.output.contract.ContractChangeItemResDTO;
import com.trinasolar.trinax.intentorder.constants.enums.IntentOrderTypeEnum;
import com.trinasolar.trinax.intentorder.dto.input.IntentOrderPCQueryReqDTO;
import com.trinasolar.trinax.log.behavior.support.BehaviorLog;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;


@Slf4j
@Tag(name = "合同API")
@RestController
@RequiredArgsConstructor
@RequestMapping("/contract")
@Api(value = "合同API", tags = "Contract API")
public class ContractController {
    private final ContractFeign contractFeign;
    private final ContractItemChangeFeign contractItemChangeFeign;

    @PostMapping("/pageContractResPc")
    @Operation(summary = "合同-分页查询pc端-过后端权限滤")
    public Result<PageResponse<ContractResPcDTO>> pageContractResPc(@RequestBody PageRequest<ContractQueryPcReqDTO> pageReqDTO) {
        pageReqDTO.getQuery().setUserID(AuthUserHelper.getAuthUser().getUserIdStr());
        return contractFeign.pageContractResPcAuth(pageReqDTO);
    }


    @PostMapping("/userDetailPageContractResPc")
    @Operation(summary = "用户详情&客户详情-合同-分页查询pc端")
    public Result<PageResponse<ContractResPcDTO>> userDetailPageContractResPc(@RequestBody PageRequest<ContractQueryPcReqDTO> pageReqDTO) {
        pageReqDTO.getQuery().setUserID(AuthUserHelper.getAuthUser().getUserIdStr());
        return contractFeign.userDetailPageContractResPc(pageReqDTO);
    }

    @BehaviorLog(ignore = false, ignoreReq = false)
    @PostMapping("/export/listContractResPcExcel")
    @Operation(summary = "合同-pc端-excel导出 ")
    public void listContractResPcExcel(@RequestBody ContractQueryPcReqDTO reqDTO, HttpServletResponse response) {
        reqDTO.setUserID(AuthUserHelper.getAuthUser().getUserIdStr());
        Result<List<ContractResPcExcelDTO>> result = contractFeign.listContractResPcExcelAuth(reqDTO);
        String filename ="";
        if(IntentOrderTypeEnum.QUICK_ORDER.getCode().equals(reqDTO.getOrderType())){
            filename = "渠道订单合同_" + DateFormatUtils.format(new Date(), "yyyyMMdd_HH_mm_ss");
        }else{
            filename = "销售合同_" + DateFormatUtils.format(new Date(), "yyyyMMdd_HH_mm_ss");
        }
        ExcelDownloadUtil.download(response, ContractResPcExcelDTO.class, result.getData(), filename, ExcelTypeEnum.XLSX);
    }

    @PostMapping("/export/userDetailListContractResPc")
    @Operation(summary = "用户详情-合同-pc端-excel导出")
    public void userDetailListContractResPc(@RequestBody ContractQueryPcReqDTO reqDTO, HttpServletResponse response) {
        reqDTO.setUserID(AuthUserHelper.getAuthUser().getUserIdStr());
        Result<List<ContractResPcExcelDTO>> result = contractFeign.userDetailListContractResPc(reqDTO);
        List<ContractResPcExcelDTO> contractResPcExcelDTOS = result.getData();
        List<UserDetailContractResPcExcelDTO> userDetailContractResPcExcelDTOS = BeanUtil.copyToList(contractResPcExcelDTOS, UserDetailContractResPcExcelDTO.class);
        String filename = "合同_" + DateFormatUtils.format(new Date(), "yyyyMMdd_HH_mm_ss");
        ExcelDownloadUtil.download(response, UserDetailContractResPcExcelDTO.class, userDetailContractResPcExcelDTOS, filename, ExcelTypeEnum.XLSX);
    }


    @PostMapping("/contractDetailResPc")
    @Operation(summary = "合同-合同详情-pc端显示")
    public Result<ContractDetailResPcDTO> contractDetailResPc(@Valid @ParameterObject ContractQueryPcDetailReqDTO reqDTO) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        reqDTO.setUserID(authUser.getUserIdStr());
        return contractFeign.contractDetailResPc(reqDTO);
    }

    @PostMapping("/contractItemsModify")
    @Operation(summary = "合同行编辑-发起sf合同数据同步")
    public Result<List<ContractChangeItemResDTO>> contractItemsModify(@Valid @RequestBody ContractItemsModifyReqDTO req) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        req.setLoginUserId(authUser.getUserIdStr());
        req.setLoginUserName(authUser.getName());
        req.setLoginUserType(authUser.getUserType());
        return contractFeign.contractItemsModify(req);
    }

    @PostMapping("/qryContractItem")
    @Operation(summary = "通过合同编号查询合同行信息")
    public Result<List<ContractItemInfoResDTO>> qryContractItem(@RequestParam @NotBlank String sfContractNo) {
        return contractItemChangeFeign.qryContractItem(sfContractNo);
    }


    @PostMapping("/deliveryContractForManagement/internal")
    @Operation(summary = "管理端-发货申请详情-查询合同产品列表(内部)")
    Result<List<ContractDeliveryResAppDTO>> deliveryPageContractForManagementInternal(@Valid @RequestBody ContractDeliveryQueryReqDTO reqDTO) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        reqDTO.setUserID(authUser.getUserIdStr());
        return contractFeign.deliveryContractForManagement(reqDTO);
    }

    @Operation(summary = "合同趋势列表")
    @PostMapping({"/contractTrendList"})
    Result<List<ContractTrendResDTO>> contractTrendList(@RequestBody ContractTrendReqDTO reqDTO) {
        return contractFeign.contractTrendList(reqDTO);
    }

    @Operation(summary = "合同趋势列表导出")
    @PostMapping({"contractTrendExport"})
    void trendListExport(@RequestBody ContractTrendReqDTO reqDTO, HttpServletResponse response) {
        Result<List<ContractTrendResDTO>> result = contractFeign.contractTrendList(reqDTO);
        String filename = "合同_" + DateFormatUtils.format(new Date(), "yyyyMMdd_HH_mm_ss");
        ExcelDownloadUtil.download(response, ContractTrendResDTO.class, result.getData(), filename, ExcelTypeEnum.XLSX);
    }


    @PostMapping("/contract/pageDeliveryAgreement")
    @Operation(summary = "提货委托书-分页查询")
    Result<PageResponse<DeliveryAgreementRespDTO>> pageDeliveryAgreement(@RequestBody PageRequest<DeliveryAgreementQueryDTO> pageReqDTO){
        return contractFeign.pageDeliveryAgreement(pageReqDTO);
    }

    @PostMapping("/contract/exportDeliveryAgreement")
    @Operation(summary = "提货委托书-导出")
    void exportDeliveryAgreement(@RequestBody DeliveryAgreementQueryDTO reqDTO, HttpServletResponse response){
        Result<List<DeliveryAgreementRespDTO>> result = contractFeign.exportDeliveryAgreement(reqDTO);
        String filename = "提货委托书_" + DateFormatUtils.format(new Date(), "yyyyMMdd_HH_mm_ss");
        ExcelDownloadUtil.download(response, DeliveryAgreementRespDTO.class, result.getData(), filename, ExcelTypeEnum.XLSX);
    }

    @PostMapping("/contract/cancelDeliveryAgreement")
    @Operation(summary = "提货委托书-作废")
    Result cancelDeliveryAgreement(@RequestBody List<String> contractIds){
        return contractFeign.cancelDeliveryAgreement(contractIds);
    }

    @PostMapping("/delivery/getEmail")
    @Operation(summary = "获取邮件信息")
    Result<PageResponse<EmailResDTOByContract>> getEmail(@RequestBody @Valid PageRequest<EmailReqDTOByContract> req){
        return contractFeign.getEmail(req);
    }

    @PostMapping("/delivery/sendEmailByExchange")
    @Operation(summary = "发送邮件通过Exchange")
    Result sendEmailByExchange(@RequestBody @Valid SendEmailByExchangeDTOByContract req){
        return contractFeign.sendEmailByExchange(req);
    }

    @PostMapping("/delivery/getSendEmailInfo")
    @Operation(summary = "获取邮件发送信息")
    Result<SendEmailInfoDTO> getSendEmailInfo(@RequestBody @Valid SendEmailInfoDTO req){
        return contractFeign.getSendEmailInfo(req);
    }

    @PostMapping("/quickOrder/previewContractDoc")
    @Operation(summary = "渠道快速生成订单合同合同文件预览")
    Result<QuickOrderContractPreviewResDTO> previewContractDoc(@RequestBody QuickOrderContractSubmitReqDTO quickOrderContractSubmitReqDTO){
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        quickOrderContractSubmitReqDTO.setCurrentUserId(authUser.getUserIdStr());
        quickOrderContractSubmitReqDTO.setCurrentUserType(authUser.getUserType());
        return contractFeign.previewContractDoc(quickOrderContractSubmitReqDTO);
    }

    @PostMapping("/quickOrder/review")
    @Operation(summary = "渠道快速订单合同预审页面")
    Result<QuickOrderContractReviewDTO> reviewContract(@RequestBody IntentOrderPCQueryReqDTO intentOrderPCQueryReqDTO){
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        intentOrderPCQueryReqDTO.setCurrentUserId(authUser.getUserIdStr());
        intentOrderPCQueryReqDTO.setCurrentUserType(authUser.getUserType());
        return contractFeign.reviewContract(intentOrderPCQueryReqDTO);
    }

    @PostMapping("/quickOrder/submitContract")
    @Operation(summary = "提交合同")
    Result<QuickOrderContractReviewDTO> submitQuickOrderContract(@RequestBody QuickOrderContractSubmitReqDTO quickOrderContractSubmitReqDTO){
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        quickOrderContractSubmitReqDTO.setCurrentUserId(authUser.getUserIdStr());
        quickOrderContractSubmitReqDTO.setCurrentUserType(authUser.getUserType());
        return contractFeign.submitQuickOrderContract(quickOrderContractSubmitReqDTO);
    }

    @PostMapping("/contract/retryQuickOrderContractToBPM")
    @Operation(summary = "重试BPM提交")
    Result retryQuickOrderContractToBPM(@RequestParam String contractId) {
        return contractFeign.retryQuickOrderContractToBPM(contractId);
    }
    @PostMapping("/quickOrder/confirmOrder")
    @Operation(summary = "确认合同")
    Result<String> confirmOrder(@RequestBody ContractModifyReqDTO contractModifyReqDTO){
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        contractModifyReqDTO.setCurrentUserId(authUser.getUserIdStr());
        contractModifyReqDTO.setCurrentUserName(authUser.getName());
        return contractFeign.confirmOrder(contractModifyReqDTO);
    }

    @PostMapping("/quickOrder/cancelOrder")
    @Operation(summary = "取消合同")
    Result<String> cancelOrder(@RequestBody ContractModifyReqDTO contractModifyReqDTO){
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        contractModifyReqDTO.setCurrentUserId(authUser.getUserIdStr());
        contractModifyReqDTO.setCurrentUserName(authUser.getName());
        return contractFeign.cancelOrder(contractModifyReqDTO);
    }
}
