package com.trinasolar.trinax.staffbff.controller.contract.invoice;

import com.trinasolar.trinax.auth.core.details.AuthUserDetails;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.billing.api.InvoiceFeign;
import com.trinasolar.trinax.billing.dto.input.ModifyApplicationReqDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "开票申请 -内部员工")
@RestController
@RequiredArgsConstructor
@Slf4j
public class InvoiceController {
    final InvoiceFeign invoiceFeign;

    @PostMapping("/sales/modifyInvoiceApplication")
    @Operation(summary = "编辑开票申请-销售")
    public Result<String> modifyInvoiceApplication(@RequestBody ModifyApplicationReqDTO req) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        req.setCurrentUserId(authUser.getUserIdStr());
        req.setCurrentUserName(authUser.getName());
        req.setCurrentUserType(authUser.getUserType());
        return invoiceFeign.modifyInvoiceApplication(req);
    }


}
