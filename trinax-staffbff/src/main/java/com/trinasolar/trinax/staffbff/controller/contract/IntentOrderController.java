package com.trinasolar.trinax.staffbff.controller.contract;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.trinasolar.trinax.auth.core.details.AuthUserDetails;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.ValidatorUtils;
import com.trinasolar.trinax.common.utils.excel.ExcelDownloadUtil;
import com.trinasolar.trinax.intentorder.api.IntentOrderFeign;
import com.trinasolar.trinax.intentorder.constants.IntentOrderConstant;
import com.trinasolar.trinax.intentorder.constants.enums.IntentOrderStatusEnum;
import com.trinasolar.trinax.intentorder.constants.enums.IntentOrderTypeEnum;
import com.trinasolar.trinax.intentorder.constants.enums.OrderMethodEnum;
import com.trinasolar.trinax.intentorder.dto.EnterpriseIntentOrderExcelResDTO;
import com.trinasolar.trinax.intentorder.dto.IntentOrderPCExcelResDTO;
import com.trinasolar.trinax.intentorder.dto.IntentOrderUserDetailExcelResDTO;
import com.trinasolar.trinax.intentorder.dto.input.*;
import com.trinasolar.trinax.intentorder.dto.output.*;
import com.trinasolar.trinax.log.behavior.support.BehaviorLog;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Tag(name = "内部员工意向单接口")
@RestController
@RequiredArgsConstructor
@Slf4j
public class IntentOrderController {
    @Autowired
    private IntentOrderFeign intentOrderFeign;

    //以下接口APP端调用

    /***
     * 意向单删除接口
     * @param intentOrderNo
     * @return
     */
    @BehaviorLog(ignore = false, ignoreReq = false, bizNo = "#intentOrderNo")
    @Operation(description = "意向单删除接口")
    @DeleteMapping("/intentOrder/deleteIntentOrder")
    Result<String> deleteIntentOrder(@NotBlank(message = "意向单编号不为空") @RequestParam("intentOrderNo") String intentOrderNo) {
        return intentOrderFeign.deleteIntentOrder(intentOrderNo, AuthUserHelper.getAuthUser().getUserIdStr());
    }

    /**
     * 取消意向单
     *
     * @param reqDTO req
     * @return 取消意向单
     */
    @BehaviorLog(ignore = false, ignoreReq = false, bizNo = "#reqDTO.intentOrderID")
    @Operation(description = "意向单取消接口")
    @PostMapping("/intentOrder/cancelIntentOrder")
    Result<String> cancelIntentOrder(@Validated @RequestBody IntentOrderCancelReqDTO reqDTO) {
        LocalDateTime start = LocalDateTime.now();
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        reqDTO.setUserID(authUser.getUserIdStr());
        reqDTO.setUserType(authUser.getUserType());
        Result<String> result = intentOrderFeign.cancelStaffIntentOrder(reqDTO);
        long milliSeconds = Duration.between(start, LocalDateTime.now()).toMillis();
        log.info("内部用户取消意向单{},接口耗时{}毫秒", reqDTO.getIntentOrderID(), milliSeconds);
        return result;
    }

    /**
     * 保存意向单接口：内部用户帮助外部客户创建意向单（只做保存）
     *
     * @param reqDTO
     * @return
     */
    @BehaviorLog(ignore = false, ignoreReq = false, bizNo = "#result?.data.intentOrderNo")
    @PostMapping("/intentOrder/save")
    public Result<SubmitAndConfirmResDTO> saveIntentOrder(@Validated @RequestBody IntentOrderSaveReqDTO reqDTO) {
        //一项单号不存在时，说明是新增保存意向单
        if (StringUtils.isBlank(reqDTO.getIntentOrderNo())) {
//            if(IntentOrderTypeEnum.QUICK_ORDER.getCode().equals(reqDTO.getOrderType())){
//                reqDTO.setStatus(IntentOrderStatusEnum.UN_CONFIRMED.getCode());
//            }else{
                reqDTO.setStatus(IntentOrderStatusEnum.UN_SUBMIT.getCode());
//            }
        }
        reqDTO.setIsSurrogateOrder(OrderMethodEnum.ORDER_BY_EMPLOYEE.getCode());
        AuthUserDetails userDetails = AuthUserHelper.getAuthUser();
        reqDTO.setCreatedBy(userDetails.getUserIdStr());
        reqDTO.setCreatedName(userDetails.getName());
        reqDTO.setCreatedPhone(userDetails.getMobile());
        return intentOrderFeign.saveIntentOrder(reqDTO);
    }

    @PostMapping("/intentOrder/confirmToIntendOrder")
    @Operation(summary = "内部用户确认需求->意向单")
    public Result<SubmitAndConfirmResDTO> confirmToIntendOrder(@Validated @RequestBody IntentOrderSaveReqDTO reqDTO) {
        //一项单号不存在时，说明是新增保存意向单
        if (StringUtils.isBlank(reqDTO.getIntentOrderNo())) {
            reqDTO.setStatus(IntentOrderStatusEnum.UN_SUBMIT.getCode());
        }
        reqDTO.setIsSurrogateOrder(OrderMethodEnum.ORDER_BY_EMPLOYEE.getCode());
        AuthUserDetails userDetails = AuthUserHelper.getAuthUser();
        reqDTO.setCreatedBy(userDetails.getUserIdStr());
        reqDTO.setCreatedName(userDetails.getName());
        reqDTO.setCreatedPhone(userDetails.getMobile());
        return intentOrderFeign.saveIntentOrder(reqDTO);
    }

    /**
     * 编辑保存意向单：只做修改保存操作
     * 1、内部用户修改未提及状态意向单--意向单行信息直接删除重新插入，意向单休息直接计算修改。
     * 2、内部用户修改提交状态意向单（修改由外部用户提交的意向单）--同上
     * 3、内部用户修改已确认状态意向单（修改已确认状态意向单：不会删除行）--新增和修改的意向单行信息需区分处理，意向单信息直接修改。
     *
     * @param reqDTO
     * @return
     */
    @PostMapping("/intentOrder/edit")
    public Result<SubmitAndConfirmResDTO> editIntentOrder(@Validated @RequestBody IntentOrderSaveReqDTO reqDTO) {
        ValidatorUtils.validateNotEmpty(reqDTO.getIntentOrderNo(), "意向单编号不为空");
        AuthUserDetails userDetails = AuthUserHelper.getAuthUser();
        reqDTO.setUpdatedBy(userDetails.getUserIdStr());
        reqDTO.setUpdatedName(userDetails.getName());
        reqDTO.setUpdatedPhone(userDetails.getMobile());
        return intentOrderFeign.updateIntentOrder(reqDTO);
    }

    /**
     * 校验意向单参数
     *
     * @param reqDTO
     * @return
     */
    @PostMapping("/intentOrder/check")
    public Result<SubmitAndConfirmResDTO> checkIntentOrder(@Validated @RequestBody IntentOrderConfirmReqDTO reqDTO) {
        AuthUserDetails userDetails = AuthUserHelper.getAuthUser();
        if (!SysUserTypeEnum.INTERNAL.getType().equalsIgnoreCase(userDetails.getUserType())) {
            return Result.fail("当前用户非内部员工，无法进行此操作");
        }
        reqDTO.setUserId(userDetails.getUserIdStr());
        reqDTO.setUserName(userDetails.getName());
        reqDTO.setUserType(userDetails.getUserType());

        return intentOrderFeign.checkIntentOrder(reqDTO);
    }

    /**
     * 确认意向单接口：只能由内部用户去确认
     *
     * @param reqDTO
     * @return
     */
    @BehaviorLog(ignore = false, ignoreReq = false, bizNo = "#reqDTO.intentOrderNo")
    @PostMapping("/intentOrder/confirm")
    public Result<SubmitAndConfirmResDTO> confirmIntentOrder(@Validated @RequestBody IntentOrderConfirmReqDTO reqDTO) {
        LocalDateTime start = LocalDateTime.now();
        AuthUserDetails userDetails = AuthUserHelper.getAuthUser();
        if (!SysUserTypeEnum.INTERNAL.getType().equalsIgnoreCase(userDetails.getUserType())) {
            return Result.fail("当前用户非内部员工，无法进行此操作");
        }
        reqDTO.setUserId(userDetails.getUserIdStr());
        reqDTO.setUserName(userDetails.getName());
        reqDTO.setUserType(userDetails.getUserType());
        Result<SubmitAndConfirmResDTO> result = intentOrderFeign.confirmIntentOrder(reqDTO);
        long milliSeconds = Duration.between(start, LocalDateTime.now()).toMillis();
        log.info("确认意向单{},接口耗时{}毫秒", reqDTO.getIntentOrderNo(), milliSeconds);
        return result;
    }

    //以下接口PC端调用

    /**
     * 意向单列表分页查询接口：针对内部用户
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/intentOrder/findPCPage")
    Result<PageResponse<IntentOrderPCQueryResDTO>> findIntentOrderPCPage(@RequestBody PageRequest<IntentOrderPCQueryReqDTO> reqDTO) {
        reqDTO.getQuery().setCurrentUserId(AuthUserHelper.getAuthUser().getUserIdStr());
        return intentOrderFeign.findIntentOrderPCPage(reqDTO);
    }

    /**
     * 文件名命名规则：表名（英文）+YYYYMMDD
     *
     * @param queryDto
     * @param response
     * @return
     */
    @BehaviorLog(ignore = false, ignoreReq = false)
    @PostMapping(value = "/intentOrder/exportPCPage")
    @Operation(description = "导出意向单信息")
    public void exportIntentOrderPCPage(@RequestBody IntentOrderPCQueryReqDTO queryDto, HttpServletResponse response) {
        AuthUserDetails userDetails = AuthUserHelper.getAuthUser();
        queryDto.setCurrentUserId(userDetails.getUserIdStr());
        queryDto.setCurrentUserType(userDetails.getUserType());
        Result<List<IntentOrderPCExcelResDTO>> result = intentOrderFeign.findIntentOrderPCPageList(queryDto);
        String filename ="";
        if(IntentOrderTypeEnum.QUICK_ORDER.getCode().equals(queryDto.getOrderType())){
            filename = IntentOrderConstant.QUICK_ORDER_EXCEL_FILENAME_PRE + DateFormatUtils.format(new Date(), "yyyyMMdd_HH_mm_ss");
        }else{
            filename = IntentOrderConstant.INTENT_ORDER_EXCEL_FILENAME_PRE + DateFormatUtils.format(new Date(), "yyyyMMdd_HH_mm_ss");
        }
        ExcelDownloadUtil.download(response, IntentOrderPCExcelResDTO.class, result.getData(), filename, ExcelTypeEnum.XLSX);
    }

    /**
     * 获取汇总信息
     *
     * @param queryDto
     * @return
     */
    @PostMapping(value = "/intentOrder/getSummaryData")
    @Operation(description = "获取意向单汇总信息")
    public Result<IntentOrderSummaryResDTO> getSummaryData(@RequestBody IntentOrderPCQueryReqDTO queryDto) {
        queryDto.setCurrentUserId(AuthUserHelper.getAuthUser().getUserIdStr());
        return intentOrderFeign.getSummaryData(queryDto);
    }


    /**
     * 意向单行列表分页查询接口：针对内部用户
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/intentOrder/findItemPCPage")
    Result<PageResponse<IntentOrderItemResDTO>> findIntentOrderItemPCPage(@Validated @RequestBody PageRequest<IntentOrderItemPCQueryReqDTO> reqDTO) {
        ValidatorUtils.validateNotEmpty(reqDTO.getQuery().getIntentOrderNo(), "意向单号不为空");
        return intentOrderFeign.findIntentOrderItemPCPage(reqDTO);
    }

    /**
     * 文件名命名规则：表名（英文）+YYYYMMDD
     *
     * @param queryDto
     * @param response
     * @return
     */
    @PostMapping(value = "/intentOrder/exportUserDetailPage")
    @Operation(description = "用户详情导出意向单信息")
    public void exportUserDetailPage(@RequestBody IntentOrderPCQueryReqDTO queryDto, HttpServletResponse response) {
        ValidatorUtils.validateNotEmpty(queryDto.getDataUserId(), "查看数据用户的userId不为空");
        ValidatorUtils.validateNotEmpty(queryDto.getDataUserType(), "查看数据用户的用户类型不为空");
        AuthUserDetails userDetails = AuthUserHelper.getAuthUser();
        queryDto.setCurrentUserId(userDetails.getUserIdStr());
        queryDto.setCurrentUserType(userDetails.getUserType());
        Result<List<IntentOrderPCExcelResDTO>> result = intentOrderFeign.findIntentOrderPCPageList(queryDto);
        List<IntentOrderUserDetailExcelResDTO> excelList = BeanUtil.copyToList(result.getData(), IntentOrderUserDetailExcelResDTO.class);
        String filename = IntentOrderConstant.INTENT_ORDER_EXCEL_FILENAME_PRE + DateFormatUtils.format(new Date(), "yyyyMMdd_HH_mm_ss");
        ExcelDownloadUtil.download(response, IntentOrderUserDetailExcelResDTO.class, excelList, filename, ExcelTypeEnum.XLSX);
    }

    /**
     * 意向单行列表分页查询接口：针对内部用户
     *
     * @param reqDTO
     * @return
     */
    @Operation(description = "企业意向单列表条件分页查询")
    @PostMapping(value = "/intentOrder/findEnterpriseIntentOrderPage")
    Result<PageResponse<IntentOrderPCQueryResDTO>> findEnterpriseIntentOrderPage(@Validated @RequestBody PageRequest<IntentOrderPCQueryReqDTO> reqDTO) {
        ValidatorUtils.validateNotEmpty(reqDTO.getQuery().getEnterpriseId(), "客户企业ID不为空");
        reqDTO.getQuery().setCurrentUserId(AuthUserHelper.getAuthUser().getUserIdStr());
        reqDTO.getQuery().setCurrentUserType(AuthUserHelper.getAuthUser().getUserType());
        return intentOrderFeign.findIntentOrderPCPage(reqDTO);
    }

    /**
     * 文件名命名规则：表名（英文）+YYYYMMDD
     *
     * @param queryDto
     * @param response
     * @return
     */
    @Operation(description = "企业意向单列表条件导出")
    @PostMapping(value = "/intentOrder/exportEnterpriseIntentOrderPage")
    public void exportEnterpriseIntentOrderPage(@RequestBody IntentOrderPCQueryReqDTO queryDto, HttpServletResponse response) {
        ValidatorUtils.validateNotEmpty(queryDto.getEnterpriseId(), "客户企业ID不为空");
        AuthUserDetails userDetails = AuthUserHelper.getAuthUser();
        queryDto.setCurrentUserId(userDetails.getUserIdStr());
        queryDto.setCurrentUserType(userDetails.getUserType());
        Result<List<IntentOrderPCExcelResDTO>> result = intentOrderFeign.findIntentOrderPCPageList(queryDto);
        List<EnterpriseIntentOrderExcelResDTO> excelList = BeanUtil.copyToList(result.getData(), EnterpriseIntentOrderExcelResDTO.class);
        String filename = IntentOrderConstant.INTENT_ORDER_EXCEL_FILENAME_PRE + DateFormatUtils.format(new Date(), "yyyyMMdd_HH_mm_ss");
        ExcelDownloadUtil.download(response, EnterpriseIntentOrderExcelResDTO.class, excelList, filename, ExcelTypeEnum.XLSX);
    }

    @Operation(summary = "意向单趋势列表")
    @PostMapping({"intentOrder/trendList"})
    Result<List<IntentOrderReportTrendResDTO>> trendList(@RequestBody IntentOrderReportTrendReqDTO reqDTO) {
        return intentOrderFeign.trendList(reqDTO);
    }

    @Operation(summary = "意向单趋势列表导出")
    @PostMapping({"intentOrder/trendListExport"})
    Result<List<IntentOrderReportTrendResDTO>> trendListExport(@RequestBody IntentOrderReportTrendReqDTO reqDTO,HttpServletResponse response) {
        Result<List<IntentOrderReportTrendResDTO>> result = intentOrderFeign.trendList(reqDTO);
        String filename = IntentOrderConstant.INTENT_ORDER_EXCEL_FILENAME_PRE + DateFormatUtils.format(new Date(), "yyyyMMdd_HH_mm_ss");
        ExcelDownloadUtil.download(response, IntentOrderReportTrendResDTO.class, result.getData(), filename, ExcelTypeEnum.XLSX);
        return intentOrderFeign.trendList(reqDTO);
    }

    @BehaviorLog(ignore = false, ignoreReq = false, bizNo = "#reqDTO.intentOrderNo")
    @PostMapping("/intentOrder/config")
    public Result<SubmitAndConfirmResDTO> configIntentOrder(@RequestBody IntentOrderConfigReqDTO reqDTO) {
        LocalDateTime start = LocalDateTime.now();
        AuthUserDetails userDetails = AuthUserHelper.getAuthUser();
        if (!SysUserTypeEnum.INTERNAL.getType().equalsIgnoreCase(userDetails.getUserType())) {
            return Result.fail("当前用户非内部员工，无法进行此操作");
        }
        reqDTO.setUserId(userDetails.getUserIdStr());
        reqDTO.setUserName(userDetails.getName());
        Result<SubmitAndConfirmResDTO> result = intentOrderFeign.configIntentOrder(reqDTO);
        long milliSeconds = Duration.between(start, LocalDateTime.now()).toMillis();
        log.info("运营编辑功率确认意向单{},接口耗时{}毫秒", reqDTO.getIntentOrderNo(), milliSeconds);
        return result;
    }

    @BehaviorLog(ignore = false, ignoreReq = false, bizNo = "#intentOrderNo")
    @Operation(description = "意向单拒绝接口")
    @PostMapping("/intentOrder/refuse")
    Result<String> refuseIntentOrder(@RequestBody IntentOrderConfigReqDTO reqDTO) {
        AuthUserDetails userDetails = AuthUserHelper.getAuthUser();
        reqDTO.setUserId(userDetails.getUserIdStr());
        reqDTO.setUserName(userDetails.getName());
        return intentOrderFeign.refuseConfig(reqDTO);
    }

}
