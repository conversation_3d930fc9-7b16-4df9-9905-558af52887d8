<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.trinasolar</groupId>
        <artifactId>trinax-shared</artifactId>
        <version>0.0.2.RELEASE</version>
        <relativePath>../trinax-shared</relativePath>
    </parent>
    <packaging>jar</packaging>
    <artifactId>trinax-staffbff</artifactId>
    <name>trinax-staffbff</name>
    <version>0.0.4-SNAPSHOT</version>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <java.version>8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trinax-common</artifactId>
            <version>0.0.2.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trinax-basic-api</artifactId>
            <version>0.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trinax-delivery-api</artifactId>
            <version>0.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trinax-masterdata-api</artifactId>
            <version>0.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trinax-contract-api</artifactId>
            <version>0.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trinax-user-api</artifactId>
            <version>0.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trinax-partner-api</artifactId>
            <version>0.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trinax-behavior-log</artifactId>
            <version>0.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trinax-report-api</artifactId>
            <version>0.0.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trinax-auth-center-core</artifactId>
            <version>0.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.trinasolar.lapetus.log</groupId>-->
<!--            <artifactId>log2-spring-boot-starter</artifactId>-->
<!--        </dependency>-->
    </dependencies>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot-dependencies.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${plugin.compile.version}</version>

                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <forceJavacCompilerUse>true</forceJavacCompilerUse>
                </configuration>
                <!--configuration>
                <release>11</release>
                </configuration-->

            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot-dependencies.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>

</project>
