package com.trinasolar.trinax.integration.service;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.dto.input.oa.InvoiceQuantityQryReqDTO;
import com.trinasolar.trinax.integration.dto.input.oa.SaleInvoiceReqDTO;
import com.trinasolar.trinax.integration.dto.output.oa.InvoiceQuantityQryResDTO;
import com.trinasolar.trinax.integration.dto.output.oa.SaleInvoiceResDTO;

import java.util.List;

public interface OAInvoiceService {
    Result<List<InvoiceQuantityQryResDTO>> qryInvoiceQuantity(List<InvoiceQuantityQryReqDTO> req);

    Result<SaleInvoiceResDTO> initiateSaleInvoice(SaleInvoiceReqDTO req);
}
