package com.trinasolar.trinax.integration.manager.remote.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "")
public class ContractDocument {

    @Schema(description = "电子签约文档ID")
    private String id;

    @Schema(description = "文档名称")
    private String title;

    @Schema(description = "文档类型(pdf、ofd)")
    private String type;

    @Schema(description = "文件用途:NORMAL(电子签约正文)、TERMINATION(作废声明)、OFFLINE(线下签署文件)、" +
            "SPONSOR_FILE(发起方内部可见文档)、ATTACHMENT(附件)、EVIDENCE(存证报告)")
    private String usage;

    private List<DetailDocumentParam> params;
}
