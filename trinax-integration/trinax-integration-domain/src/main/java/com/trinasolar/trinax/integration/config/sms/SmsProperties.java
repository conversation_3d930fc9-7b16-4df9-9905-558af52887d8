package com.trinasolar.trinax.integration.config.sms;

import lombok.Data;

@Data
//@Configuration("smsPropertiesService")
//@ConfigurationProperties(prefix = "trina.sms")
public class SmsProperties {

    private String clientId;

    private String clientSecret;

    private String host;

    private String signName;

    private String appKey;

    private String sendStatus;

    private String appSecret;

    private String serviceType;

}
