package com.trinasolar.trinax.integration.manager.remote.contract;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "ThirdContractFeign", url = "${trina.contract.url}")
public interface ThirdContractFeign {


    @PostMapping(value = "contract/signurl")
    ThirdContractPreSignResDTO signurl(@RequestBody ThirdContractSignReqDTO contractId, @RequestHeader HttpHeaders headers);

    @GetMapping(value = "contract/viewurl")
    ThirdContractViewResDTO viewurl(@RequestParam Long contractId,
                                    @RequestParam String pageType,
                                    @RequestParam String allowDownload,
                                    @RequestHeader HttpHeaders headers);


    @GetMapping(value = "contract/detail")
    ContractSignFileResDTO detail(@RequestParam long contractId,
                                  @RequestParam String bizId,
                                  @RequestParam boolean showParamValue,
                                  @RequestParam boolean displayImageValue,
                                  @RequestHeader HttpHeaders headers);
}
