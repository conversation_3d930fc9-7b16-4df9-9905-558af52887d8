<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.trinasolar</groupId>
		<artifactId>trinax-integration-root</artifactId>
		<version>0.0.4-SNAPSHOT</version>
	</parent>

	<packaging>jar</packaging>
	<version>0.0.4-SNAPSHOT</version>
	<artifactId>trinax-integration-api</artifactId>
	<name>trinax-integration-api</name>
	<description>Demo project for Spring Boot</description>
	<properties>
		<java.version>8</java.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.trinasolar</groupId>
			<artifactId>trinax-common</artifactId>
		</dependency>

		<dependency>
			<groupId>dtt.asset</groupId>
			<artifactId>dtt-limiting</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>io.swagger.core.v3</groupId>
			<artifactId>swagger-annotations</artifactId>
			<version>2.2.9</version>
		</dependency>

		<!--	对接天机接口需要的包	-->
		<dependency>
			<groupId>com.aliyun.api.gateway</groupId>
			<artifactId>sdk-core-java</artifactId>
			<version>1.1.0</version>
		</dependency>
	</dependencies>

<!--	<repositories>-->
<!--		<repository>-->
<!--			<id>trina-nexus</id>-->
<!--			<name>trina-nexus</name>-->
<!--			<url>http://10.40.1.96:18081/nexus/content/groups/public/</url>-->
<!--			<releases>-->
<!--				<enabled>true</enabled>-->
<!--			</releases>-->
<!--			<snapshots>-->
<!--				<enabled>true</enabled>-->
<!--			</snapshots>-->
<!--		</repository>-->
<!--	</repositories>-->

<!--	<distributionManagement>-->
<!--		<repository>-->
<!--			<id>trina-release</id>-->
<!--			<name>Releases</name>-->
<!--			<url>http://10.40.1.96:18081/nexus/content/repositories/releases/</url>-->
<!--		</repository>-->
<!--		<snapshotRepository>-->
<!--			<id>trina-snapshot</id>-->
<!--			<name>Snapshot</name>-->
<!--			<url>http://10.40.1.96:18081/nexus/content/repositories/snapshots/</url>-->
<!--		</snapshotRepository>-->
<!--	</distributionManagement>-->
</project>
