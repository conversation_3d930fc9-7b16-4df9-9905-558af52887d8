package com.trinasolar.trinax.integration.dto.output.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShipmentSignItemsDTO {

    @Schema(description = "合同号")
    private String pi;

    @Schema(description = "SO单号")
    private String so;

    @Schema(description = "运单号")
    private String shipmentNo;

    @Schema(description = "产品名称")
    private String product;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "总功率")
    private String totalPower;

    @Schema(description = "运输方式")
    private String transports;

    @Schema(description = "签收人员")
    private String signer;

    @Schema(description = "车牌号")
    private String licensePlateNumber;

    @Schema(description = "签收时间")
    private LocalDateTime signTime;

    @Schema(description = "签收单")
    private String podNo;

}
