package com.trinasolar.trinax.integration.constants.enums.intentOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 意向单取消时，返回状态码和对应的至尊宝提示文案
 */
@Getter
@AllArgsConstructor
public enum ConfirmHeaderStatusEnum {

    SUCCESS("SUCCESS", "确认意向单成功"),
    /**
     * Product data can not be empty
     */
    PRODUCT_EMPTY("101", "同步信息失败，请联系系统管理员"),

    /**
     * Parsing Product Data Error
     */
    PARSE_PRODUCT_ERROR("102", "同步信息失败，请联系系统管理员"),

    /**
     * CRM can't find the corresponding opportunity Owner
     */
    NO_OPPORTUNITY_OWNER("103", "同步信息失败，请联系系统管理员"),

    /**
     * CRM can't find the payment term
     */
    NO_PAYMENT_TERM("104", "当前付款条款ID在CRM系统中不存在，请联系系统管理员到SF中补充"),

    /**
     * CRM can't find the tax classification
     */
    NO_TAX_CLASSIFICATION("105", "当前税分类在CRM系统中不存在，请联系系统管理员到SF中补充"),

    /**
     * 动态抓取代码处理Exception报错信息
     */
    CODE_EXCEPTION("106", "同步信息失败，请联系系统管理员"),
    ;

    private String code;

    private String desc;


    public static String getDescByCode(String code) {
        for (ConfirmHeaderStatusEnum value : ConfirmHeaderStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

}
