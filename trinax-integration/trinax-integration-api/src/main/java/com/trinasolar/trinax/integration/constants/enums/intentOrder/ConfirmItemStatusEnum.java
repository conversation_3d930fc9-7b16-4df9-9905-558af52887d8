package com.trinasolar.trinax.integration.constants.enums.intentOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 意向单取消时，返回状态码和对应的至尊宝提示文案
 */
@Getter
@AllArgsConstructor
public enum ConfirmItemStatusEnum {

    SUCCESS("SUCCESS", "取消意向单成功"),
    /**
     * The product is not found in the system
     */
    PRODUCT_NOT_USE("201", "当前产品不可用，同步信息失败，请联系系统管理员"),

    /**
     * Product quantity must be greater than zero
     */
    QUANTITY_LESS_THAN_ZERO ("202", ""),

    /**
     * There are contracts here that have been submitted for approval and are not allowed to be deleted
     */
    CONTRACT_SUBMITTED("203", ""),

    /**
     * No related product exists in the system
     */
    PRODUCT_NOT_EXIST("204", "当前产品不可用，同步信息失败，请联系系统管理员"),

    /**
     * 动态抓取保存数据的报错信息
     */
    CODE_EXCEPTION("205", "同步信息失败，请联系系统管理员"),
    ;

    private String code;

    private String desc;


    public static String getDescByCode(String code) {
        for (ConfirmItemStatusEnum value : ConfirmItemStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

}
