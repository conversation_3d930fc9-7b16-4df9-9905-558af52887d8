package com.trinasolar.trinax.integration.dto.input.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "签署文件请求")
@RequiredArgsConstructor(staticName = "builder")
@Accessors(chain = true)
public class SignFileReqDTO {

    @Schema(description = "当前登录人 id")
    private String currentUserId;

    @Schema(description = "当前登录人 名字")
    private String currentUserName;


    @Schema(description = "当前登录人 类型")
    private String currentUserType;

    @Schema(description = " 合同号")
    @NotBlank(message = "契约锁合同 id 不能为空")
    private String agreementId;

}
