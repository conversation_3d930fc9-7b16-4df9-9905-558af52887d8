package com.trinasolar.trinax.integration.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "获取产品文档列表入参")
public class GetProductDocsReqDTO {

    @NotNull(message = "productId不能为空")
    private String productId;

    @NotNull(message = "version不能为空")
    private String version;

    @NotNull(message = "regions不能为空")
    private String regions;

}
