package com.trinasolar.trinax.integration.api;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.constants.ServiceIds;
import com.trinasolar.trinax.integration.dto.input.intentOrder.IntendOrderConfirmReqDTO;
import com.trinasolar.trinax.integration.dto.input.intentOrder.ReceiveOpportunityLostReqDTO;
import com.trinasolar.trinax.integration.dto.output.IntendOrderConfirmResDTO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = ServiceIds.INTEGRATION_SERVICE)
public interface IntegrationIntentOrderFeign {

    @Operation(summary = "意向单确认")
    @PostMapping("/trina/intentOrder/confirm")
    Result<IntendOrderConfirmResDTO> intentOrderConfirm(@RequestBody IntendOrderConfirmReqDTO req);


    @Operation(summary = "意向单取消")
    @PostMapping("/trina/intentOrder/cancel")
    Result<Boolean> intentOrderCancel(@RequestBody ReceiveOpportunityLostReqDTO lost);

}
