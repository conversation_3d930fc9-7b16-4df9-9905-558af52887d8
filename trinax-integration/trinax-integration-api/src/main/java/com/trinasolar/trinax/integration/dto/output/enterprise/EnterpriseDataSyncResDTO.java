package com.trinasolar.trinax.integration.dto.output.enterprise;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class EnterpriseDataSyncResDTO {

    @Schema(description = "sfId")
    private String sfId;

    @Schema(description = "mdmId")
    private String mdmId;

    @Schema(description = "企业名称")
    private String name;

    @Schema(description = "税号")
    private String taxNumber;

    @Schema(description = "客户活跃状态")
    private String status;

    @Schema(description = "客户组织性质")
    private String customerOrgNature;

    @Schema(description = "业务线")
    private String businessLine;

    @Schema(description = "客户角色")
    private String customerRole;

    @Schema(description = "是否区域 KA")
    private String isRegionKa;

    @Schema(description = "客户登记")
    private String customerLevel;

    @Schema(description = "内部公司")
    private boolean intercompanyOrderType;

//    @Schema(description = "ouName")
//    private String ouName;

    @Schema(description = "erp 税率 id")
    private String taxId;

    @Schema(description = "erp 税率名称")
    private String taxName;

    @Schema(description = "币种")
    private String currency;

    @Schema(description = "工商注册号")
    private String regNumber;

    @Schema(description = "行业")
    private String industry;

    @Schema(description = "隶属集团 id")
    private String groupId;

    @Schema(description = "隶属集团名称")
    private String groupName;

    @Schema(description = "来源系统")
    private String sourceSystem;

    @Schema(description = "交易规模")
    private String transactionScale;

    @Schema(description = "主要分类")
    private String mainSegment;

    @Schema(description = "主要终端市场")
    private String mainMarketSegment;

    @Schema(description = "主要渠道类型")
    private String mainChannelType;

    @Schema(description = "客户建商状态")
    private String releaseStatus;

    @Schema(description = "addressList")
    private List<Address> addressList;

    @Schema(description = "message")
    private String message;

    @Schema(description = "sf 未处理的错误信息")
    private String errmsg;


    @Schema(description = "erp业务实体")
    private String erpOperatingUnit;

    @Schema(description = "erp业务实体名称")
    private String erpOperatingUnitName;
}
