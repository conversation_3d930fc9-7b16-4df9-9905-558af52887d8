package com.trinasolar.trinax.integration.dto.input.documentPreview;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "获取文档预览地址请求参数")
public class GetUrlReqDTO {

    @Schema(title = "文件全局ID", example = "trinasolarx_4a4c0aa618df")
    private String id;
    @Schema(title = "文件名称", example = "P02023090154203894152.pdf")
    private String name;
    @Schema(title = "文件大小", example = "0")
    private Long size;
    @Schema(title = "文件下载地址", example = "0")
    private String downloadUrl;
    @Schema(title = "创建用户", example = "I202310080002")
    private String creatorId;
    @Schema(title = "创建用户名称", example = "XXX")
    private String creatorName;
    @Schema(title = "当前登录用户", example = "I202310080002")
    private String userId;
    @Schema(title = "当前登录用户名称", example = "XXX")
    private String userName;

}
