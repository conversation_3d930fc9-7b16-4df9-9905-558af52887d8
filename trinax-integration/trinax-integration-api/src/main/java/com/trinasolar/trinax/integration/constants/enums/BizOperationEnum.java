package com.trinasolar.trinax.integration.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizOperationEnum {
    GET_CONTRACT_SIGN_URL("GET_CONTRACT_SIGN_URL", "获取合同签署地址"),
    GET_CONTRACT_VIEW_URL("GET_CONTRACT_VIEW_URL", "获取合同浏览地址"),
    CONTRACT_SINGLE_SIGN_NOTIFY("CONTRACT_SINGLE_SIGN_NOTIFY", "合同签署状态通知"),
    GET_CONTRACT_SO_INFO("GET_CONTRACT_SO_INFO", "获取合同发货运输信息"), //此处比较复杂，如果要按照请求状态处理，需要完全修改以前实现方式
    GET_CONTRACT_DELIVER_INFO("GET_CONTRACT_DELIVER_INFO", "获取物流信息"),
    GET_CONTRACT_REMAIN_QTY("GET_CONTRACT_REMAIN_QTY", "获取合同剩余数量"),//没有使用
    FEEDBACK_ENTERPRISE_INFO("FEEDBACK_ENTERPRISE_INFO", "回传客户企业"),
    SYNC_SALES_CONTRACT_INFO("SYNC_SALES_CONTRACT_INFO", "同步销售合同"),
    SYNC_FRAME_CONTRACT_INFO("SYNC_FRAME_CONTRACT_INFO", "同步框架合同"),
    SYNC_INTENT_ORDER_INFO("SYNC_INTENT_ORDER_INFO", "同步意向单"),
    SYNC_ENTERPRISE_INFO("SYNC_ENTERPRISE_INFO", "同步客户信息"),
    CREATE_ORDER_INFO("CREATE_ORDER_INFO", "创建so订单"),
    CREATE_ORDER_ITEM_INFO("CREATE_ORDER_ITEM_INFO", "创建so订单产品"),
    CANCEL_INTENT_ORDER("CANCEL_INTENT_ORDER", "取消意向单"),
    SYNC_ENTERPRISE_BUILD("SYNC_ENTERPRISE_BUILD", "同步建商"),
    SYNC_ENTERPRISE_CONTACT("SYNC_ENTERPRISE_CONTACT", "客户联系人同步"),
    SYNC_DR_INFO("SYNC_DR_INFO", "同步DR"),
    GET_PRODUCT_DOC_URL("GET_PRODUCT_DOC_URL", "获取文档的URL"),
    QUERY_PRODUCT_DOC("QUERY_PRODUCT_DOC", "查询产品文档"),
    SYNC_PRODUCT_INFO("SYNC_PRODUCT_INFO", "同步产品信息"),
    SYNC_STORAGE_PRODUCT_INFO("SYNC_STORAGE_PRODUCT_INFO", "同步工商储产品信息"),
    GET_QYS_CONTRACT_DETAIL("GET_QYS_CONTRACT_DETAIL", "获取契约锁合同文件列表"),
    CONTRACT_CHANGE_ITEM("CONTRACT_CHANGE_ITEM", "修改合同产品行"),
    SUBMIT_APPROVAL("SUBMIT_APPROVAL", "放货审批提交"),
    QRY_NO_RELEASE("QRY_NO_RELEASE", "查询 NoRelease"),
    QRY_BALANCE_RELEASE("QRY_BALANCE_RELEASE", "查询分销预付款余额"),
    EDIT_BALANCE_RELEASE("EDIT_BALANCE_RELEASE", "更新分销预付款余额"),
    SYNC_CONTRACT_TO_SF("SYNC_CONTRACT_TO_SF", "同步合同给SF"),
    BPM_STATUS_ACCESS("BPM_STATUS_ACCESS", "bpm状态变更"),
    SYNC_DELIVERY_AGREEMENT_INFO("SYNC_DELIVERY_AGREEMENT_INFO", "提货委托书签署完成通知"),
    SUBMIT_CONTRACT_APPROVAL("SUBMIT_CONTRACT_APPROVAL", "销售合同提交bpm审批"),
    QRY_SO_OCCUPIED("QRY_SO_OCCUPIED", "查询RDD ID的SO占用数量"),
    ;


    private String code;

    private String desc;


    public static String getDescByCode(String code) {
        for (BizOperationEnum value : BizOperationEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
