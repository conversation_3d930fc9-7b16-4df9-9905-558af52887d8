package com.trinasolar.trinax.integration.api;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.constants.ServiceIds;
import com.trinasolar.trinax.integration.dto.input.SfAccountBalanceEditReq;
import com.trinasolar.trinax.integration.dto.input.delivery.*;
import com.trinasolar.trinax.integration.dto.output.delivery.DeliverSyncResDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.LogisticsResDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.NoReleaseResDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.OrderSyncResDTO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

@FeignClient(name = ServiceIds.INTEGRATION_SERVICE)
public interface IntegrationDeliveryFeign {
    @Operation(summary = "确认发货")
    @PostMapping("/delivery/confirm")
    Result<String> deliveryConfirm(@RequestBody DeliveryHeadDTO req);

    @Operation(summary = "查询物流信息")
    @PostMapping("/delivery/queryLogistics")
    Result<LogisticsResDTO> queryLogistics(@RequestParam @NotBlank String wayBillNo);

    @Operation(summary = "根据发货申请，同步订单给SFDC--废弃")
    @PostMapping("/delivery/orderSync")
    Result<OrderSyncResDTO> orderSync(@RequestBody List<OrderSyncHeadDTO> req);

    @Operation(summary = "根据发货申请，同步订单产品给SFDC--废弃")
    @PostMapping("/delivery/orderProductSync")
    Result<OrderSyncResDTO> orderProductSync(@RequestBody List<OrderItemSyncRowDTO> req);


    @Operation(summary = "发货数据同步 SF")
    @PostMapping("/delivery/deliverSync")
    Result<DeliverSyncResDTO> deliverSync(@RequestBody List<DeliverSyncReqDTO> req);

    @Operation(summary = "根据发货申请，查询 sf noRelease ")
    @PostMapping("/delivery/qryNoRelease")
    Result<NoReleaseResDTO> qryNoRelease(@RequestBody NoReleaseQryReqDTO req);


    @Operation(summary = "根据mdmId查询企业的预付款余额")
    @GetMapping("/delivery/queryBalance")
    Result<BigDecimal> queryBalance(@RequestParam @NotBlank String enterpriseMdmId);

    @Operation(summary = "根据mdmId执行预付款扣减和回滚")
    @PostMapping("/delivery/editBalance")
    Result editBalance(@RequestBody SfAccountBalanceEditReq sfAccountBalanceEditReq);
}
