package com.trinasolar.trinax.integration.dto.output.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShipmentReleaseItemsDTO {

    @Schema(description = "合同号")
    private String pi;

    @Schema(description = "SO单号")
    private String so;

    @Schema(description = "产品名称")
    private String product;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "总功率")
    private String totalPower;

    @Schema(description = "产品功率")
    private Integer productPower;

    @Schema(description = "放货时间")
    private LocalDateTime deliveryTime;


}
