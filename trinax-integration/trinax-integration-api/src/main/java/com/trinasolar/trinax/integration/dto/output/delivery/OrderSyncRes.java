package com.trinasolar.trinax.integration.dto.output.delivery;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class OrderSyncRes{

    @Schema(description = "参考订单ID 唯一标志")
    private String referenceId;

    @Schema(description = "订单ID 创建成功时返回")
    private String id;

    @Schema(description = "错误信息数组")
    private List<OrderSyncErrors> errors;

}