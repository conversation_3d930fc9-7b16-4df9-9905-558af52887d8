package com.trinasolar.trinax.integration.dto.input.delivery;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@Schema(description = "订单信息")
public class OrderSyncHeadDTO {

    @Schema(description = "客户名")
    private String AccountId;

    @Schema(description = "合同")
    private String TS_Contract__c;

    @Schema(description = "币种")
    private String CurrencyIsoCode;

    @Schema(description = "区域")
    private String TS_Region__c;

    @Schema(description = "子区域")
    private String TS_SubRegion__c;

    @Schema(description = "货物到达区域")
    private String TS_Goods_Arrival_Region__c;

    @Schema(description = "货物到达子区域")
    private String TS_GoodsArrivalSubRegion__c;

    @Schema(description = "ERP销售账套")
    private String TS_ERP_OperatingUnit__c;

    @Schema(description = "价格手册ID")
    private String Pricebook2Id;

    @Schema(description = "订单类型")
    private String Type;

    @Schema(description = "状态")
    private String Status;

    @Schema(description = "业务机会Id")
    private String OpportunityId;

    @Schema(description = "订单开始日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private LocalDate EffectiveDate;

    @Schema(description = "贸易条款")
    private String TS_incoterm__c;

    @Schema(description = "销售人员")
    private String TS_Sales_Rep__c;

    @Schema(description = "渠道")
    private String TS_Channel__c;


    @Schema(description = "发货日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private LocalDate TS_Delivery_Date__c;


    @Schema(description = "发货日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private LocalDate TS_Scheduled_Delivery_Date__c;

    @Schema(description = "应用")
    private String TS_Application__c;

    @Schema(description = "收款银行")
    private String TS_Bank__c;

    @Schema(description = "安装国家")
    private String TS_CountryofInstallation__c;

    @Schema(description = "目的地")
    private String TS_Destination_Port__c;


    @Schema(description = "起运地")
    private String TS_Start_Port__c;

    @Schema(description = "收票地址")
    private String TS_BillToAddress__c;

    @Schema(description = "收货地址")
    private String TS_ShipToAddress__c;

    @Schema(description = "ERP Tax 类型ID")
    private String TS_Tax__c;

    @Schema(description = "ODM")
    private String TS_ODM__c;

    @Schema(description = "ERP订单类型")
    private String TS_Order_Type__c;

    @Schema(description = "记录类型ID")
    private String RecordTypeId;

    @Schema(description = "订单来源")
    private String TX_Order_Source__c;

    @Schema(description = "订单来源")
    private OrderSyncAttributes attributes;

}

