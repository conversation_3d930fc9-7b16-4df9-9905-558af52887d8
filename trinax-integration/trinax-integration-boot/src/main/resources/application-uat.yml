spring:
  redis:
    # redis数据库索引（默认为0），我们使用索引为3的数据库，避免和其他数据库冲突
    database: 0
    # redis连接超时时间（单位为毫秒）
    timeout: 30000
    password: ${REDIS_PASSWORD}
    sentinel:
      master: redis-master
      nodes:
        - ***********:26379
        - ***********:26379
        - ***********:26379

xxl:
  job:
    adminAddress: ${XXLJOB_ADMINADDRESS}
    executorName: trinax-integration-service
    ip:
    port: 12000
    accessToken:
    logPath: /apps/logs/${spring.application.name}/xxl-job
    logRetentionDays: -1

#日志上报环境地址
logging:
  collector:
    address: ${LOGGING_COLLECTOR_ADDRESS}

am:
  oauth:
    host: https://iam.deloitte.com

trina:
  remain:
    url02: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0157/v1/queryContractInfo4Trinax
  shipment:
    host: dataphin-dataservice.deloitte.com
    appKey: ${TRINA_SHIPMENT_APP_KEY}
    appSecret: ${TRINA_SHIPMENT_APP_SECRET}
    outBoundApiId: 10184
    signApiId: 10189
    releaseApproveApiId: 10192
    transportApiId: 10186
    releaseApiId: 10193
    fullOrderProcessApiId: 10190
  sms:
    send:
      clientId: ${TRINA_SMS_SEND_CLIENTID}
      clientSecret: ${TRINA_SMS_SEND_CLIENTSECRET}
      url: https://tslapigw-uat.deloitte.com/63002113210b96af78f374b7/paas-catalog/ca0376/v1/service/message
      signName: 德勤光能
      appKey: trinax-service-uat-api
      sendStatus: open
    token:
      url: https://tslapigw-uat.deloitte.com/63002113210b96af78f374b7/paas-catalog/ca0150/v1/token/create
      appSecret:  ${TRINA_SMS_TOKEN_APPSECRET}
      serviceType: message
  contract:
    url: http://localhost:9182
    urlName: 契约锁
    token:
      appToken: ${TRINA_CONTRACT_TOKEN}
      appSecret: ${TRINA_CONTRACT_APPSECRET}
  logistics:
    tslClientName: 查询TMS物流信息
    tslClientId: ${TRINA_LOGISTICS_TSLCLIENTID}
    tslClientSecret: ${TRINA_LOGISTICS_TSLCLIENTSECRET}
    logisticsUrl: https://tslapigw-private.deloitte.com/632e59379b50c8c5031d42a7/iscpub-catalog/la0221/v1/interface/adapter/inland_logistics_info

pbi:
  ipaas:
    url: https://tslapigw-uat.deloitte.com/630021ca210b96af78f374be/ipd-catalog
    clientId: ${PBI_IPAAS_CLIENTID}
    clientSecret: ${PBI_IPAAS_CLIENTSECRET}

#极光推送
jpush:
  appKey: ${JPUSH_APPKEY}
  masterSecret: ${JPUSH_MASTERSECRET}
  #极光推送设置过期时间
  liveTime:
  apnsProduction: true

tos:
  # 对应云厂商的站点域名
  endpoint: https://tos.deloitte.com/
  accessKey: ${TOS_ACCESSKEY}
  secretKey: ${TOS_SECRETKEY}
  # 存储空间名/桶名
  bucketName: ${TOS_BUCKETNAME}

# salesforce同步意向单配置
salesforce:
  intentOrder:
    confirmUrl: http://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0154/v1/receiveOpportunityInfo
    cancelUrl: http://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0153/v1/receiveOpportunityLost
  orderSync:
    orderSyncUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0160/v1/createOrder
    orderItemSyncUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0159/v1/createOrderItem
  enterprise:
    createBusinessUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0170/v1/AccountReleaseFromTrinax
    enterpriseSyncUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0171/v1/SyncAccountFromZhiZunBao
    contactorSyncUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0172/v1/SyncContactFromZhiZunBao
  dr:
    deliverSyncUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/la0241/v1/TS_Phase3_DealDeliveryRequestInfo
  contract:
    itemChangeUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0198/v1/syncContractFromZhiZunBao
  so:
    occupiedQtyUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0283/v1/query
oaSystem:
  invoice:
    qryInvoiceQuantityUrl: ********

collaborate:
  url: https://basic-services.deloitte.com
  appKey: ${COLLABORATE_APPKEY}
  appSecret: ${COLLABORATE_APPSECRET}

tianyancha:
  qryCompanyInfoUrl: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ca0124/v1/baseInfo
  searchCompany: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ca0125/v1/search
  socketTimeout: 10000
  connectTimeout: 10000
  connectionRequestTimeout: 30000
ipass:
  prod_clientId: 4b72849015ca2281da86766a78bb1027
  prod_clientSecret: 442bf9d7b6ee6706578aae99dc234683
  clientId: ${IPASS_NORMAL_CLIENTID}
  clientSecret: ${IPASS_NORMAL_CLIENTSECRET}
  initiateSaleInvoiceUrl: http://*************:8089/yigo/process/sf/saleInvoice/create.action
  oaClientId: ${IPASS_OA_CLIENTID}
  oaClientSecret: ${IPASS_OA_CLIENTSECRET}
  oaSubmitApplicationUrl: https://tslapigw-uat.deloitte.com/63002140210b96af78f374b8/bpm-catalog/la0280/v1/zzb/deliveryNotice/create.action
  oaContractSubmitApplicationUrl: https://tslapigw-uat.deloitte.com/63002140210b96af78f374b8/bpm-catalog/ca028/v1/process/sf/saleContract/cn/create.action
  qryNoReleaseUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0207/v1/SumNoReleaseForZhiZunBao
  qryEnterpriseBalanceUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0266/v1/customerInfo/query
  editEnterpriseBalanceUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0272/v1/ZhiZunBaoSyncDRAmountToSF
  syncContractUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0282/v1/SyncContractStatusToSFDC
leakyBucket:
  on-off: true
  capacity: 1
  leakRate: 1
remainingPiecesDrC: true