spring:
  redis:
    # redis数据库索引（默认为0），我们使用索引为3的数据库，避免和其他数据库冲突
    database: 0
    # redis连接超时时间（单位为毫秒）
    timeout: 30000
    password: ${REDIS_PASSWORD}
    sentinel:
      master: redis-master
      nodes:
        - *************:26379
        - *************:26379
        - *************:26379

xxl:
  job:
    adminAddress: ${XXLJOB_ADMINADDRESS}
    executorName: trinax-integration-service
    ip:
    port: 12000
    accessToken:
    logPath: /apps/logs/${spring.application.name}/xxl-job
    logRetentionDays: -1

#日志上报环境地址
logging:
  collector:
    address: ${LOGGING_COLLECTOR_ADDRESS}

am:
  oauth:
    host: https://iam.deloitte.com

trina:
  remain:
    url02: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ma0157/v1/queryContractInfo4Trinax
  shipment:
    host: dataphin-dataservice.deloitte.com
    appKey: ${TRINA_SHIPMENT_APP_KEY}
    appSecret: ${TRINA_SHIPMENT_APP_SECRET}
    outBoundApiId: 10199
    signApiId: 10196
    releaseApproveApiId: 10204
    transportApiId: 10197
    releaseApiId: 10203
    fullOrderProcessApiId: 10195
  sms:
    send:
      clientId: ${TRINA_SMS_SEND_CLIENTID}
      clientSecret: ${TRINA_SMS_SEND_CLIENTSECRET}
      url: https://tslapigw.deloitte.com/632e58a6ea9347760d64c4e8/paaspub-catalog/ca0376/v1/service/message
      signName: 德勤光能
      appKey: trinax-service-api
      sendStatus: open
    token:
      url: https://tslapigw.deloitte.com/632e58a6ea9347760d64c4e8/paaspub-catalog/ca0150/v1/token/create
      appSecret: ${TRINA_SMS_TOKEN_APPSECRET}
      serviceType: message
  contract:
    url: http://esign-api.deloitte.com
    urlName: 契约锁
    token:
      appToken: ${TRINA_CONTRACT_TOKEN}
      appSecret: ${TRINA_CONTRACT_APPSECRET}
  logistics:
    tslClientName: 查询TMS物流信息
    tslClientId: ${TRINA_LOGISTICS_TSLCLIENTID}
    tslClientSecret: ${TRINA_LOGISTICS_TSLCLIENTSECRET}
    logisticsUrl: https://tslapigw-private.deloitte.com/632e59379b50c8c5031d42a7/iscpub-catalog/la0221/v1/interface/adapter/inland_logistics_info

pbi:
  ipaas:
    url: https://tslapigw-private.deloitte.com/632e5971ea9347760d64c4ec/ipdpub-catalog
    clientId: ${PBI_IPAAS_CLIENTID}
    clientSecret: ${PBI_IPAAS_CLIENTSECRET}

#极光推送
jpush:
  appKey: ${JPUSH_APPKEY}
  masterSecret: ${JPUSH_MASTERSECRET}
  #极光推送设置过期时间
  liveTime:
  apnsProduction: true

tos:
  # 对应云厂商的站点域名
  endpoint: https://tos.deloitte.com/
  accessKey: ${TOS_ACCESSKEY}
  secretKey: ${TOS_SECRETKEY}
  # 存储空间名/桶名
  bucketName: ${TOS_BUCKETNAME}

# salesforce同步意向单配置
salesforce:
  intentOrder:
    confirmUrl: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ma0154/v1/receiveOpportunityInfo
    cancelUrl: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ma0153/v1/receiveOpportunityLost
  orderSync:
    orderSyncUrl: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ma0160/v1/createOrder
    orderItemSyncUrl: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ma0159/v1/createOrderItem
  enterprise:
    createBusinessUrl: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ma0170/v1/AccountReleaseFromTrinax
    enterpriseSyncUrl: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ma0171/v1/SyncAccountFromZhiZunBao
    contactorSyncUrl: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ma0172/v1/SyncContactFromZhiZunBao
  dr:
    deliverSyncUrl: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/la0241/v1/TS_Phase3_DealDeliveryRequestInfo
  contract:
    itemChangeUrl: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ma0198/v1/syncContractFromZhiZunBao

oaSystem:
  invoice:
    qryInvoiceQuantityUrl: ********

collaborate:
  url: https://basic-services.deloitte.com
  appKey: ${COLLABORATE_APPKEY}
  appSecret: ${COLLABORATE_APPSECRET}
tianyancha:
  qryCompanyInfoUrl: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ca0124/v1/baseInfo
  searchCompany: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ca0125/v1/search
  socketTimeout: 10000
  connectTimeout: 10000
  connectionRequestTimeout: 30000
knife4j:
  production: true
ipass:
  clientId: ${IPASS_NORMAL_CLIENTID}
  clientSecret: ${IPASS_NORMAL_CLIENTSECRET}
  initiateSaleInvoiceUrl: https://bpm.deloitte.com/yigo/process/sf/saleInvoice/create.action
  oaClientId: ${IPASS_OA_CLIENTID}
  oaClientSecret: ${IPASS_OA_CLIENTSECRET}
  oaSubmitApplicationUrl: https://tslapigw-private.deloitte.com/632e58b9ea9347760d64c4e9/bpmpub-catalog/la0280/v1/zzb/deliveryNotice/create.action
  qryNoReleaseUrl: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ma0207/v1/SumNoReleaseForZhiZunBao
  qryEnterpriseBalanceUrl: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ma0266/v1/customerInfo/query
  editEnterpriseBalanceUrl: https://tslapigw-private.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ma0272/v1/ZhiZunBaoSyncDRAmountToSF
  oaContractSubmitApplicationUrl: https://tslapigw.deloitte.com/632e58b9ea9347760d64c4e9/bpmpub-catalog/ca028/v1/process/sf/saleContract/cn/create.action
  syncContractUrl: https://tslapigw.deloitte.com/632e5910456440c1e9a1d379/mtcpub-catalog/ma0282/v1/SyncContractStatusToSFDC
leakyBucket:
  on-off: true
  capacity: 30
  leakRate: 3
remainingPiecesDrC: true