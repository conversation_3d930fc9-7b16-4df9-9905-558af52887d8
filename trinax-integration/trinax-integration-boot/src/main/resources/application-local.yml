server:
  port: 8883
spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  redis:
    # redis数据库索引（默认为0），我们使用索引为3的数据库，避免和其他数据库冲突
    database: 0
    # redis服务器地址（默认为localhost）
    host: ${REDIS_HOST:*************}
    # redis端口（默认为6379）
    port: ${REDIS_PORT:6379}
    # redis访问密码（默认为空）
#    password: ${REDIS_PASSWORD:Trina@123}
    # redis连接超时时间（单位为毫秒）
    timeout: 30000

xxl.job:
  admin:
    #    addresses: http://xxl-job-admin:8080/xxl-job-admin
    addresses: http://xxl-job-admin:8080/xxl-job-admin
  accessToken:
  executor:
    appname: integration-service
    address:
    ip:
    port: 7790
    logpath: /tmp/xxl_log
    logretentiondays: 30
#日志上报环境地址
logging:
  collector:
    #dev
    address: localhost:8088

am:
  oauth:
    host: https://iam.deloitte.com
trina:
  sf:
    editBalaceFail: false
    deliverSyncFail: false
  bpm:
    submitApproveFail: false
  remain:
    url02: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0157/v2/queryContractInfo4TrinaxOtherEnv
  shipment:
    host: dataphin-dataservice.deloitte.com
    appKey: ${TRINA_SHIPMENT_APP_KEY:200000140}
    appSecret: ${TRINA_SHIPMENT_APP_SECRET:7a515744b66a4e44a1af2be7aaf78fda}
    outBoundApiId: 10184
    signApiId: 10189
    releaseApproveApiId: 10192
    transportApiId: 10186
    releaseApiId: 10193
    fullOrderProcessApiId: 10190
  sms:
    send:
      clientId: ${TRINA_SMS_SEND_CLIENTID:""}
      clientSecret: ${TRINA_SMS_SEND_CLIENTSECRET:""}
      url: https://tslapigw-uat.deloitte.com/63002113210b96af78f374b7/paas-catalog/ca0376/v1/service/message
      signName: 德勤光能
      appKey: trinax-service-api
      sendStatus: closed
    token:
      url: https://tslapigw-uat.deloitte.com/63002113210b96af78f374b7/paas-catalog/ca0150/v1/token/create
      appSecret: ${TRINA_SMS_TOKEN_APPSECRET:""}
      serviceType: message
  contract:
    url: http://localhost:9182
    urlName: 契约锁-获取预签署地址
    token:
      appToken: ${TRINA_CONTRACT_TOKEN:""}
      appSecret: ${TRINA_CONTRACT_APPSECRET:""}
  logistics:
    tslClientId: ${TRINA_LOGISTICS_TSLCLIENTID:""}
    tslClientSecret: ${TRINA_LOGISTICS_TSLCLIENTSECRET:""}
    logisticsUrl: ${TRINA_LOGISTICS_LOGISTICSURL:""}

pbi:
  ipaas:
    url: ${PBI_IPAAS_URL:""}
    clientId: ${PBI_IPAAS_CLIENTID:""}
    clientSecret: ${PBI_IPAAS_CLIENTSECRET:""}

#极光推送
jpush:
  appKey: ${JPUSH_APPKEY:f2dfdb499873624fa42f10a4}
  masterSecret: ${JPUSH_MASTERSECRET:a99dbfb87025a68566987ee7}
  #极光推送设置过期时间
  liveTime:
  apnsProduction: false

tos:
  # 对应云厂商的站点域名
  endpoint: https://tos.deloitte.com/
  accessKey: t*****
  secretKey: nin***********rm
  # 存储空间名/桶名
  bucketName: nin***********rm

# salesforce同步意向单配置
salesforce:
  intentOrder:
    confirmUrl: ""
    cancelUrl: ""
  orderSync:
    orderSyncUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0160/v1/createOrder
    orderItemSyncUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0159/v1/createOrderItem
  enterprise:
    createBusinessUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0170/v1/AccountReleaseFromTrinax
    enterpriseSyncUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0171/v1/SyncAccountFromZhiZunBao
    contactorSyncUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0172/v1/SyncContactFromZhiZunBao
  dr:
    deliverSyncUrl:
  contract:
    itemChangeUrl: https://deloitte--test01.sandbox.my.salesforce.com/services/apexrest/TS_Phase3/SyncContractFromZhiZunBao
  so:
    occupiedQtyUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0283/v1/query

oaSystem:
  invoice:
    qryInvoiceQuantityUrl: ********

collaborate:
  url: https://basic-services.deloitte.com
  appKey: ${COLLABORATE_APPKEY:test-service-api}
  appSecret: ${COLLABORATE_APPSECRET:6b8a1f89-6fad-45af-b687-8375d33e7762}
tianyancha:
  qryCompanyInfoUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ca0124/v1/baseInfo
  searchCompany: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ca0125/v1/search
  socketTimeout: 1000
  connectTimeout: 1000
  connectionRequestTimeout: 3000
ipass:
  prod_clientId: 4b72849015ca2281da86766a78bb1027
  prod_clientSecret: 442bf9d7b6ee6706578aae99dc234683
  clientId: ${IPASS_NORMAL_CLIENTID:4b72849015ca2281da86766a78bb1027}
  clientSecret: ${IPASS_NORMAL_CLIENTSECRET:442bf9d7b6ee6706578aae99dc234683}
  initiateSaleInvoiceUrl: http://*************:8089/yigo/process/sf/saleInvoice/create.action
  oaClientId: ${IPASS_OA_CLIENTID:4b72849015ca2281da86766a78bb1027}
  oaClientSecret: ${IPASS_OA_CLIENTSECRET:442bf9d7b6ee6706578aae99dc234683}
  oaSubmitApplicationUrl: https://tslapigw-uat.deloitte.com/63002140210b96af78f374b8/bpm-catalog/la0280/v1/zzb/deliveryNotice/create.action
  oaContractSubmitApplicationUrl: https://tslapigw-uat.deloitte.com/63002140210b96af78f374b8/bpm-catalog/ca028/v1/process/sf/saleContract/cn/create.action
  qryNoReleaseUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0207/v1/SumNoReleaseForZhiZunBao
  qryEnterpriseBalanceUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0266/v1/customerInfo/query
  editEnterpriseBalanceUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0272/v1/ZhiZunBaoSyncDRAmountToSF
  syncContractUrl: https://tslapigw-uat.deloitte.com/6300219d210b96af78f374bb/mtc-catalog/ma0282/v1/SyncContractStatusToSFDC
leakyBucket:
  on-off: true
  capacity: 1
  leakRate: 1
remainingPiecesDrC: true


