package com.trinasolar.trinax.config;

import dtt.asset.leakybucket.LeakyBucket;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class LeakyBucketConfig {
    //桶容量
    @Value("${leakyBucket.capacity}")
    Integer capacity;

    //流速
    @Value("${leakyBucket.leakRate}")
    Integer leakRate;

    @Bean("tianyanLeakyBucket")
    public LeakyBucket leakyBucket() {
        return new LeakyBucket(capacity, leakRate);
    }
}