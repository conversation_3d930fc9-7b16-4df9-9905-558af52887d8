package com.trinasolar.trinax.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;

/**
 * Created on 2024/12/19, 9:24
 * author: zhangyang
 */
public class QysDecodeUtil {

    /**
     * 电子签署平台回调解密算法
     * @param encryptedText 待解密字符串
     * @param aesKey 回调配置的aesKey
     * @param token  回调配置的token
     */
    public String decrypt(String encryptedText, String aesKey, String token) throws Exception {
        byte[] keyBytes = aesKey.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, 0, 16, "AES");

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        byte[] ivBytes = getHashBytes(token);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes, 0, 16);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);

        byte[] encryptedBytes = Base64.getDecoder().decode(encryptedText);

        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);

        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }
    public byte[] getHashBytes(String source) throws Exception {
        MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
        return messageDigest.digest(source.getBytes(StandardCharsets.UTF_8));
    }
}
