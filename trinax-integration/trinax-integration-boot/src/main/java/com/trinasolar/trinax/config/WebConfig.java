package com.trinasolar.trinax.config;

import dtt.asset.leakybucket.LeakyBucketLimiterInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @date 21/11/2023
 * @description
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {


    @Autowired
    private TokenVerificationInterceptor tokenVerificationInterceptor;
    @Autowired
    private LeakyBucketLimiterInterceptor leakyBucketLimiterInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tokenVerificationInterceptor).addPathPatterns("/**");
        registry.addInterceptor(leakyBucketLimiterInterceptor).addPathPatterns("/**");
    }
}
