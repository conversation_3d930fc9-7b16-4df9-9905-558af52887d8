package com.trinasolar.trinax.config;

import com.trinasolar.trinax.auth.dto.output.SysApiClientDetailResDTO;
import com.trinasolar.trinax.auth.dto.output.SysApiClientResDTO;
import com.trinasolar.trinax.auth.dto.output.SysApiTokenResDTO;
import com.trinasolar.trinax.integration.manager.SysApiTokenService;
import com.trinasolar.trinax.utils.ShiroSimpleHashUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 21/11/2023
 * @description
 */
@Component
@Slf4j
public class TokenVerificationInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private SysApiTokenService sysApiTokenService;


    /**
     * @param request
     * @param response
     * @param handler  访问的目标方法
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        if (handler instanceof HandlerMethod) {
            final HandlerMethod handlerMethod = (HandlerMethod) handler;
            final Method method = handlerMethod.getMethod();
            TokenVerification tokenVerificationByMethod = AnnotationUtils.findAnnotation(method, TokenVerification.class);
            if (Objects.isNull(tokenVerificationByMethod)) {
                return true;
            }

            //获取参数
            String appId = request.getHeader("zzb-appid");
            String token = request.getHeader("zzb-token");
            log.info("开始验证API token流程：appId={}",appId);
            Assert.isTrue(!StringUtils.isEmpty(token), "参数为空：token不能为空");
            Assert.isTrue( !StringUtils.isEmpty(appId), "参数为空：appId不能为空");

            //判断应用是否存在
            SysApiClientResDTO client = sysApiTokenService.findClientByAppId(appId);
            Assert.notNull(client, "参数错误：appId错误");

            SysApiClientDetailResDTO detail = sysApiTokenService.findClientDetailByAppId(appId);
            Assert.notNull(client, "参数错误：appId查询应用错误");
            boolean tokenFlag = token.equals(detail.getAccessToken());
            Assert.isTrue(tokenFlag, "参数错误：token错误");

            boolean validity = null != detail.getAccessTokenValidity() && detail.getAccessTokenValidity() > 0;
            Assert.isTrue(validity, "参数错误：token过期");
            //判断是否存在token
            ValueOperations<String, SysApiTokenResDTO> tokenRedis = redisTemplate.opsForValue();
            String key = ShiroSimpleHashUtil.simpleHash(appId  + token);
            SysApiTokenResDTO tokenInfo = tokenRedis.get(key);
            if(ObjectUtils.isEmpty(tokenInfo)){
                SysApiTokenResDTO sysApiTokenResDTO = new SysApiTokenResDTO();
                sysApiTokenResDTO.setAccessToken(detail.getAccessToken());
                sysApiTokenResDTO.setAppId(detail.getAppId());
                sysApiTokenResDTO.setAppKey(client.getAppKey());
                sysApiTokenResDTO.setAppName(client.getAppName());
                sysApiTokenResDTO.setAccessTokenValidity(detail.getAccessTokenValidity());
                tokenRedis.set(key, sysApiTokenResDTO, detail.getAccessTokenValidity(), TimeUnit.DAYS);
            }
            log.info("结束验证API token流程");
        }


        return super.preHandle(request, response, handler);
    }

}