package com.dtt.dttframeworkxxljob.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Configuration
@EnableConfigurationProperties(XxljobProp.class)
@Slf4j
public class XxljobAutoConfig {

	@Resource
	XxljobProp xxljobProp;
	@Bean
	public XxlJobSpringExecutor xxlJobExecutor() {
		log.info("xxl-job Starting Config Init......");

		XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
		xxlJobSpringExecutor.setAdminAddresses(xxljobProp.getAdminAddress());
		xxlJobSpringExecutor.setAppname(xxljobProp.getExecutorName());
		xxlJobSpringExecutor.setIp(NetworkHelper.getLocalIP());
		xxlJobSpringExecutor.setPort(xxljobProp.getPort());
		xxlJobSpringExecutor.setAccessToken(xxljobProp.getAccessToken());
		xxlJobSpringExecutor.setLogPath(xxljobProp.getLogPath());
		xxlJobSpringExecutor.setLogRetentionDays(xxljobProp.getLogRetentionDays());

		log.info("xxl-job Starting Config Init Done......");
		return xxlJobSpringExecutor;
	}
}
