<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>dtt.asset</groupId>
        <artifactId>dtt-base</artifactId>
        <version>0.0.2.RELEASE</version>
        <relativePath>../../dtt-base</relativePath><!-- lookup parent from repository -->
    </parent>
    <artifactId>dtt-framework-xxljob</artifactId>
    <version>0.0.2.RELEASE</version>
    <name>dtt-framework-xxljob</name>
    <description>dtt-framework-xxljob</description>
    <properties>
        <java.version>8</java.version>
    </properties>
    <dependencies>

        <!-- https://mavenlibs.com/maven/dependency/com.xuxueli/xxl-job-core -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>${xxljob.version}</version>
        </dependency>
        <!--<dependency>-->
            <!--<groupId>dtt.asset</groupId>-->
            <!--<artifactId>dtt-spring-boot-starter</artifactId>-->
        <!--</dependency>-->
    </dependencies>
</project>
