package dtt.segment.id.generator.config;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * IdGeneratorConfig
 *
 * @author: Su, Hongyi
 * @date: 2021/5/24 10:47
 * @version: 1.0
 */
@Configuration
@EnableConfigurationProperties({IdGeneratorConfiguration.class})
public class IdGeneratorConfig {

    @Bean
    public ExecutorService fetchNextSegmentExecutor() {
        AtomicInteger threadIncr = new AtomicInteger(0);
        return new ThreadPoolExecutor(2, 4, 5, TimeUnit.MINUTES, new SynchronousQueue<>(), r -> {
            Integer incr = null;
            synchronized (threadIncr) {
                incr = threadIncr.incrementAndGet();
                if (incr >= 500) {
                    threadIncr.set(0);
                    incr = 1;
                }
            }
            return new Thread(r, "fetch-next-segment-thread-" + incr);
        }, new ThreadPoolExecutor.CallerRunsPolicy());
    }

}
