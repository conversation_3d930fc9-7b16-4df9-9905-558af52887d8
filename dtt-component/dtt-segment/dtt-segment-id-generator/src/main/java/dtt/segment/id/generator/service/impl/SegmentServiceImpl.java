package dtt.segment.id.generator.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import dtt.segment.id.generator.config.IdGeneratorConfiguration;
import dtt.segment.id.generator.dto.SegmentId;
import dtt.segment.id.generator.dto.SegmentParam;
import dtt.segment.id.generator.entity.Segment;
import dtt.segment.id.generator.mapper.SegmentMapper;
import dtt.segment.id.generator.service.ISegmentService;
import dtt.segment.id.generator.utils.SegmentUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * SegmentServiceImpl
 *
 * @author: Su, Hongyi
 * @date: 2021/5/28 11:34
 * @version: 1.0
 */
@Slf4j
@Service
public class SegmentServiceImpl implements ISegmentService {

    /**
     * businessType在数据库表是否存在缓存
     */
    private static final ConcurrentMap<String, Boolean> EXIST_CACHE = new ConcurrentHashMap();

    @Autowired
    private IdGeneratorConfiguration idGeneratorConfiguration;
    @Autowired
    private SegmentMapper segmentMapper;

    /**
     * 不能参与其他事务
     * 防止被其他事务回滚
     * @param businessType 业务
     * @param paramKey 配置项
     * @return
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @Override
    public SegmentId fetchNextSegmentId(String businessType, String paramKey) {
        SegmentParam segmentParam = SegmentUtils.getSegmentParam(idGeneratorConfiguration.getSegments(), paramKey);

        // 初始化号段
        initSegment(businessType, segmentParam);

        // 至少试2次
        int retryTimes = segmentParam.getRetryTimes();
        synchronized (SegmentUtils.getInitSegmentKey(businessType)) {
            for (int i = 0; i < retryTimes; i++) {
                Segment segment = segmentMapper.selectOne(Wrappers.<Segment>lambdaQuery().eq(Segment::getBusinessType, businessType));
                if (segment == null) {
                    throw new RuntimeException("不能从数据库中找到号段【" + businessType + "】");
                } else if (segment.getLimitId() != null && segment.getMaxId() >= segment.getLimitId()) {
                    throw new RuntimeException("号段已达极限值【" + businessType + "】");
                }

                long maxId = segment.getMaxId() + segment.getStep();
                if (segment.getLimitId() != null) {
                    maxId = Math.min(maxId, segment.getLimitId());
                }

                Segment updateSegment = new Segment();
                updateSegment.setId(segment.getId());
                updateSegment.setMaxId(maxId);
                updateSegment.setStep(segment.getStep());
                updateSegment.setVersion(segment.getVersion());
                updateSegment.setUpdateTime(new Date());
                int count = segmentMapper.updateMaxId(updateSegment);
                if (count == 1) {
                    return SegmentUtils.convert(updateSegment, segmentParam);
                } else {
                    log.info("未获取到号段乐观锁【" + businessType + "】，retryTimes=" + (i + 1));
                }
            }
        }

        // 多次尝试仍未获取到segment
        throw new RuntimeException("多次尝试仍未获取到segment【" + retryTimes + "次】");
    }

    /**
     * 判断号段是否存在，如果未存在则初始化
     */
    private void initSegment(String businessType, SegmentParam segmentParam) {
        if (segmentParam.getAutoInit() == Boolean.FALSE) {
            return;
        }

        Boolean flag = EXIST_CACHE.get(businessType);
        if (flag != null) {
            return;
        }

        synchronized (SegmentUtils.getInitSegmentKey(businessType)) {
            flag = EXIST_CACHE.get(businessType);
            if (flag != null) {
                return;
            }

            Segment exitSegment = segmentMapper.selectOne(Wrappers.<Segment>lambdaQuery().eq(Segment::getBusinessType, businessType));
            if (exitSegment != null) {
                EXIST_CACHE.put(businessType, Boolean.TRUE);
                return;
            }

            log.info("初始化号段开始，businessType={}", businessType);
            Date now = new Date();
            Segment segment = new Segment();
            segment.setMaxId(0L);
            segment.setBusinessType(businessType);
            segment.setVersion(1L);
            segment.setStep(segmentParam.getStep());
            segment.setLimitId(segmentParam.getLimitId());
            segment.setCreateTime(now);
            segment.setUpdateTime(now);
            try {
                segmentMapper.insert(segment);
                log.info("初始化号段成功，businessType={}", businessType);
            } catch (DuplicateKeyException e) {
                log.info("初始化号段重复，businessType={}", businessType);
            }
            EXIST_CACHE.put(businessType, Boolean.TRUE);
        }
    }

}
