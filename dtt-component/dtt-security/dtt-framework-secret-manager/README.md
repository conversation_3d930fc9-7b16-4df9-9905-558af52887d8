# Deloitte Notification component  <br>

# 1.Introduction
Upon startup, call the cloud vendor KMS service to obtain the security certificate

# 2.Compilation

## 2.1 How to compile modules?

* How to install to local repository only?

```shell
cd deloitte-framework-secret-manager
mvn install -f pom.xml '-Dmaven.test.skip=true'
```

* How to upload to private maven server?

```shell
cd deloitte-framework-secret-manager
mvn deploy -f pom.xml '-Dmaven.test.skip=true'
```

# 3.Modules Introduction

## Directory structure ##

```bash
|   pom.xml
\---src
    +---main
    |   +---java
    |   |   \---com
    |   |       \---deloitte
    |   |           \---secret
    |   |               +---config
    |   |               |       DttSecretConfig.java                    # Parameter file listener startup configuration
    |   |               |       FileChangeListener.java                 # Parameter file listening
    |   |               |       PropertyPreparedListener.java           # ApplicationPreparedEvent event listening
    |   |               |       SecretProperty.java                     # Parameter encapsulation
    |   |               |
    |   |               \---service
    |   |                   |   SecretService.java                      # Cloud vendor KMS service unified query interface 
    |   |                   |
    |   |                   \---impl
    |   |                           SecretServiceAliImpl.java           # Ali Cloud KMS service implementation
    |   |                           SecretServiceAWSImpl.java           # Amazon KMS service implementation
    |   |                           SecretServiceAzureImpl.java         # Azure key-vault service implementation
    |   |                           SecretServiceAzureImpl.java         # Parameter file reading
    |   |                           SecretServiceHuaweiImpl.java        # huawei Cloud KMS service implementation
    |   |                           SecretServiceTencentImpl.java       # tencent Cloud Secrets Manager service implementation
    |   |
    |   \---resources
    |       \---META-INF
    |               spring.factories                                    # Spring boot auto assemble configuration 
    |
    \---test
        \---java
            \---com
                \---deloitte
                    \---secret
                            SecretServiceTest.java                      # test class
```

## Design Logic
1. Use [spring.factories](src%2Fmain%2Fresources%2FMETA-INF%2Fspring.factories) auto assemble spring container.
2. [PropertyPreparedListener.java](src%2Fmain%2Fjava%2Fcom%2Fdeloitte%2Fsecret%2Fconfig%2FPropertyPreparedListener.java) :Invoke the configured cloud vendor KSM service to replace configuration file parameters 
3. [SecretService.java](src%2Fmain%2Fjava%2Fcom%2Fdeloitte%2Fsecret%2Fservice%2FSecretService.java) : Parameter query interface definition

# 4. References
1. [Aliyun-KMS-SDK](https://help.aliyun.com/document_detail/28956.html)
2. [Amazon-KMS-DOC](https://docs.aws.amazon.com/zh_cn/kms/latest/developerguide/overview.html)
3. [Azure-key-vault](https://learn.microsoft.com/zh-cn/azure/key-vault/secrets/about-secrets)
4. [huawei-KMS](https://support.huaweicloud.com/productdesc-dew/dew_01_0001.html)
5. [Tencent-SSM](https://cloud.tencent.com/document/product/1140/40278)

# 5.Q&A
Comming soon!

