package dtt.secret;

import com.aliyuncs.kms.secretsmanager.client.exception.CacheSecretException;
import com.amazonaws.regions.Regions;
import dtt.secret.config.SecretProperty;
import dtt.secret.service.impl.SecretServiceAWSImpl;
import dtt.secret.service.impl.SecretServiceAliImpl;
import dtt.secret.service.impl.SecretServiceAzureImpl;
import dtt.secret.service.impl.SecretServiceHuaweiImpl;
import dtt.secret.service.impl.SecretServiceTencentImpl;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;

public class SecretServiceTest {

    @Test
    void testAZ(){
        SecretProperty secretProperty = new SecretProperty();
        secretProperty.addProperties(SecretProperty.AZ_KEY_VALUE_URL,"https://xxx.com");
        String value = new SecretServiceAzureImpl(secretProperty).getParameter("xxx");
        Assertions.assertEquals(value,null);
    }

    @Test
    void testAWS1(){
        SecretProperty secretProperty = new SecretProperty();
        secretProperty.addProperties(SecretProperty.AWS_REGION, Regions.AP_NORTHEAST_1.getName());
        secretProperty.addProperties(SecretProperty.AWS_ACCESS_KEY,"accessKey");
        secretProperty.addProperties(SecretProperty.AWS_SECRET_KEY,"secretKey");
        String value = new SecretServiceAWSImpl(secretProperty).getParameter("userName");
        Assertions.assertEquals(value,null);
    }

    @Test
    void testAWS2(){
        //角色授权访问
        SecretProperty secretProperty = new SecretProperty();
        secretProperty.addProperties(SecretProperty.AWS_REGION, Regions.AP_NORTHEAST_1.getName());
        String value = new SecretServiceAWSImpl(secretProperty).getParameter("userName");
        Assertions.assertEquals(value,null);
    }

    @Test
    void testAli1() throws IOException, CacheSecretException {
        SecretProperty secretProperty = new SecretProperty();
        secretProperty.addProperties(SecretProperty.AL_CREDENTIALS_TYPE,"ak");
        secretProperty.addProperties(SecretProperty.AL_REGION, "cn-beijing");

        secretProperty.addProperties(SecretProperty.AL_ACCESS_KEY,"accessKey");
        secretProperty.addProperties(SecretProperty.AL_SECRET_KEY,"secretKey");

        String value = new SecretServiceAliImpl(secretProperty).getParameter("userName");
        Assertions.assertEquals(value,null);
    }

    @Test
    void testAli2() throws IOException, CacheSecretException {
        SecretProperty secretProperty = new SecretProperty();
        //ram_role sts 一样
        secretProperty.addProperties(SecretProperty.AL_CREDENTIALS_TYPE,"sts");
        secretProperty.addProperties(SecretProperty.AL_REGION, "cn-beijing");

        secretProperty.addProperties(SecretProperty.AL_ACCESS_KEY,"accessKey");
        secretProperty.addProperties(SecretProperty.AL_SECRET_KEY,"secretKey");
        secretProperty.addProperties(SecretProperty.AL_ROLE_NAME,"roleSessionName");
        secretProperty.addProperties(SecretProperty.AL_ROLE_ARN,"roleArn");
        secretProperty.addProperties(SecretProperty.AL_POLICY,"policy");

        String value = new SecretServiceAliImpl(secretProperty).getParameter("userName");
        Assertions.assertEquals(value,null);

    }

    @Test
    void testAli3() throws IOException, CacheSecretException {
        SecretProperty secretProperty = new SecretProperty();
        secretProperty.addProperties(SecretProperty.AL_CREDENTIALS_TYPE,"ecs_ram_role");
        secretProperty.addProperties(SecretProperty.AL_REGION, "cn-beijing");

        secretProperty.addProperties(SecretProperty.AL_ROLE_NAME,"roleSessionName");
        String value = new SecretServiceAliImpl(secretProperty).getParameter("userName");
        Assertions.assertEquals(value,null);
    }

    @Test
    void testAli4() throws IOException, CacheSecretException {
        SecretProperty secretProperty = new SecretProperty();
        secretProperty.addProperties(SecretProperty.AL_CREDENTIALS_TYPE,"client_key");
        secretProperty.addProperties(SecretProperty.AL_REGION, "cn-beijing");

        secretProperty.addProperties(SecretProperty.AL_CLIENT_KEY_FILE_PATH,"clientKeyPath");
        secretProperty.addProperties(SecretProperty.AL_PASS_PHRASE,"passPhrase");

        String value = new SecretServiceAliImpl(secretProperty).getParameter("userName");
        Assertions.assertEquals(value,null);
    }

    @Test
    void testAli5() throws IOException, CacheSecretException {
        SecretProperty secretProperty = new SecretProperty();
        secretProperty.addProperties(SecretProperty.AL_CREDENTIALS_TYPE,"client_key");
        secretProperty.addProperties(SecretProperty.AL_REGION, "cn-beijing");

        secretProperty.addProperties(SecretProperty.AL_CLIENT_KEY_FILE_PATH,"clientKeyPath");
        secretProperty.addProperties(SecretProperty.AL_PASS_PHRASE_FILE_PATH,"passPhrasePath");

        String value = new SecretServiceAliImpl(secretProperty).getParameter("userName");
        Assertions.assertEquals(value,null);
    }

    @Test
    void testHuawei(){
        SecretProperty secretProperty = new SecretProperty();
        secretProperty.addProperties(SecretProperty.HW_ACCESS_KEY,"accessKey");
        secretProperty.addProperties(SecretProperty.HW_SECRET_KEY,"secretAccessKey");
        secretProperty.addProperties(SecretProperty.HW_PROJECT_ID,"projectId");
        secretProperty.addProperties(SecretProperty.HW_ENDPOINT,"csmsEndpoint");
        secretProperty.addProperties(SecretProperty.HW_LATEST_SECRET,"latest");

        String value = new SecretServiceHuaweiImpl(secretProperty).getParameter("userName");
        Assertions.assertEquals(value,null);
    }

    @Test
    void testTencent1(){
        SecretProperty secretProperty = new SecretProperty();
        secretProperty.addProperties(SecretProperty.TX_REGION,"region");
        secretProperty.addProperties(SecretProperty.TX_ACCESS_KEY,"accessKeyId");
        secretProperty.addProperties(SecretProperty.TX_SECRET_KEY,"accessKeySecret");
        secretProperty.addProperties(SecretProperty.TX_LATEST_SECRET,"SSM_Current");

        String value = new SecretServiceTencentImpl(secretProperty).getParameter("userName");
        Assertions.assertEquals(value,null);
    }

    @Test
    void testTencent2(){
        SecretProperty secretProperty = new SecretProperty();
        secretProperty.addProperties(SecretProperty.TX_REGION,"region");
        secretProperty.addProperties(SecretProperty.TX_ROLE_NAME,"roleName");
        secretProperty.addProperties(SecretProperty.TX_LATEST_SECRET,"SSM_Current");

        String value = new SecretServiceTencentImpl(secretProperty).getParameter("userName");
        Assertions.assertEquals(value,null);
    }
}
