# Deloitte Notification component  <br>

# 1.Introduction

Implements encryption and decryption functions of RSA, AES and PGP

# 2.Compilation

## 2.1 How to compile modules?

* How to install to local repository only?

```shell
cd deloitte-framework-encrypt
mvn install -f pom.xml '-Dmaven.test.skip=true'
```

* How to upload to private maven server?

```shell
cd deloitte-framework-encrypt
mvn deploy -f pom.xml '-Dmaven.test.skip=true'
```

# 3.Modules Introduction

## Directory structure ##

```bash
|   pom.xml
\---src
    +---main
    |   \---java
    |       \---com
    |           \---deloitte
    |               \---encrypt
    |                   +---asymmetric
    |                   |       AsymmetricAlgorithmExtension.java   # Enumeration of RSA extension algorithms
    |                   |       RSA2.java                           # RSA extension implementation
    |                   +---config
    |                   |       InitConfiguration.java              # Initial configuration of the encryption service
    |                   \---service
    |                       |   EncryptService.java                 # Encryption and decryption interface, main entrance
    |                       |
    |                       \---impl
    |                               EncryptAESServiceImpl.java      # AES encryption and decryption implementation
    |                               EncryptPGPServiceImpl.java      # PGP encryption and decryption implementation,open PGP implementation
    |                               EncryptRSAServiceImpl.java      # RSA encryption and decryption implementation
    |
    \---test
        \---java
            \---com
                \---deloitte
                    \---encrypt
                            EncryptAESServiceImplTest.java      # Tests for the corresponding service
                            EncryptPGPServiceImplTest.java      # Tests for the corresponding service
                            EncryptRSAServiceImplTest.java      # Tests for the corresponding service

```

## Design Logic
EncryptService defines the encryption and decryption interface; RSA, AES, and PGP each have one implementation

# 4. References
[java-security](https://docs.oracle.com/javase/7/docs/technotes/guides/security/StandardNames.html#Cipher)
[hutool-github](https://github.com/dromara/hutool)
# 5.Q&A
Comming soon!