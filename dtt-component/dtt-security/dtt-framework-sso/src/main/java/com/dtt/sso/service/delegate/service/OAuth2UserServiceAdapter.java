package com.dtt.sso.service.delegate.service;

import com.dtt.sso.service.delegate.RegistrationSupport;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.user.OAuth2User;

public interface OAuth2UserServiceAdapter extends OAuth2UserService<OAuth2UserRequest, OAuth2User>, RegistrationSupport {
}
