package com.dtt.sso.service.config;


import com.dtt.sso.service.delegate.parameter.DelegatingParameterConverter;
import com.dtt.sso.service.delegate.parameter.ParametersCreator;
import com.dtt.sso.service.delegate.resolver.CodeRequestBuilder;
import com.dtt.sso.service.delegate.resolver.DelegatingCodeRequestBuilder;
import com.dtt.sso.service.delegate.service.DelegatingUserService;
import com.dtt.sso.service.delegate.service.OAuth2UserServiceAdapter;
import com.dtt.sso.service.handler.DefaultOAuth2LoginFailureHandler;
import com.dtt.sso.service.handler.DefaultOAuth2LoginSuccessHandler;
import com.dtt.sso.service.handler.OAuth2LoginFailureHandler;
import com.dtt.sso.service.handler.OAuth2LoginSuccessHandler;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.security.oauth2.client.endpoint.DefaultAuthorizationCodeTokenResponseClient;
import org.springframework.security.oauth2.client.endpoint.OAuth2AccessTokenResponseClient;
import org.springframework.security.oauth2.client.endpoint.OAuth2AuthorizationCodeGrantRequest;
import org.springframework.security.oauth2.client.http.OAuth2ErrorResponseErrorHandler;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.client.web.DefaultOAuth2AuthorizationRequestResolver;
import org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestResolver;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.endpoint.DefaultMapOAuth2AccessTokenResponseConverter;
import org.springframework.security.oauth2.core.endpoint.OAuth2AccessTokenResponse;
import org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames;
import org.springframework.security.oauth2.core.http.converter.OAuth2AccessTokenResponseHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Configuration
public class SSODefaultConfiguration {




    @Bean
    public RedisAuthorizationRequestRepository redisAuthorizationRequestRepository(RedisTemplate<Object,Object> redisTemplate){
        return new RedisAuthorizationRequestRepository(redisTemplate);
    }

    @Bean
    public OAuth2AuthorizationRequestResolver authorizationRequestResolver(ClientRegistrationRepository clientRegistrationRepository,
                                                                           DelegatingCodeRequestBuilder delegatingCodeRequestBuilder,
                                                                           SSOProperties ssoProperties){
        DefaultOAuth2AuthorizationRequestResolver requestResolver = new DefaultOAuth2AuthorizationRequestResolver(clientRegistrationRepository,
                        ssoProperties.getAuthorizationCodeRequestBaseUrl());
        requestResolver.setAuthorizationRequestCustomizer(delegatingCodeRequestBuilder);

        return requestResolver;
    }


    @Bean
    public OAuth2AccessTokenResponseClient<OAuth2AuthorizationCodeGrantRequest> oAuth2AccessTokenResponseClient(DelegatingParameterConverter delegatingParameterConverter){
        DefaultAuthorizationCodeTokenResponseClient oAuth2AccessTokenResponseClient = new DefaultAuthorizationCodeTokenResponseClient();
        oAuth2AccessTokenResponseClient.setRestOperations(restTemplate());
        oAuth2AccessTokenResponseClient.setRequestEntityConverter(delegatingParameterConverter);
        return oAuth2AccessTokenResponseClient;
    }

    private RestTemplate restTemplate(){
        OAuth2AccessTokenResponseHttpMessageConverter oAuth2AccessTokenResponseHttpMessageConverter = new OAuth2AccessTokenResponseHttpMessageConverter();
        oAuth2AccessTokenResponseHttpMessageConverter.setSupportedMediaTypes(Arrays.asList(MediaType.TEXT_PLAIN, MediaType.APPLICATION_JSON));
        Converter<Map<String, Object>, OAuth2AccessTokenResponse> setAccessTokenResponseConverter = (paramMap) -> {
            DefaultMapOAuth2AccessTokenResponseConverter defaultMapOAuth2AccessTokenResponseConverter = new DefaultMapOAuth2AccessTokenResponseConverter();
            //微信返回的token信息中没有tokenType，需要手动指定，避免报错
            paramMap.computeIfAbsent(OAuth2ParameterNames.TOKEN_TYPE, k -> OAuth2AccessToken.TokenType.BEARER.getValue());
            return defaultMapOAuth2AccessTokenResponseConverter.convert(paramMap);
        };
        // 设置这个转换器
        oAuth2AccessTokenResponseHttpMessageConverter.setAccessTokenResponseConverter(setAccessTokenResponseConverter);
        RestTemplate restTemplate = new RestTemplate(Arrays.asList(new FormHttpMessageConverter(), oAuth2AccessTokenResponseHttpMessageConverter));
        restTemplate.setErrorHandler(new OAuth2ErrorResponseErrorHandler());
        return restTemplate;
    }


    @Configuration
    static class DelegatingConfiguration{
        @Bean
        public DelegatingParameterConverter delegatingParameterConverter(List<ParametersCreator> creators){
            DelegatingParameterConverter delegatingParameterConverter = new DelegatingParameterConverter();
            delegatingParameterConverter.addCreators(creators);
            return delegatingParameterConverter;
        }


        @Bean
        public DelegatingCodeRequestBuilder delegatingCodeRequestBuilder(List<CodeRequestBuilder> builders){
            DelegatingCodeRequestBuilder delegatingCodeRequestBuilder = new DelegatingCodeRequestBuilder();
            delegatingCodeRequestBuilder.addBuilders(builders);
            return delegatingCodeRequestBuilder;
        }


        @Bean
        public DelegatingUserService delegatingUserService(List<OAuth2UserServiceAdapter> adapters){
            DelegatingUserService delegatingUserService = new DelegatingUserService();
            delegatingUserService.addAdapters(adapters);
            return delegatingUserService;
        }
    }


    @Configuration
    static class HandlerConfiguration{
        @Bean
        @ConditionalOnMissingBean(OAuth2LoginFailureHandler.class)
        public OAuth2LoginFailureHandler defaultOAuth2LoginFailureHandler(){
            return new DefaultOAuth2LoginFailureHandler();
        }

        @Bean
        @ConditionalOnMissingBean(OAuth2LoginSuccessHandler.class)
        public OAuth2LoginSuccessHandler defaultOAuth2LoginSuccessHandler(){
            return new DefaultOAuth2LoginSuccessHandler();
        }

    }





}
