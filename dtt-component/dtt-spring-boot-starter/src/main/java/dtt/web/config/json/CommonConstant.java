/*
 * Copyright 2019-2029 geekidea(https://github.com/geekidea)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package dtt.web.config.json;

import java.util.Date;

/**
 * 常量
 */
public final class CommonConstant {


    /**
     * 默认页码为1
     */
    public static final String COMMON_DATE_FORMAT = "yyyy/MM/dd HH:mm:ss";

    /**
     * 默认页码为1
     */
    public static final Long DEFAULT_PAGE_INDEX = 1L;

    /**
     * 默认页大小为10
     */
    public static final Long DEFAULT_PAGE_SIZE = 10L;

    /**
     * 最大页大小为100
     */
    public static final Long MAX_PAGE_SIZE = 100L;


    public static final String IS_DELETED = "is_deleted";

    public static final String DELETE_TIME = "delete_time";

    /**
     * 请求流水，GUID 36为小写
     */
    public static final String TRANSACTION_ID = "X-Transaction-Id";

    public static final String OWNER_USER_ID = "ownerUserId";

    public static final String OWNER_USER_NAME = "ownerUserName";
    
    public static final String OWNER_USER_STAFF_NAME = "ownerUserStaffName";
    
    public static final String OWNER_ORG_ID = "ownerOrgId";

    public static final String USER_INFO = "ownerUserInfo";

    public static final String FEIGN_REQUEST_HEADER = "FEIGN-REQUEST-HEADER";


    public static final String QRCODE_APP_NAME = "masterdata.qrcode.appName";

    public static final String QRCODE_TYPE = "masterdata.qrcode.qrcode_type";

    public static final String COUPON_STATUS = "coupon.coupon_status";

    public static final String COUPON_DEFINITION_STATUS = "coupon.coupon_definition_status";

    public static final String COUPON_TYPE = "coupon.coupon_type";

    public static final String S3_ADDRESS_GROUP = "masterdata.baseurl";

    public static final String CAMPAIGN_STATUS = "campaign.status";

    public static final Date QR_CODE_END_TIME = new Date(1988035200000L);
}
