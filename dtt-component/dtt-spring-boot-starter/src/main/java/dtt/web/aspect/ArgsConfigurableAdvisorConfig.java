package dtt.web.aspect;

import org.springframework.aop.aspectj.AspectJExpressionPointcutAdvisor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
// @EnableElasticsearchRepositories
public class ArgsConfigurableAdvisorConfig {

    @Value("${pointcut.argspointcut:execution(* nopointcut..*(..))}")
    private String pointcut;

    @Autowired
    private ArgsLogInterceptor argsLogInterceptor;

    @Bean
    public AspectJExpressionPointcutAdvisor configurabledvisor(){ //(ArgsLogInterceptor interceptor) {
        AspectJExpressionPointcutAdvisor advisor = new AspectJExpressionPointcutAdvisor();
        //if(pointcut!=null && !pointcut.equals(""))
        //{
            advisor.setExpression(pointcut);
            advisor.setAdvice(argsLogInterceptor);
            //advisor.setAdvice(new ArgsLogInterceptor ());
        //}


        return advisor;
    }


    public static final String EXECUTOR_NAME = "argsLogMqAsyncSend";

    @Bean(EXECUTOR_NAME)
    public Executor mqAsyncSendExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(1000);
        executor.setKeepAliveSeconds(120);
        executor.setThreadNamePrefix("argsLogMqAsyncSend-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}
