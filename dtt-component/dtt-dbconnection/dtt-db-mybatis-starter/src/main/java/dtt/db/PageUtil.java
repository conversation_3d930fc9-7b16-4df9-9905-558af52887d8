package dtt.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import dtt.db.exception.SensitiveExceptionUtil;
import dtt.db.pagination.BaseOrderItem;
import dtt.db.pagination.BasePageOrderParam;
import dtt.db.pagination.BasePageParam;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
public class PageUtil {


    /**
     * 获取mybatisplus分页对象
     *
     * @param baseBasePageParam 分页参数
     * @return
     */
    public static Page getPage(BasePageParam baseBasePageParam) {
        return getPage(baseBasePageParam, null);
    }

    /**
     * 获取mybatisplus分页对象
     *
     * @param baseBasePageParam 分页参数
     * @param defaultOrder  默认排序列
     * @return
     */
    public static Page getPage(BasePageParam baseBasePageParam, OrderItem defaultOrder) {
        Page page = new Page(baseBasePageParam.getPageIndex(), baseBasePageParam.getPageSize());
        setBasePageOrderParam(baseBasePageParam, defaultOrder, page);
        return page;
    }

    /**
     *
     *
     */


    /**
     * 如果是BasePageParam是OrderBasePageParam，并且不为空，则使用前端排序
     * 否则使用默认排序
     *
     * @param baseBasePageParam 分页参数
     * @param defaultOrder  默认排序
     * @param page          分页对象
     */
    public static void setBasePageOrderParam(BasePageParam baseBasePageParam, OrderItem defaultOrder, Page page) {
        if (baseBasePageParam instanceof BasePageOrderParam) {
            BasePageOrderParam baseBasePageOrderParam = (BasePageOrderParam) baseBasePageParam;

            List<BaseOrderItem> pageSorts = baseBasePageOrderParam.getPageSorts();
            List<OrderItem> orderItems = transOrder(pageSorts);
            if (CollUtil.isEmpty(pageSorts)) {
                setDefaultOrder(page, defaultOrder);
            } else {
                page.setOrders(orderItems);
            }
        } else {
            setDefaultOrder(page, defaultOrder);
        }
    }
    private static List<OrderItem> transOrder(List<BaseOrderItem> baseOrderItems){
        List<OrderItem> orderItems = new ArrayList<>();
        if(CollUtil.isEmpty(baseOrderItems)){
            return orderItems;
        }
        for (BaseOrderItem baseOrderItem : baseOrderItems) {
            String column = baseOrderItem.getColumn();
            orderItems.add(baseOrderItem.isAsc() ? OrderItem.asc(column) : OrderItem.desc(column));
        }

        return orderItems;
    }

    public static void setDefaultOrder(Page page, OrderItem defaultOrder) {
        if (defaultOrder != null) {
            page.setOrders(Arrays.asList(defaultOrder));
        }
    }


    /**
     * 优化分页limit查询
     * 当不是第一页时，继续使用limit 0,10,表示是相同的条件查询
     * 不同条件查询时，返回的都是第一页
     * 当前页码设置为0
     * limit 0,10
     * 当不是第一页，且lastRowId不为空
     * current=0
     * 4,5,6 lastRowId：6
     * where id > 6 order by id limit 0,3
     *
     * @param baseBasePageParam
     * @param optimizeLimit
     * @return
     */
    public static Page getPage(BasePageParam baseBasePageParam, boolean optimizeLimit) {
        return getPage(baseBasePageParam, true, null);
    }

    public static Page getPage(BasePageParam baseBasePageParam, boolean optimizeLimit, String optimizeLimitColumn) {
        if (CharSequenceUtil.isBlank(optimizeLimitColumn)) {
            optimizeLimitColumn = "id";
        }
        if (optimizeLimit) {
            Page page = new Page(0, baseBasePageParam.getPageSize());
            page.setOrders(Arrays.asList(OrderItem.desc(optimizeLimitColumn)));
            return page;
        } else {
            return getPage(baseBasePageParam, null);
        }
    }

    public static Long getLastRowLimitValue(IPage page) {
        return getLastRowLimitValue(page, null);
    }

    /**
     * 获取返回结果集中的最后一行的ID
     *
     * @param page
     * @param optimizeLimitColumn
     * @return
     */
    public static Long getLastRowLimitValue(IPage page, String optimizeLimitColumn) {
        if (CharSequenceUtil.isBlank(optimizeLimitColumn)) {
            optimizeLimitColumn = "id";
        }
        List list = page.getRecords();
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        Object object = list.get(list.size() - 1);
        if (object == null) {
            return null;
        }
        try {
            if (object instanceof Map) {
                Map map = (Map) object;
                return (Long) map.get(optimizeLimitColumn);
            }
            Class cls = object.getClass();
            Field field = cls.getDeclaredField(optimizeLimitColumn);
            field.setAccessible(true);
            return (Long) field.get(object);
        } catch (Exception e) {
            log.warn("lastRowId is null", SensitiveExceptionUtil.handleException(e));
        }
        return null;
    }

}
