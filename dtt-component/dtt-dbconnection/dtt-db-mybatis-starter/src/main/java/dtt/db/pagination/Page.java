package dtt.db.pagination;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 数据页
 *
 * <AUTHOR> Xiaohua
 * @date 24/03/2021 19:56
 */
@Data
@ApiModel(value = "分页", description = "分页对象")
public class Page<T> implements Serializable {

    private static final long serialVersionUID = 324637585842559713L;

    public static final int DEFAULT_PAGE_SIZE = 1;

    public static final int MAX_PAGE_SIZE = 100;

    /**
     * 内部服务调用
     */
    private boolean internal;
    
    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页", dataType = "int")
    private int pageIndex;
    /**
     * 页数量
     */
    @ApiModelProperty(value = "总页数", dataType = "int")
    private int totalPage;
    /**
     * 页大小
     */
    @ApiModelProperty(value = "页大小", dataType = "int")
    private int pageSize;
    /**
     * 总数据量
     */
    @ApiModelProperty(value = "总数据量", dataType = "int")
    private int total;
    /**
     * 数据列表
     */
    @ApiModelProperty(value = "当前页数据列表", dataType = "array")
    private List<T> data;

    public void setPageSize(int pageSize) throws Exception {
        if(pageSize <=0 ){
            pageSize = DEFAULT_PAGE_SIZE;
        }
        if(!internal && pageSize > MAX_PAGE_SIZE){
            throw new Exception("pageSize max value:"+ MAX_PAGE_SIZE);
        }
        this.pageSize = pageSize;
    }
}
