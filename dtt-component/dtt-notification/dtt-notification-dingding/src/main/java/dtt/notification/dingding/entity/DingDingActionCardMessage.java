package dtt.notification.dingding.entity;

import lombok.Data;

/**
 * DingDingActionCardMessage
 *
 * <AUTHOR> Alex Jiexiang
 * @date 2023-07-20
 */
@Data
public class DingDingActionCardMessage {

    /**
     * 首屏会话透出的展示内容
     */
    private String title;

    /**
     * markdown格式的消息
     */
    private String text;


    /**
     * 单个按钮的标题
     * 设置此项和singleURL后，btns无效
     */
    private String singleTitle;

    /**
     * 点击消息跳转的URL，打开方式如下：
     * 移动端  在钉钉客户端内打开
     * PC端   默认侧边栏打开
     * 希望在外部浏览器打开。
     * 企业内部应用参考消息链接说明。
     * 第三方企业应用参考消息链接说明。
     */
    private String singleURL;

    /**
     * 0：按钮竖直排列
     * 1：按钮横向排列
     */
    private String btnOrientation;

}
