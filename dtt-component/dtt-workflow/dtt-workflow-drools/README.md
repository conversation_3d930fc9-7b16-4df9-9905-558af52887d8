# Deloitte drools component  <br>

# 1.Introduction

Drools basic function encapsulate implementation.

# 2.Compilation

## 2.1 How to compile modules?

* How to install to local repository only?

```shell
cd dtt-workflow-drools
mvn install -f pom.xml '-Dmaven.test.skip=true'
```

* How to upload to private maven server?

```shell
cd dtt-workflow-drools 
mvn deploy -f pom.xml '-Dmaven.test.skip=true'
```

# 3.Modules Introduction

## Directory structure ##
```yaml

dtt-workflow-drools                                         # drools-component
├─src
│  └─main
│      ├─java
│      │  └─dtt
│      │      └─workflow
│      │          └─drools
│      │              ├─config
│      │              │      DroolsAutoConfig.java          # drools auto configuration 
│      │              │      DroolsConfig.java              # drools basic configuration 
│      │              │      DroolsContainerConfig.java     # drools container configuration 
│      │              │      DroolsDatabaseConfig.java      # drools database configuration 
│      │              │      DroolsFileConfig.java          # drools file configuration 
│      │              │
│      │              └─loader
│      │                      ClassPathLoader.java          # drools class path rule loader implement
│      │                      FileRuleLoader.java           # drools file rule loader implement 
│      │                      RuleLoader.java               # drools rule  loader interface  
│      │
│      └─resources
│          │  application.properties                         # spring configuration
│          │
│          └─META-INF
│                  spring.factories                          # spring auto assemble configuration
```

## Design Logic

Encapsulate drools basic function.

1. Use [spring.factories](src%2Fmain%2Fresources%2FMETA-INF%2Fspring.factories) auto assemble spring container.
2. Three drl rules loading modes are provided

* classpath
* file
* database (coming soon!)

3. Dynamically implement drl rules additions, deletions and changes (coming soon!)



# 4. References

## Enterprise WeChat

[Drools official document](https://docs.drools.org/8.41.0.Final/drools-docs/drools/introduction/index.html)

# 5.Q&A


* How to configure the drools ?

1. Select your drl loading mode


* classpath(default config)
```yaml
drools:
    config:
         loadType: classpath
```

* file
If you're loading classpath file, you need add classpath: prefix  to the path
If you're loading absolute file, you need add file: prefix  to the path
```yaml
drools:
  config:
    loadType: file
    file:
      path:    # here your loading path 
```
* database

```yaml
drools:
  config:
    loadType: database
    database:
      database: xxx    # table scheme
      tableName: xxx   # table name
      fieldName: xxx   # filed name
      condition: xxx   # condition
```
* How to use drools ?
1. Inject KieContainer
```
  @Autowired
    private KieContainer kieContainer;
```
2. Create session
If you use classpath mode loading,you should assign session name
```
 KieSession session = kieContainer.newKieSession();
```
3. Insert object
```
  session.insert(order);
```
4. Execute rules
```
session.fireAllRules();
```



