<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>dtt.asset</groupId>
        <artifactId>trinabuy-workflow</artifactId>
        <version>0.0.2.RELEASE</version>
    </parent>

    <artifactId>trinabuy-workflow-drools</artifactId>
    <version>0.0.2.RELEASE</version>

    <dependencies>
        <!-- Drools dependency -->
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-compiler</artifactId>
        </dependency>
        <dependency>
            <groupId>org.kie</groupId>
            <artifactId>kie-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.kie</groupId>
            <artifactId>kie-internal</artifactId>

        </dependency>

        <!--<dependency>-->
            <!--<groupId>org.drools</groupId>-->
            <!--<artifactId>drools-xml-support</artifactId>-->

        <!--</dependency>-->

        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-mvel</artifactId>

        </dependency>


    </dependencies>

    <properties>
        <!--<maven.compiler.source>11</maven.compiler.source>-->
        <!--<maven.compiler.target>11</maven.compiler.target>-->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
</project>