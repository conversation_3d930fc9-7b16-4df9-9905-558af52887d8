package dtt.workflow.drools.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 邮件配置
 *
 * <AUTHOR> <PERSON>
 * @date 2023-07-17
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "drools.config.database")
public class DroolsDatabaseConfig {
    /**
     * drl存储 库名
     */
    private String database;

    /**
     * drl存储 表名
     */
    private String tableName;

    /**
     * drl存储 字段名
     */
    private String fieldName;

    /**
     * drl存储 条件
     */
    private String condition;


}