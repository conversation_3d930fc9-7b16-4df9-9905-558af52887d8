package dtt.workflow.drools.service.impl;

import dtt.workflow.drools.service.DroolsService;
import org.kie.api.KieBase;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.kie.api.runtime.rule.AgendaFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * drools实现类
 *
 * <AUTHOR> <PERSON>
 * @date 2023-07-28
 */
@Service
public class DroolsServiceImpl implements DroolsService {
    @Autowired
    KieContainer kieContainer;

    @Override
    public KieContainer getContainer() {
        return kieContainer;
    }

    @Override
    public KieBase getKieBase() {
        return kieContainer.getKieBase();
    }

    @Override
    public KieBase getKieBase(String baseName) {
        return kieContainer.getKieBase(baseName);
    }

    @Override
    public KieSession newSession(String sessionName) {
        return kieContainer.newKieSession(sessionName);
    }

    @Override
    public KieSession newSession() {
        return kieContainer.newKieSession();
    }

    @Override
    public int executeRules(String sessionName, Object fact, AgendaFilter filter) {
        KieSession kieSession = kieContainer.newKieSession(sessionName);
        kieSession.insert(fact);
        return kieSession.fireAllRules(filter);
    }

    @Override
    public int executeRules(String sessionName, Object fact) {
        KieSession kieSession = kieContainer.newKieSession(sessionName);
        kieSession.insert(fact);
        return kieSession.fireAllRules();
    }

    @Override
    public int executeRules(Object fact) {
        KieSession kieSession = kieContainer.newKieSession();
        kieSession.insert(fact);
        return kieSession.fireAllRules();
    }
}
