package dtt.support.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName DataStatusEnum
 * @Description 审批状态枚举值
 * <AUTHOR>
 * @Date 2019/11/26
 **/
@Getter
@AllArgsConstructor
public enum PurchaseReturnStatusEnum {
    EXPIRED("ZT02", "审批中"),
    PASSED("ZT07", "已审核通过"),
    CONFIRMED("ZT03", "已确认"),
    DISABLED("ZT04", "已禁用"),

    REJECTED("ZT05", "已驳回"),
    REVERSED("ZT08", "已冲销"),
    DRAFT("ZT06", "已保存");

    private String code;

    private String name;
}
