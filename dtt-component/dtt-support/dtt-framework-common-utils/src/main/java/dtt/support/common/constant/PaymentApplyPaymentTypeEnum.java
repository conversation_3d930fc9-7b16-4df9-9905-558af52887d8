package dtt.support.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 部门：
 * 功能：付款申请付款类别
 * 描述：
 * 作成者：李磊
 * 作成时间：2020/1/19
 **/
@Getter
@AllArgsConstructor
public enum PaymentApplyPaymentTypeEnum {

    PT01("PT01","货款"),
    PT02("PT02","非货款"),
    PT03("PT03","客户退款-有合同"),
    PT04("PT04","客户退款-无合同"),
    PT05("PT05","保证金"),
    PT06("PT06","虚拟付款"),
    PT07("PT07","资金调拨-向下调拨"),
    PT08("PT08","资金调拨-同户名调拨"),
    PT10("PT10","退保证金"),
    PT11("PT11","返利润"),
    PT12("PT12","期货保证金"),
    PT14("PT14","资金调拨-向上归集"),;

    private String code;

    private String name;
}
