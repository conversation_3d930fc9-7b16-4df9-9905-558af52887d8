package dtt.support.common.dto;

import lombok.*;

/**
 * @Description:
 * @Author: 程杰(J)
 * @Date: 2023-04-20
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DataPermDTO {

    /***
     * 权限ID
     */
    private Integer permissionId;

    /***
     * 权限类型
     */
    private String permissionType;

    /***
     * 角色ID
     */
    private String roleId;
}
