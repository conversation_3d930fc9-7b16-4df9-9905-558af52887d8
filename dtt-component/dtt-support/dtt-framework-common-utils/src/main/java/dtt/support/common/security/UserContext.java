package dtt.support.common.security;

/**
 * <p>用户上下文容器</p>
 * <p>创建日期：2020-06-15</p>
 * 
 */
public class UserContext {

    /**
     * 保存 tenantCode
     */
    private static ThreadLocal<String> tenantCode = new ThreadLocal<>();
    /**
     * 保存companyCode
     */
    private static ThreadLocal<String> companyCode = new ThreadLocal<>();
    
    /**
     * 保存companyName
     */
    private static ThreadLocal<String> companyName = new ThreadLocal<>();
    
    /**
     * departmentCode
     */
    private static ThreadLocal<String> departmentCode = new ThreadLocal<>();
    /**
     * departmentName
     */
    private static ThreadLocal<String> departmentName = new ThreadLocal<>();

    /**
     * tenantCode
     *
     * @return 租户
     */
    public static String getTenantCode() {
        String pn = tenantCode.get();
        if (pn == null) {
            return "";
        }
        return pn;
    }

    public static void setTenantCode(String tenantCodeValue) {
    	tenantCode.set(tenantCodeValue);
    }

    public static void removeTenantCode() {
    	tenantCode.remove();
    }
    
    /**
     * companyCode
     *
     * @return 公司代码
     */
    public static String getCompanyCode() {
        String pn = companyCode.get();
        if (pn == null) {
            return "";
        }
        return pn;
    }

    public static void setCompanyCode(String companyCodeValue) {
    	companyCode.set(companyCodeValue);
    }

    public static void removeCompanyCode() {
    	companyCode.remove();
    }
    
    /**
     * companyName
     *
     * @return 租户
     */
    public static String getCompanyName() {
        String pn = companyName.get();
        if (pn == null) {
            return "";
        }
        return pn;
    }

    public static void setCompanyName(String companyNameValue) {
    	companyName.set(companyNameValue);
    }

    public static void removeCompanyName() {
    	companyName.remove();
    }
    
    /**
     * tenantCode
     *
     * @return 租户
     */
    public static String getDepartmentCode() {
        String pn = departmentCode.get();
        if (pn == null) {
            return "";
        }
        return pn;
    }

    public static void setDepartmentCode(String departmentCodeValue) {
    	departmentCode.set(departmentCodeValue);
    }

    public static void removeDepartmentCode() {
    	departmentCode.remove();
    }
    
    /**
     * tenantCode
     *
     * @return 租户
     */
    public static String getDepartmentName() {
        String pn = departmentName.get();
        if (pn == null) {
            return "";
        }
        return pn;
    }

    public static void setDepartmentName(String departmentNameValue) {
    	departmentName.set(departmentNameValue);
    }

    public static void removeDepartmentName() {
    	departmentName.remove();
    }    

    public static void clear() {
    	tenantCode.remove();
    	companyCode.remove();
    	companyName.remove();
    	departmentCode.remove();
    	departmentName.remove();
    }
}

