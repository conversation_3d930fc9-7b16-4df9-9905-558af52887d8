package dtt.support.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 信用证承兑状态
 * <AUTHOR>
 * @date 2020年7月10日14:53:43
 */
@Getter
@AllArgsConstructor
public enum CreditLetterAccStatusEnum {

    SAVED("001", "已保存"),
    
    EXPIRED("002", "审批中"),
    
    REJECTED("003", "已驳回"),
    
    ACCEPTED("004", "已承兑"),
    
    PAID("005", "已付款");
    
    private String code;
    
    private String name;
}
