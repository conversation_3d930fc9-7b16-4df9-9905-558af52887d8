package dtt.support.common.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import dtt.support.common.exception.ArgNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;

import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
public class JSONUtil {

    /**
     * 遍历Json 返回第一个传入key的value
     * @param jsonObject 遍历对象
     * @param key key
     * @return value
     * @throws ArgNotFoundException 找不到抛异常
     */
    public static Object getFirst(JSONObject jsonObject, String key) throws ArgNotFoundException{
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            if(entry.getKey().equals(key)){
                log.info("get value:{} ,with key:{}",entry.getValue(),key);
                return entry.getValue();
            }
            if (null != entry.getValue() && entry.getValue() instanceof LinkedHashMap) {
                JSONObject value = JSON.parseObject(JSON.toJSONString(entry.getValue()));
                if (!value.isEmpty()) {
                    try {
                        return getFirst(value, key);
                    }catch (ArgNotFoundException e){
                        log.warn("key not found in this json object,Continue searching rest");
                    }
                }
            } else if (null != entry.getValue() && entry.getValue() instanceof JSONArray) {
                JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(entry.getValue()));
                if (!jsonArray.isEmpty()) {
                    try {
                        return getFirst(jsonArray, key);
                    }catch (ArgNotFoundException e){
                        log.warn("key not found in this json array ,Continue searching rest");
                    }
                }
            }
        }
        log.warn("business key not found,key:{},arg:{}",key,jsonObject);
        throw new ArgNotFoundException("key:"+key+" not found, with json:"+jsonObject.toJSONString());
    }

    /**
     * 遍历Json数组 返回第一个传入key的value
     * @param jsonArray 遍历对象
     * @param key key
     * @return value
     * @throws ArgNotFoundException 找不到抛异常
     */
    public static Object getFirst(@NonNull JSONArray jsonArray, String key) throws ArgNotFoundException {
        for (Object data : jsonArray) {
            if (data instanceof LinkedHashMap) {
                JSONObject value = JSON.parseObject(JSON.toJSONString(data));
                if (!value.isEmpty()) {
                    try {
                        return getFirst(value, key);
                    }catch (ArgNotFoundException e){
                        log.warn("key not found in this Json,Continue searching rest array");
                    }
                }
            } else if (data instanceof JSONArray) {
                JSONArray newJsonArray = JSONArray.parseArray(JSON.toJSONString(data));
                if (!newJsonArray.isEmpty()) {
                    return getFirst(newJsonArray, key);
                }
            }
        }
        log.warn("business key not found,key:{},arg:{}",key,jsonArray);
        throw new ArgNotFoundException("key:"+key+" not found, with jsonArray:"+jsonArray.toJSONString());
    }
}
