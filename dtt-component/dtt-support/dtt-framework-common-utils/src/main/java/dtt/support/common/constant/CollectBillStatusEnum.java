package dtt.support.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 应收票据状态
 * @Author：qiulx
 * @Date: 2020/3/9
 * @return
 **/
@Getter
@AllArgsConstructor
public enum CollectBillStatusEnum {

    REGISTERED("01", "已登记"),

    CLAIMED("02","已认领"),

    DISCOUNTED("03","已贴现"),

    ENDORSE("04","已背书"),

    COLLECTION("05","已托收"),

    PLEDGE("06","已质押");

    private String code;

    private String name;
}
