package dtt.support.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CreditLetterImportEnum {

    CL_TYPE("cl-clType", "信用证类别"),

    IMPORT_EXPORT_NATURE("cl-importExportNature", "经营性质"),

    CL_ATTR("cl-clAttr", "信用证属性"),

    CL_NATURE("cl-clNature", "信用证性质"),

    BUSINESS_NATURE("cl-businessNature", "业务性质"),

    ISSUE_TYPE("cl-issueType", "开证类型");

    private String code;

    private String name;
}
