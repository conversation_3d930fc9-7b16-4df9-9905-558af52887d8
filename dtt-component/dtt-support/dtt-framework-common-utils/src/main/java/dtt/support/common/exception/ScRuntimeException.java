package dtt.support.common.exception;

/**
 * <p>框架基类异常</p>
 * <p>创建日期：2018-02-28</p>
 *
 * <AUTHOR> <EMAIL>
 */
public class ScRuntimeException extends RuntimeException {

    public ScRuntimeException() {
    }

    public ScRuntimeException(String message) {
        super(message);
    }

    public ScRuntimeException(String message, Throwable cause) {
        super(message, cause);
    }

    public ScRuntimeException(Throwable cause) {
        super(cause);
    }

    public ScRuntimeException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
