package dtt.support.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName ExternalPriceTypeEnum
 * @Description 外部价格类型（主数据权限）
 * <AUTHOR>
 * @Date 2019/12/19
 **/
@Getter
@AllArgsConstructor
public enum ExternalPriceTypeEnum {

    CCFRJPRICE("WBJG01","CCF日均价格"),

    QHJSPRICE("WBJG02","期货结算价格"),

    XHWPPRICE("WBJG03","现货尾盘价格"),

    QHSPPRICE("WBJG04","期货收盘价格");

    private String code;

    private String name;
}
