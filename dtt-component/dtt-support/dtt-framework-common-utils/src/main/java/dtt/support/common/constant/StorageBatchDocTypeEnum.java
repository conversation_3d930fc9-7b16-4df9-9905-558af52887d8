package dtt.support.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName DataStatusEnum
 * @Description 业务批次单据类型
 * <AUTHOR>
 * @Date 2020/9/9
 **/
@Getter
@AllArgsConstructor
public enum StorageBatchDocTypeEnum {
    // 业务入库
    DISTRIBUTION_BUSINESS_INBOUND("t_distribution_business_inbound", "业务入库"),
    // 销售退货
    ORDER_SALES_RETURN("t_order_sales_return", "销售退货"),
    // 部门间交易
    STORAGE_FUNCTION_TRADE("t_storage_function_trade", "部门间交易"),
    // 库存调拨
    STORAGE_ALLOT("t_storage_allot", "库存调拨"),
 // 委托加工入库
    MC_INBOUND_IN("t_mc_inbound", "委托加工入库"),

    //生产加工入库,给定一个特殊值,目前看来对于其他的业务没有影响。主要用作库存实时查询中显示单据类型“生产加工入库”
    SC_INBOUND_IN("productionInbound","生产加工入库");

    private String code;

    private String name;
}
