package dtt.support.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 会计引擎模块
 * @Author: chxiang
 * @Date: 16/03/2022 16:36
 **/
@Getter
@AllArgsConstructor
public enum CreditEngineModel {

    CREDIT_ENGINE_PO("Credit_Engine_PO", "采购管理"),
    CREDIT_ENGINE_OM("Credit_Engine_OM", "销售管理"),
    CREDIT_ENGINE_ZKD("Credit_Engine_ZKD", "转款单"),
    CREDIT_ENGINE_YFM("Credit_Engine_YFM", "应付管理"),
    CREDIT_ENGINE_YSM("Credit_Engine_YSM", "应收管理"),
    CREDIT_ENGINE_SKM("Credit_Engine_SKM", "收款管理"),
    CREDIT_ENGINE_FKM("Credit_Engine_FKM", "付款管理"),
    CREDIT_ENGINE_YSPJM("Credit_Engine_YSPJM", "应收票据管理"),
    CREDIT_ENGINE_YFPJM("Credit_Engine_YFPJM", "应付票据管理"),
    CREDIT_ENGINE_XYZM("Credit_Engine_XYZM", "信用证管理"),
    CREDIT_ENGINE_XSFHM("Credit_Engine_XSFHM", "销售发货管理"),
    CREDIT_ENGINE_CGRKM("Credit_Engine_CGRKM", "采购入库管理"),
    CREDIT_ENGINE_WLYSM("Credit_Engine_WLYSM", "物流运输管理"),
    CREDIT_ENGINE_WLHTM("Credit_Engine_WLHTM", "物流合同管理");

    private String code;

    private String name;

    public static String getName(String code) {
        for (CreditEngineModel processTypeEnum : CreditEngineModel.values()) {
            if (processTypeEnum.getCode().equalsIgnoreCase(code)) {
                return processTypeEnum.getName();
            }
        }
        return null;
    }

}
