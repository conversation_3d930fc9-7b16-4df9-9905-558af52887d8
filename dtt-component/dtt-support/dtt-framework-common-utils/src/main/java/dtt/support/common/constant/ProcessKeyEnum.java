package dtt.support.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 审批流程定义key
 * @Author: chxiang
 * @Date: 18/02/2022 15:25
 **/
@Getter
@AllArgsConstructor
public enum ProcessKeyEnum {

    PROCESS_FUND_PAYMENT_APPLY("Process_FundPaymentApply", "付款申请审批流程"),
    //销售合同
    PROCESS_SALES_CONTRACT("Process_SalesContract", "销售合同审批流程"),
    //采购合同
    PROCESS_CONTRACT("Process_Contract", "采购合同审批流程");


    private String code;

    private String name;

    public static String getName(String code) {
        for (ProcessKeyEnum processTypeEnum : ProcessKeyEnum.values()) {
            if (processTypeEnum.getCode().equalsIgnoreCase(code)) {
                return processTypeEnum.getName();
            }
        }
        return null;
    }

}
