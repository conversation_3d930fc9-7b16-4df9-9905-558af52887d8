package dtt.support.common.util;

import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
/**
 * @ClassName: X509TrustUtil
 * @Description:
 * @Author: shengtt
 * @Date: 2020/4/20
 */
public class X509TrustUtil implements X509TrustManager {

    private X509TrustManager standardTrustManager = null;

    public X509TrustUtil()
            throws NoSuchAlgorithmException, KeyStoreException {
        super();
        TrustManagerFactory factory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        TrustManager[] trustmanagers = factory.getTrustManagers();
        if (trustmanagers.length == 0) {
            throw new NoSuchAlgorithmException("no trust manager found");
        }
        this.standardTrustManager = (X509TrustManager) trustmanagers[0];
    }

    @Override
    public void checkClientTrusted(X509Certificate[] arg0, String arg1)
            throws CertificateException
    {
        // TODO Auto-generated method stub
        standardTrustManager.checkClientTrusted(arg0, arg1);
    }

    @Override
    public void checkServerTrusted(X509Certificate[] arg0, String arg1)
            throws CertificateException
    {
        // TODO Auto-generated method stub
        if ((arg0 != null) && (arg0.length == 1)) {
            arg0[0].checkValidity();
        } else {
            standardTrustManager.checkServerTrusted(arg0, arg1);
        }
    }

    @Override
    public X509Certificate[] getAcceptedIssuers() {
        // TODO Auto-generated method stub
        return this.standardTrustManager.getAcceptedIssuers();
    }

}