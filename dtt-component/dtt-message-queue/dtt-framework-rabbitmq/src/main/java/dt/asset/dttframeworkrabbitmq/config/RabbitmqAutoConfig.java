package dt.asset.dttframeworkrabbitmq.config;

import com.rabbitmq.client.*;
import dt.asset.dttframeworkrabbitmq.service.RabbitSerivce;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.io.IOException;
import java.util.concurrent.TimeoutException;

@Configuration
@EnableConfigurationProperties(RabbitmqProp.class)
@Slf4j
public class RabbitmqAutoConfig {
	final
	RabbitmqProp rabbitmqProp;

	public ConnectionFactory factory;
	public Channel channel;

	public RabbitmqAutoConfig(RabbitmqProp rabbitmqProp) {
		this.rabbitmqProp = rabbitmqProp;
	}

	@Bean
	public RabbitSerivce getRabbitSerivce(Channel channel) {
		return new RabbitSerivce(channel);
	}

	@Bean
	@DependsOn("dttGetChannel")
	public void ConsumerAutoConfig() {
		com.rabbitmq.client.Consumer consumer = new DefaultConsumer(channel) {
			@Override
			public void handleDelivery(String consumerTag, Envelope envelope,
			                           AMQP.BasicProperties properties, byte[] body)
				throws IOException {
				String message = new String(body, "UTF-8");
				log.info("MyCustomer Received '" + message + "'");
			}
		};
		try {
			channel.queueDeclare("QueueTest", true, false, false, null);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		//自动回复队列应答 -- RabbitMQ中的消息确认机制, false会导致能接收消息， 但会重复消费，需要手动ack
		try {
			channel.basicConsume("QueueTest", true, consumer);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	@Bean(name = "dttGetChannel")
	public Channel getChannel() throws IOException, TimeoutException {
		factory = new ConnectionFactory();
		// 设置 RabbitMQ 的主机名
		factory.setHost(rabbitmqProp.getHost());
		factory.setUsername(rabbitmqProp.getUsername());
		factory.setPassword(rabbitmqProp.getPassword());
		factory.setPort(Integer.parseInt(rabbitmqProp.getPort()));
		channel = factory.newConnection().createChannel();
		return channel;
	}
}
