package dtt.cache.redisson.configuration;

import dtt.cache.redisson.entity.RedissonProperties;
import dtt.cache.redisson.invoke.StandAlone;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Configuration
@RequiredArgsConstructor
public class RedissonConfiguration {

  @Resource
  private final RedissonProperties redissonProperties;

  @Bean
  public RedissonClient getRedisson() {
    return StandAlone.getInstance(redissonProperties);
  }

}
