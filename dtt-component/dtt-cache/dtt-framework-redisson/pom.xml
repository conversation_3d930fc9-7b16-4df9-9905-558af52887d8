<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>dtt.asset</groupId>
    <artifactId>dtt-base</artifactId>
    <version>0.0.2.RELEASE</version>
    <relativePath>../../dtt-base</relativePath>
  </parent>

  <packaging>jar</packaging>
  <groupId>dtt.asset</groupId>
  <artifactId>dtt-framework-redisson</artifactId>

  <properties>
    <!--<maven.compiler.source>11</maven.compiler.source>-->
    <!--<maven.compiler.target>11</maven.compiler.target>-->
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson-spring-boot-starter</artifactId>
      <version>${redisson.version}</version>
    </dependency>
    <dependency>
      <groupId>dtt.asset</groupId>
      <artifactId>dtt-framework-cache</artifactId>
      <version>${project.version}</version>
    </dependency>
  </dependencies>

</project>
