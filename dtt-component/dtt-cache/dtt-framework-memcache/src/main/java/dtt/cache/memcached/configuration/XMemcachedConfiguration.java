package dtt.cache.memcached.configuration;

import net.rubyeye.xmemcached.MemcachedClient;
import net.rubyeye.xmemcached.MemcachedClientBuilder;
import net.rubyeye.xmemcached.XMemcachedClientBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
public class XMemcachedConfiguration {
  @Bean
  public MemcachedClient getMemcachedClient() throws IOException {
    MemcachedClientBuilder memcachedClientBuilder = new XMemcachedClientBuilder("localhost:11211");
    MemcachedClient memcachedClient = memcachedClientBuilder.build();
    return memcachedClient;
  }
}
