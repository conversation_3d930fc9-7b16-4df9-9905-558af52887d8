package com.trinasolar.trinax.user.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户高级查询入参")
public class UsersQueryDTO extends UserQueryAuthDTO {

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "关键字")
    private String keyword;
    //外部
    @Schema(description = "状态")
    private Collection<String> status;
    @Schema(description = "角色")
    private Collection<String> roleIds;
    //内部多余
    @Schema(description = "隶属组织")
    private String organizationCode;

    @Schema(description = "隶属组织（列表）", hidden = true)
    private List<String> organizationCodeList;

    @Schema(description = "地区")
    private Collection<String> regions;

    @Schema(description ="所属省份编码")
    private String userProvinceCode;

    @Schema(description ="所属城市编码")
    private String userCityCode;

    @Schema(description = "用户运营跟进状态")
    private Collection<Integer> followStatus;

    @Schema(description = "是否绑定企业")
    private Integer isBindEnterprise;

    @Schema(description ="注册开始时间")
    private LocalDateTime registrationTimeStart;

    @Schema(description ="注册结束时间")
    private LocalDateTime registrationTimeEnd;

    @Schema(description ="是否已同步区域总")
    private Integer isAssignManager;

    @Schema(description ="销售跟进状态")
    private List<Integer> saleFollowStatus;
}
