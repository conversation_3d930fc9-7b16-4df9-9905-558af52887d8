package com.trinasolar.trinax.user.api;

import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.user.constants.ServiceIds;
import com.trinasolar.trinax.user.dto.input.*;
import com.trinasolar.trinax.user.dto.input.query.CoAttachReqDTO;
import com.trinasolar.trinax.user.dto.input.relation.CoBindInfReqDTO;
import com.trinasolar.trinax.user.dto.input.relation.EnterpriseRelUnBindReqDTO;
import com.trinasolar.trinax.user.dto.input.relation.EnterpriseUserConfirmReqDTO;
import com.trinasolar.trinax.user.dto.input.relation.ModifyContactReqDTO;
import com.trinasolar.trinax.user.dto.output.*;
import com.trinasolar.trinax.user.dto.output.authentication.AuthenticationResDTO;
import com.trinasolar.trinax.user.dto.output.query.CoAttachBindResDTO;
import com.trinasolar.trinax.user.dto.output.query.CoAttachSalesResDTO;
import com.trinasolar.trinax.user.dto.output.relation.ContactorSyncResDTO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@Validated
@FeignClient(name = ServiceIds.USER_SERVICE)
public interface SysDealerSalesRelationFeign {

    /**
     * 通过用户id+关系类型获取企业与用户关系
     */
    @GetMapping("/dealer-sales-relation/enterprise-user-relations")
    Result<List<SysEnterpriseUserRelationResDTO>> listEnterpriseUserRelation(@RequestParam("userId") String userId,
                                                                             @RequestParam("enterpriseUserType") String enterpriseUserType);

    @GetMapping("/dealer-sales-relation/usersByEnterpriseIdAndType")
    Result<List<SysEnterpriseUserRespDTO>> listUserByEnterpriseIdAndType(@RequestParam("enterpriseId") String enterpriseId,
                                                                         @RequestParam("enterpriseUserType") String enterpriseUserType);

    /**
     * 根据不同的用户类型，拼凑不同的条件查询sys_dealer_sales_relation
     *
     * @param userId，根据类型确定是销售还是经销商
     * @param enterpriseUserType    目前区分为内部、外部用户
     * @return
     */
    @GetMapping("/dealer-sales-relation/linkEnterpriseUnion")
    Result<List<SysDealerSalesRelationRespDTO>> getLinkEnterpriseUnion(@RequestParam("userId") String userId,
                                                                       @RequestParam("enterpriseUserType") String enterpriseUserType);

    /**
     * 当前经销商用户获取他的下级用户信息（包含自身）
     *
     * @param dealerUserId
     * @return
     */
    @GetMapping("/dealer-sales-relation/getSubDealerInfo")
    Result<List<SysSubDealerRespDTO>> getSubDealerInfo(@RequestParam("dealerUserId") String dealerUserId);

    /**
     * 查询内部人员在指定企业下要服务的外部用户
     */
    @PostMapping("/dealer-sales-relation/usersByEnterpriseIdForSales")
    Result<List<SysEnterpriseUserRespDTO>> listUserByEnterpriseIdForSales(@Valid @RequestBody EnterpriseUserForSalesQueryDTO queryDTO);

    @Operation(description = "根据userId获取关联的企业列表+用户信息")
    @PostMapping("/dealer-sales-relation/associatedEnterprise")
    Result<AssociatedUserEnterpriseRespDTO> associatedEnterprise(@RequestBody @Valid AssociatedEnterpriseQueryDTO reqDTO);

    @Operation(description = "外部用户操作重启认证")
    @PostMapping("/dealer-sales-relation/enterpriseUserConfirm")
    Result<String> enterpriseUserReboot(@RequestBody EnterpriseUserConfirmReqDTO req);

    @Operation(summary = "分配专属销售")
    @PostMapping({"/allocateExclusiveSales"})
    Result<Void> allocateExclusiveSales(@RequestBody @Valid AllocateExclusiveSalesDTO allocateExclusiveSalesDTO);

    @Operation(summary = "企业解绑用户")
    @PostMapping({"/unBinding"})
    Result<String> unBinding(@RequestBody @Validated EnterpriseRelUnBindReqDTO reqDTO);

    @Operation(summary = "我绑定的企业（外部人员）-分页")
    @PostMapping("/dealer-sales-relation/pageExternalEnterprise")
    Result<PageResponse<ExternalEnterpriseRespDTO>> pageExternalEnterprise(@RequestBody @Valid PageRequest<PageExternalEnterpriseQueryDTO> pageReqDTO);

    @Operation(summary = "我绑定的企业（外部人员）-列表")
    @PostMapping("/dealer-sales-relation/listExternalEnterprise")
    Result<List<ExternalEnterpriseRespDTO>> listExternalEnterprise(@RequestBody PageExternalEnterpriseQueryDTO reqDTO);


    @Operation(summary = "根据企业id获取关联的用户列表-分页")
    @PostMapping("/dealer-sales-relation/pageAssociatedUsers")
    Result<PageResponse<SysDealerUserRespDTO>> pageAssociatedUsers(@RequestBody @Valid PageRequest<EnterpriseAssociatedUserQueryDTO> pageReqDTO);

    @Operation(summary = "认证失败")
    @PostMapping("/dealer-sales-relation/authenticationFail")
    Result<Void> authenticationFail(@RequestBody @Valid AuthenticationFailedDTO reqDTO);

    @Operation(summary = "认证成功")
    @PostMapping("/dealer-sales-relation/authenticationSuccess")
    Result<AuthenticationResDTO> authenticationSuccess(@RequestBody @Valid AuthenticationSuccessDTO reqDTO);

    @Operation(summary = "根据企业id和经销商用户id查询记录")
    @GetMapping("/dealer-sales-relation/listByEnterpriseIdAndDealerUserId")
    Result<List<SysDealerSalesRelationRespDTO>> listByEnterpriseIdAndDealerUserId(@RequestParam String enterpriseId, @RequestParam String dealerUserId);

    @Operation(summary = "根据企业id和经销商用户id查询记录")
    @PostMapping("/dealer-sales-relation/listByEnterpriseIdAndDealerUserIdBatch")
    Result<List<SysDealerSalesRelationRespDTO>> listByEnterpriseIdAndDealerUserIdBatch(@RequestBody List<SysSalesDealerRelationReqDTO> reqDTO);

    @Operation(summary = "我的客户（内部人员）-分页")
    @PostMapping("/dealer-sales-relation/pageInternalEnterprise")
    Result<PageResponse<SalesEnterpriseRespDTO>> pageInternalEnterprise(@RequestBody PageRequest<InternalEnterpriseQueryDTO> pageReqDTO);

    @Operation(summary = "用户详情-我的客户（内部人员）-分页")
    @PostMapping("/dealer-sales-relation/userDetailPageInternalEnterprise")
    Result<PageResponse<SalesEnterpriseRespDTO>> userDetailPageInternalEnterprise(@RequestBody PageRequest<InternalEnterpriseQueryDTO> pageReqDTO);

    @Operation(summary = "我的客户（内部人员）-列表 ")
    @PostMapping("/dealer-sales-relation/listInternalEnterprise")
    Result<List<SalesEnterpriseRespDTO>> listInternalEnterprise(@RequestBody InternalEnterpriseQueryDTO reqDTO);


    @Operation(summary = "销售人员查看我所服务的企业用户（分页）")
    @PostMapping("/dealer-sales-relation/pageMyDealers")
    Result<PageResponse<SysDealerRespDTO>> pageMyDealers(@RequestBody PageRequest<MyDealerQueryDTO> pageReqDTO);

    @Operation(summary = "根据企业加外部用户查询专属销售列表接口")
    @GetMapping("/dealer-sales-relation/salesByEnterpriseIdAndDealerUserId")
    Result<List<SysDealerSalesRespDTO>> salesByEnterpriseIdAndDealerUserId(@RequestParam String enterpriseId, @RequestParam String dealerUserId);

    @Operation(summary = "更新主销售为专属销售")
    @PostMapping("/dealer-sales-relation/updateMainSalesToSales")
    Result<Void> updateMainSalesToSales(@Valid @RequestBody UpdateMainSalesToSalesDTO updateMainSalesToSalesDTO);

    @Operation(summary = "销售人员查看我关联的用户（分页）")
    @PostMapping("/dealer-sales-relation/pageMyAssociatedDealerUser")
    Result<PageResponse<AssociatedDealerUserRespDTO>> pageMyAssociatedDealerUser(@RequestBody PageRequest<MyAssociatedDealerUserQueryDTO> pageReqDTO);

    @Operation(summary = "根据销售id列表查询作为专属销售的企业id列表（去重）")
    @PostMapping("/dealer-sales-relation/listEnterpriseIdBySalesIds")
    Result<List<String>> listEnterpriseIdBySalesIds(@RequestBody List<String> salesUserIds);

    @Operation(summary = "转需求可以选择的人员数据-生态合作伙伴-分页")
    @PostMapping("/user-role/pageRequirementEnterpriseUser")
    Result<PageResponse<RequirementChangeExternalUserRespDTO>> pageRequirementEnterpriseUser(@Valid @RequestBody PageRequest<RequirementChangeUserQueryDTO> pageReqDTO);

    @Operation(summary = "需求单选择的企业-列表")
    @PostMapping("/dealer-sales-relation/requirementEnterprises")
    Result<List<RequirementEnterpriseRespDTO>> requirementEnterprises(@RequestBody @Valid RequirementEnterpriseQueryDTO reqDTO);

    @Operation(summary = "查询企业的销售")
    @PostMapping("/dealer-sales-relation/pageEnterpriseSaleUser")
    Result<PageResponse<EnterpriseSalesUserRespDTO>> pageEnterpriseSaleUser(@RequestBody @Valid PageRequest<EnterpriseSalesUserQueryDTO> pageReqDTO);

    @Operation(summary = "根据角色查询企业的用户id列表")
    @GetMapping("/dealer-sales-relation/dealerUserByEnterpriseIdAndRole")
    List<String> dealerUserByEnterpriseIdAndAuthStatus(@RequestParam String enterpriseId, @RequestParam(required = false) Integer authStatus);

    @Operation(summary = "查询绑定信息")
    @PostMapping("/dealer-sales-relation/qyrBindInfoByCondition")
    List<ContactorSyncResDTO> qyrBindInfoByCondition(@RequestBody CoBindInfReqDTO req);

    @Operation(summary = "更新联系人 sfId")
    @PostMapping("/dealer-sales-relation/modifyContactInfo")
    Result<Void> modifyContactInfo(@RequestBody List<ModifyContactReqDTO> req);


    @Operation(summary = "查询内部人员在指定企业下要服务的外部用户(当前登录用户) -编辑准入的时候选择联系人")
    @PostMapping("/dealer-sales-relation/listContactByEnterpriseId")
    Result<List<SysDealerRespDTO>> listContactByEnterpriseId(@Valid @RequestBody ContactEnterpriseUserQueryDTO queryDTO);

    @Operation(summary = "根据企业id列表和认证状态查询用户企业关系")
    @PostMapping("/dealer-sales-relation/listByEnterpriseIdsAndAuthStatus")
    List<SysDealerSalesRelationRespDTO> listByEnterpriseIdsAndAuthStatus(@RequestBody EnterpriseIdsAndAuthStatusQueryDTO queryDTO);

    @Operation(summary = "更新关系表中的客户类别")
    @PostMapping("/dealer-sales-relation/updateEnterpriseCustomerCategory")
    void updateEnterpriseCustomerCategory(@RequestParam String enterpriseId, @RequestParam String customerCategory);

    @Operation(summary = "外部用户查询认证通过的企业id列表")
    @GetMapping("/dealer-sales-relation/authPassedEnterpriseByDealer")
    Result<List<String>> authPassedEnterpriseByDealer( @RequestParam String dealerUserId);

    @Operation(summary = "查询企业销售信息-通过外部用户")
    @PostMapping("/dealer-sales-relation/qryCoSaleAttach")
    Result<List<CoAttachSalesResDTO>> qryCoSaleAttach(@RequestBody @Valid CoAttachReqDTO req);

    @Operation(summary = "查询企业绑定用户信息-通过销售")
    @PostMapping("/dealer-sales-relation/qryCoBindAttach")
    Result<List<CoAttachBindResDTO>> qryCoBindAttach(@RequestBody @Valid CoAttachReqDTO req);


    @Operation(summary = "根据销售id列表查询作为专属销售的企业、经销商关系列表")
    @PostMapping("/dealer-sales-relation/listSysDealerSalesRelationBySalesIds")
    Result<List<SysDealerSalesRelationRespDTO>> listSysDealerSalesRelationBySalesIds(@RequestBody List<String> salesUserIds);

    @Operation(summary = "快速订单 我的客户（内部人员）-列表 ")
    @PostMapping("/dealer-sales-relation/quickOrder/listInternalEnterprise")
    Result<List<SalesEnterpriseRespDTO>> quickOrderListInternalEnterprise(@RequestBody InternalEnterpriseQueryDTO reqDTO);

    @Operation(summary = "快速订单 我的客户（外部人员）-列表 ")
    @PostMapping("/dealer-sales-relation/quickOrder/listExternalEnterprise")
    Result<List<ExternalEnterpriseRespDTO>> quickOrderListExternalEnterprise(@RequestBody PageExternalEnterpriseQueryDTO reqDTO);
}