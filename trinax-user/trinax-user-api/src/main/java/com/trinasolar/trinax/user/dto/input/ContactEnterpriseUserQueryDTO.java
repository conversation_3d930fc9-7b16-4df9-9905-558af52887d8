package com.trinasolar.trinax.user.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class ContactEnterpriseUserQueryDTO {

    @NotBlank
    @Schema(description = "企业id")
    private String enterpriseId;

    @Schema(description = "登录人员id", hidden = true)
    private String loginUserId;
}
