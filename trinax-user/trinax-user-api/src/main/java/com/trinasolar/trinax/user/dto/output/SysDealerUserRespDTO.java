package com.trinasolar.trinax.user.dto.output;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class SysDealerUserRespDTO {

    @Schema(description = "经销商用户id")
    private String dealerUserId;

    @Schema(description = "用户编码（内部员工则是员工编码）")
    private String dealerUserCode;

    @Schema(description = "用户类型：INTERNAL-内部员工，EXTERNAL-外部用户")
    private String dealerUserType;

    @Schema(description = "用户类型str")
    private String dealerUserTypeStr;

    @Schema(description = "用户姓名")
    private String dealerUserName;

    @Schema(description = "用户状态：ENABLE-启用，FREEZE-冻结，WITHDRAW-注销")
    private String dealerUserStatus;

    @Schema(description = "用户状态str")
    private String dealerUserStatusStr;

    @Schema(description = "手机号")
    private String dealerUserMobile;

    @Schema(description = "销售用户id")
    private String salesUserId;

    @Schema(description = "销售用户名称（用户所有人）")
    private String salesUserName;

    @Schema(description = "企业成员关系认证状态 1:认证通过 0:认证失败 2:认证中")
    private Integer authStatus;

    @Schema(description = "企业成员关系认证状态str")
    private String authStatusStr;

    @Schema(description = "角色")
    private String roleText;

}
