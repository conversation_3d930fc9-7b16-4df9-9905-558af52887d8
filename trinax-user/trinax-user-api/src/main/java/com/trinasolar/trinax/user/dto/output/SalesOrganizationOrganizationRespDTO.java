package com.trinasolar.trinax.user.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SalesOrganizationOrganizationRespDTO {

    @Schema(description = "销售人员ID")
    private String salesUserId;

    @Schema(description = "销售人员姓名")
    private String salesUserName;

    @Schema(description = "服务组织CODE")
    private String bizOrganizationCode;

    @Schema(description = "服务组织名称")
    private String bizOrganizationName;

    @Schema(description = "汇报对象人员ID")
    private String reportToUserId;

    @Schema(description = "汇报对象人员姓名")
    private String reportToUserName;

    @Schema(description="运营人员ID")
    private String operationUserId;

    @Schema(description="运营人员Name")
    private String operationUserName;

}
