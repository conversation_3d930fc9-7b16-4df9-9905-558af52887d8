package com.trinasolar.trinax.user.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SysSalesOrganizationRelationQueryReqDTO {

    @Schema(description = "销售人员ID")
    private String salesUserId;

    @Schema(description = "服务组织CODE")
    private String bizOrganizationCode;

    @Schema(description = "服务组织CODE（列表）")
    private List<String> bizOrganizationCodeList;

    @Schema(description = "汇报对象人员ID")
    private String reportToUserId;

    @Schema(description = "是否已删除;1：已删除 0：未删除")
    private Integer isDeleted;

}
