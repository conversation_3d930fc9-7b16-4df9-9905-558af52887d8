package com.trinasolar.trinax.user.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 销售与运营关系 Request DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SysSalesOperationRelationReqDTO {
    private Long id;

    @NotBlank(message = "salesUserId不能为空")
    @Schema(description="销售人员ID")
    private String salesUserId;
    @Schema(description="服务组织CODE")
    private String bizOrganizationCode;

    @Schema(description = "运营人员userid")
    private String operationUserId;

    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    private String createdBy;
    private String createdName;
    private String updatedBy;
    private String updatedName;

    public SysSalesOperationRelationReqDTO(String salesUserId, String bizOrganizationCode) {
        this.salesUserId = salesUserId;
        this.bizOrganizationCode = bizOrganizationCode;
    }
}
