package com.trinasolar.trinax.user.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@AllArgsConstructor
@NoArgsConstructor
@Data
public class SysDealerSalesRespDTO {

    @Schema(description = "销售用户id")
    private String salesUserId;

    @Schema(description = "销售用户名称")
    private String salesUserName;

    @Schema(description = "联系方式：手机号")
    private String mobile;

}
