package com.trinasolar.trinax.user.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class QuestionnaireExternalUserRespDTO {

    @Schema(description = "用户唯一标识")
    private String userId;

    @Schema(description = "用户姓名")
    private String userName;

    @Schema(description ="手机号")
    private String mobile;

    @Schema(description = "角色名称列表")
    private List<String> roleNames;

    @Schema(description = "企业名称列表")
    private List<String> enterpriseNames;
}
