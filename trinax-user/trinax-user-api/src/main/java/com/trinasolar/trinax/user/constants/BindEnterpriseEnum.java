package com.trinasolar.trinax.user.constants;

/**
 * @module: trinax-user
 * @package: com.trinasolar.trinax.user.constants
 * @description:
 * @author: hongfei.wang
 * @create: 2024/8/6
 **/
public enum BindEnterpriseEnum {

    // 分配状态
    NO(0, "否"),

    YES(1, "是"),

    PENDING(2, "认证中");

    /**
     *     状态代码
     */
    private Integer code;

    /**
     * 状态描述
     */
    private String description;

    /**
     * 构造方法
     * @param code 状态代码
     * @param description 状态描述
     */
    BindEnterpriseEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据状态代码获取枚举值
     * @param code 状态代码
     * @return 对应的枚举值
     * @throws IllegalArgumentException 如果代码无效则抛出异常
     */
    public static BindEnterpriseEnum valueOfCode(int code) {
        for (BindEnterpriseEnum status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    /**
     * 获取状态代码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取状态描述
     */
    public String getDescription() {
        return description;
    }
}