package com.trinasolar.trinax.user.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 销售与运营关系 Response DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SysSalesDealerRelationResDTO {
    private Long id;

    @NotBlank(message = "salesUserId不能为空")
    @Schema(description="销售人员ID")
    private String salesUserId;
    @Schema(description="enterpriseId")
    private String enterpriseId;

    @Schema(description = "上级经销商用户id")
    private String parentDealerUserId;


    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    private String createdBy;
    private String createdName;
    private String updatedBy;
    private String updatedName;

}
