package com.trinasolar.trinax.user.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class SysOrganizationTreeRespDTO {

    @Schema(description ="组织Code")
    private String organizationCode;

    @Schema(description ="组织名")
    private String organizationName;

    @Schema(description ="组织类型")
    private String organizationType;

    @Schema(description ="组织层级")
    private Integer organizationLevel;

    @Schema(description ="组织描述")
    private String description;

    @Schema(description ="父级权限Code")
    private String parentOrganizationCode;

    @Schema(description ="区域")
    private List<SysAreaDTO> areas;

    private List<SysOrganizationTreeRespDTO> children;
}
