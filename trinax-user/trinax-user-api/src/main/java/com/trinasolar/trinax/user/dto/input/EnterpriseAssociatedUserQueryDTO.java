package com.trinasolar.trinax.user.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;


@EqualsAndHashCode(callSuper = true)
@Data
public class EnterpriseAssociatedUserQueryDTO extends UserQueryAuthDTO {

    @NotBlank(message = "企业id不能为空")
    @Schema(description = "企业id")
    String enterpriseId;

}
