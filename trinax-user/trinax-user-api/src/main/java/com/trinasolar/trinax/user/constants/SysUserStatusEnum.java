package com.trinasolar.trinax.user.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum SysUserStatusEnum {
    ENABLE("ENABLE", "已启用"),
    FREEZE("FREEZE", "已冻结"),
    WIT<PERSON>RAW("WITHD<PERSON><PERSON>", "已注销");
    /**
     * 类型
     */
    private final String code;
    /**
     * 类型名
     */
    private final String desc;

    public static String getDescByCode(String code){
        for (SysUserStatusEnum value : SysUserStatusEnum.values()) {
            if (value.getCode().equals(code)){
                return value.getDesc();
            }
        }
        return "";
    }

}
