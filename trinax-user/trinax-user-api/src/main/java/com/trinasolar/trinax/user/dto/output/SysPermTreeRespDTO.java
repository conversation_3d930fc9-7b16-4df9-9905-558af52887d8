package com.trinasolar.trinax.user.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * Admin 用户 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class SysPermTreeRespDTO {

    @Schema(description = "系统权限ID")
    private String permissionId;

    @Schema(description = "系统权限名称")
    private String permissionName;

    @Schema(description = "系统权限编码")
    private String permissionCode;

    @Schema(description = "系统权限类型 PAGE-界面，BUTTON-按钮")
    private String permissionType;

    @Schema(description = "系统权限排序")
    private Integer seq;

    @Schema(description = "系统权限所属应用 TRINAX_APP-德勤通APP，TRINAX_MANAGE-德勤通后台")
    private String permissionApplication;

    @Schema(description = "父级权限ID")
    private String parentId;

    private List<SysPermTreeRespDTO> children;
}
