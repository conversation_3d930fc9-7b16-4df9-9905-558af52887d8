package com.trinasolar.trinax.user.api;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.user.constants.ServiceIds;
import com.trinasolar.trinax.user.dto.output.AppClientResDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(name = ServiceIds.USER_SERVICE, fallbackFactory = Void.class)
public interface AppClientFeign {

    /***
     * 通过clientId查询
     * @param clientId
     * @return
     */
    @ApiOperation(value = "通过clientId查询 ")
    @GetMapping("/findClientByClientId/{clientId}")
    Result<AppClientResDTO> findClientByClientId(@PathVariable String clientId);

}
