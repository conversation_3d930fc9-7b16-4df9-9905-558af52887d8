package com.trinasolar.trinax.user.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class AuthenticationFailedDTO {

    @NotBlank(message = "经销商用户id不能为空")
    @Schema(description = "经销商用户id")
    private String dealerUserId;

    @NotBlank(message = "企业id不能为空")
    @Schema(description = "企业id")
    private String enterpriseId;

    @Schema(description = "更新人", hidden = true)
    private String updateBy;

    @Schema(description = "更新人姓名", hidden = true)
    private String updateName;

}
