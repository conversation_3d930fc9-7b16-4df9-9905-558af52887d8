package com.trinasolar.trinax.user.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum SysOrganizationTypeEnum {
    SALES("SALES", "销售"),
    COMMON("COMMON", "常规"),
    SERVICE("SERVICE", "技术服务"),
    OPERATION("OPERATION", "运营"),
    ;
    /**
     * 类型
     */
    private final String code;
    /**
     * 类型名
     */
    private final String desc;

    public static String getDescByCode(String code) {
        for (SysOrganizationTypeEnum value : SysOrganizationTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

}
