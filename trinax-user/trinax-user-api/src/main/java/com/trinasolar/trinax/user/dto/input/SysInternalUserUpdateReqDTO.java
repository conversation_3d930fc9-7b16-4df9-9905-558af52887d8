package com.trinasolar.trinax.user.dto.input;

import com.trinasolar.trinax.user.dto.output.SysInternelSalesRelationRespDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * Admin 内部用户修改 Request DTO
 *
 * <AUTHOR>
 */
@Data
public class SysInternalUserUpdateReqDTO {

    private Long id;
    @NotBlank(message = "userId不能为空")
    private String userId;
    @Schema(description = "上级userid")
    private String parentUserId;

    @Schema(description = "上级userid",hidden = true)
    private String originParentUserId;

    @NotBlank(message = "名字不能为空")
    private String userName;

    @NotBlank(message = "状态不能为空")
    private String status;

    @NotBlank(message = "用户类型不能为空")
    private String userType;
//    private String userEmail;
//    private String userCode;

    @Schema(description ="SF客户ID")
    private String sfId;

    @Pattern(regexp = "^1\\d{10}$",message = "手机号码格式错误")
    @NotBlank(message = "手机号不能为空")
    private String mobile;
//    private List<SysSalesOperationRelationReqDTO> serviceOrg;
    private List<SysInternelSalesRelationRespDTO> salesRelations;
    private String organizationCode;

    @Schema(description ="更新人")
    private String updatedBy;

    @Schema(description ="更新人姓名")
    private String updatedName;

}
