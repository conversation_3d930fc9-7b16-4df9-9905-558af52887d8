package com.trinasolar.trinax.user.constants;

/**
 * @module: trinax-user
 * @package: com.trinasolar.trinax.user.constants
 * @description:
 * @author: hongfei.wang
 * @create: 2024/8/6
 **/
public enum SaleFollowStatusEnum {

    PENDING(0, "待跟进"),

    FOLLOWING(1, "跟进中"),

    VALID(2, "有效"),

    UNVALID(3, "无效");

    /**
     *     状态代码
     */
    private Integer code;

    /**
     * 状态描述
     */
    private String description;

    /**
     * 构造方法
     * @param code 状态代码
     * @param description 状态描述
     */
    SaleFollowStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据状态代码获取枚举值
     * @param code 状态代码
     * @return 对应的枚举值
     * @throws IllegalArgumentException 如果代码无效则抛出异常
     */
    public static SaleFollowStatusEnum valueOfCode(int code) {
        for (SaleFollowStatusEnum status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    /**
     * 获取状态代码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取状态描述
     */
    public String getDescription() {
        return description;
    }
}