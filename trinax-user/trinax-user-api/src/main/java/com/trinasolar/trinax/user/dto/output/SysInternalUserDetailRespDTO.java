package com.trinasolar.trinax.user.dto.output;

import com.trinasolar.trinax.user.dto.input.SysSalesOperationRelationReqDTO;
import com.trinasolar.trinax.user.dto.input.SysSalesOrganizationRelationReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * Admin 内部用户详情 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class SysInternalUserDetailRespDTO {

    private Long id;
    /**
     * 用户唯一标识
     */
    private String userId;
    private String userEmail;
    private String userCode;
    private String userName;
    private String status;
    private String mobile;
    private String organizationCode;
    private String createdName;
    private String updatedBy;
    private LocalDateTime createdTime;
    private String updatedName;
    private LocalDateTime updatedTime;
    private LocalDateTime lastLoginTime;
    private Collection<String> roleIds;
//    private List<SysSalesOperationRelationReqDTO> serviceOrg;
//    private List<SysSalesOrganizationRelationReqDTO> salesOrg;
    private List<SysInternelSalesRelationRespDTO> salesRelations;
    private String parentUserId;
    @Schema(description = "SF 客户ID")
    private String sfId;

}
