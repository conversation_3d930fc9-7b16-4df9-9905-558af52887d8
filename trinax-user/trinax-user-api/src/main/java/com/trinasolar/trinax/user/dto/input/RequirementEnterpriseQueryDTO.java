package com.trinasolar.trinax.user.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class RequirementEnterpriseQueryDTO {

    @Schema(description = "销售用户id")
    private String salesUserId;

    @NotBlank(message = "外部用户id不能为空")
    @Schema(description = "经销商用户id")
    private String dealerUserId;


}
