package com.trinasolar.trinax.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.trinasolar.trinax.basic.api.OptionFeign;
import com.trinasolar.trinax.basic.api.TodoListFeign;
import com.trinasolar.trinax.basic.constants.enums.OptionGroupConstant;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoBizCodeEnum;
import com.trinasolar.trinax.basic.dto.input.TodoListLogicDeleteReqDTO;
import com.trinasolar.trinax.basic.dto.input.option.OptionItemQueryReqDTO;
import com.trinasolar.trinax.basic.dto.output.OptionItemResDTO;
import com.trinasolar.trinax.basic.dto.output.TodoListResDTO;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.partner.api.EnterpriseAddressFeign;
import com.trinasolar.trinax.partner.api.EnterpriseBizFeign;
import com.trinasolar.trinax.partner.api.EnterpriseBizRelationFeign;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.constants.ContactorSyncConstant;
import com.trinasolar.trinax.partner.constants.PartnerConstant;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseTypeEnum;
import com.trinasolar.trinax.partner.dto.input.EnterpriseBizAuthorizeQueryDTO;
import com.trinasolar.trinax.partner.dto.input.EnterpriseQueryDTO;
import com.trinasolar.trinax.partner.dto.input.LinkEnterpriseUnionQueryReqDTO;
import com.trinasolar.trinax.partner.dto.mq.ContactorSyncMqDTO;
import com.trinasolar.trinax.partner.dto.output.*;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseAddressRegRespDTO;
import com.trinasolar.trinax.user.constants.*;
import com.trinasolar.trinax.user.dto.input.*;
import com.trinasolar.trinax.user.dto.input.query.CoAttachReqDTO;
import com.trinasolar.trinax.user.dto.input.relation.CoBindInfReqDTO;
import com.trinasolar.trinax.user.dto.input.relation.EnterpriseRelUnBindReqDTO;
import com.trinasolar.trinax.user.dto.input.relation.EnterpriseUserConfirmReqDTO;
import com.trinasolar.trinax.user.dto.input.relation.ModifyContactReqDTO;
import com.trinasolar.trinax.user.dto.output.*;
import com.trinasolar.trinax.user.dto.output.authentication.AuthenticationResDTO;
import com.trinasolar.trinax.user.dto.output.query.CoAttachBindResDTO;
import com.trinasolar.trinax.user.dto.output.query.CoAttachSalesResDTO;
import com.trinasolar.trinax.user.dto.output.relation.ContactorSyncResDTO;
import com.trinasolar.trinax.user.manager.UserRelationManager;
import com.trinasolar.trinax.user.manager.UserRoleUpdateManager;
import com.trinasolar.trinax.user.manager.caffeine.DealerSalesRelationCache;
import com.trinasolar.trinax.user.repository.atomicservice.RoleMapperService;
import com.trinasolar.trinax.user.repository.atomicservice.SysDealerSalesRelationMapperService;
import com.trinasolar.trinax.user.repository.atomicservice.SysUserRoleMapperService;
import com.trinasolar.trinax.user.repository.mapper.SysDealerSalesRelationMapper;
import com.trinasolar.trinax.user.repository.mapper.SysUserMapper;
import com.trinasolar.trinax.user.repository.po.RolePO;
import com.trinasolar.trinax.user.repository.po.SysDealerSalesRelationPO;
import com.trinasolar.trinax.user.repository.po.SysUserPO;
import com.trinasolar.trinax.user.repository.po.SysUserRolePO;
import com.trinasolar.trinax.user.service.SysDealerSalesRelationService;
import com.trinasolar.trinax.user.service.SysRoleService;
import com.trinasolar.trinax.user.service.SysUserRoleService;
import com.trinasolar.trinax.user.service.SysUserService;
import com.trinasolar.trinax.user.service.biz.*;
import com.trinasolar.trinax.user.service.biz.enterprise.CoBindRelModifyBiz;
import com.trinasolar.trinax.user.service.biz.enterprise.CoBindRelQryBiz;
import com.trinasolar.trinax.user.service.biz.enterprise.EnterpriseUserRebootBiz;
import com.trinasolar.trinax.user.service.biz.query.CoAttachQueryBiz;
import dtt.asset.dttframeworkmq.manager.MqManager;
import dtt.cache.redisclient.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【sys_dealer_sales_relation(经销商用户与销售人员关系)】的数据库操作Service实现
 * @createDate 2023-11-14 11:10:18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysDealerSalesRelationServiceImpl implements SysDealerSalesRelationService, InitializingBean {

    private final EnterpriseFeign enterpriseFeign;
    private final UserRelationManager userRelationManager;
    private final SysDealerSalesRelationMapperService sysDealerSalesRelationMapperService;
    private final GetSubDealerInfoBiz getSubDealerInfoBiz;
    private final SysUserMapper sysUserMapper;
    private final RedisUtil redisUtil;
    private final RoleMapperService roleMapperService;
    private final SysUserRoleMapperService sysUserRoleMapperService;
    private final DealerSalesRelationFillBiz dealerSalesRelationFillBiz;
    private final DealerSalesRelDelBiz dealerSalesRelDelBiz;
    private final SysRoleService roleService;
    private final SysDealerSalesRelationMapper sysDealerSalesRelationMapper;
    private final AllocateExclusiveSalesBiz allocateExclusiveSalesBiz;
    private final SysUserRoleService sysUserRoleService;
    private final SysUserService sysUserService;
    private final DealerSalesSendMsgBiz dealerSalesSendMsgBiz;
    private final TodoListUpdateStatusBiz todoListUpdateStatusBiz;
    private final DealerSalesQueryBiz dealerSalesQueryBiz;
    private final DealerSalesConvertBiz dealerSalesConvertBiz;
    private final EnterpriseBizRelationFeign enterpriseBizRelationFeign;
    private final EnterpriseUserRebootBiz enterpriseUserRebootBiz;
    private final TodoListFeign todoListFeign;
    private final UserRoleUpdateManager userRoleUpdateManager;
    private final RequirementPageChangeUserBiz requirementPageChangeUserBiz;
    private final OptionFeign optionFeign;
    private final EnterpriseAddressFeign enterpriseAddressFeign;
    private final UserAuthBiz userAuthBiz;
    private final UserFillBiz userFillBiz;
    private final EnterpriseBizFeign enterpriseBizFeign;
    private final CoBindRelQryBiz coBindRelQryBiz;
    private final CoBindRelModifyBiz coBindRelModifyBiz;
    private final MqManager mqManager;
    private final RedisTemplate redisTemplate;
    private final DealerSalesRelationCache dealerSalesRelationCache;
    private final CoAttachQueryBiz coAttachQueryBiz;
    private final SysUserAggregateBiz sysUserAggregateBiz;

    @Value("${spring.profiles.active}")
    private String active;


    @Override
    public List<SysDealerSalesRelationRespDTO> getLinkEnterpriseUnion(String userId, String enterpriseUserType) {
        List<SysEnterpriseUserRelationResDTO> sysEnterpriseUserRelationResDTOS = userRelationManager.
                listEnterpriseUserRelation(userId, enterpriseUserType);

        //类型是外部用户的isDefault才有用,否则这个接口返回的数据会有重复
        Map<String, Integer> enterpriseIdIsDefault = new HashMap<>(16, 0.75F);
        if (EnterpriseUserTypeEnum.DEALER.getType().equalsIgnoreCase(enterpriseUserType)) {
            enterpriseIdIsDefault = sysEnterpriseUserRelationResDTOS.stream()
                    .collect(Collectors.toMap(SysEnterpriseUserRelationResDTO::getEnterpriseId, SysEnterpriseUserRelationResDTO::getIsDefault));
        }

        List<String> enterpriseIds = sysEnterpriseUserRelationResDTOS.stream().map(SysEnterpriseUserRelationResDTO::getEnterpriseId).distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(enterpriseIds)) {
            return Collections.emptyList();
        }
        LinkEnterpriseUnionQueryReqDTO reqDTO = new LinkEnterpriseUnionQueryReqDTO();
        reqDTO.setEnterpriseIds(enterpriseIds);
        reqDTO.setEnterpriseType(EnterpriseTypeEnum.PARTNER.getCode());
        reqDTO.setLinkEnterpriseType(EnterpriseTypeEnum.CAPITAL.getCode());
        List<EnterpriseDTO> linkEnterpriseUnionDtos = enterpriseFeign.getLinkEnterpriseUnion(reqDTO).getData();
        List<SysDealerSalesRelationRespDTO> result = new ArrayList<>();
        for (EnterpriseDTO linkEnterpriseUnionDto : linkEnterpriseUnionDtos) {
            SysDealerSalesRelationRespDTO relation = new SysDealerSalesRelationRespDTO();
            relation.setEnterpriseId(linkEnterpriseUnionDto.getEnterpriseId());
            relation.setEnterpriseName(linkEnterpriseUnionDto.getName());
            relation.setIsDefault(enterpriseIdIsDefault.get(linkEnterpriseUnionDto.getEnterpriseId()));
            result.add(relation);
        }
        return result;
    }

    @Override
    public List<SysSubDealerRespDTO> getSubDealerInfo(String dealerUserId) {
        return getSubDealerInfoBiz.getSubDealerInfo(dealerUserId);
    }

    @Override
    public List<SysEnterpriseUserRespDTO> listUserByEnterpriseIdAndType(String enterpriseId, String enterpriseUserType) {
        List<SysDealerSalesRelationPO> relations = sysDealerSalesRelationMapperService.lambdaQuery()
                .eq(SysDealerSalesRelationPO::getEnterpriseId, enterpriseId)
                .list();

        List<String> userIds = null;
        if (StrUtil.equals(EnterpriseUserTypeEnum.DEALER.getType(), enterpriseUserType)) {
            userIds = relations.stream().map(SysDealerSalesRelationPO::getDealerUserId).collect(Collectors.toList());
        } else if (StrUtil.equals(EnterpriseUserTypeEnum.SALES.getType(), enterpriseUserType)) {
            userIds = relations.stream().map(SysDealerSalesRelationPO::getSalesUserId).collect(Collectors.toList());
        }

        if (CollectionUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }

        List<SysUserPO> users = sysUserMapper.getUsersByUserIds(userIds);
        return BeanUtil.copyToList(users, SysEnterpriseUserRespDTO.class);
    }

    @Override
    public List<SysEnterpriseUserRespDTO> listUserByEnterpriseIdForSales(EnterpriseUserForSalesQueryDTO queryDTO) {
        String loginUserId = queryDTO.getLoginUserId();
        String enterpriseId = queryDTO.getEnterpriseId();
        List<String> roleIds = sysUserRoleService.listRoleIdsByUserId(loginUserId);
        LambdaQueryWrapper<SysDealerSalesRelationPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDealerSalesRelationPO::getEnterpriseId, enterpriseId);
        if (roleIds.contains(UserConstant.SALES_AREA_MANAGER_ROLE_ID)) {
            // 1. 销售区域总能查看到自己所在战区的所有公司下的所有用户
        } else {
            EnterpriseDTO enterpriseDTO = enterpriseFeign.getEnterpriseByEnterpriseId(enterpriseId);
            if (loginUserId.equals(enterpriseDTO.getMainSalesUserId())) {
                // 2. 主销售能查看到自己负责公司下的所有用户
            } else {
                // 3. 专属销售能查看到自己负责的用户
                queryWrapper.eq(SysDealerSalesRelationPO::getSalesUserId, loginUserId);
            }
        }
        if (ObjectUtil.isNotEmpty(queryDTO.getAuthStatus())) {
            queryWrapper.eq(SysDealerSalesRelationPO::getAuthStatus, queryDTO.getAuthStatus());
        }
        List<SysDealerSalesRelationPO> dealerSalesRelations = sysDealerSalesRelationMapperService.list(queryWrapper);
        if (CollectionUtil.isEmpty(dealerSalesRelations)) {
            return new ArrayList<>();
        }
        // 开始转换成前端需要的数据对象
        Map<String, Integer> dealerUserId2authStatus = new HashMap<>();
        for (SysDealerSalesRelationPO dealerSalesRelation : dealerSalesRelations) {
            dealerUserId2authStatus.put(dealerSalesRelation.getDealerUserId(), dealerSalesRelation.getAuthStatus());
        }
        List<String> dealerUserIds = dealerSalesRelations.stream().map(SysDealerSalesRelationPO::getDealerUserId).collect(Collectors.toList());
        List<SysDealerSalesRelationPO> sysDealerSalesRelationPOS = sysDealerSalesRelationMapperService.listByDealerUserIdsAndEnterpriseId(dealerUserIds, enterpriseId);
        // 查询用户id和用户姓名的映射关系
        Map<String, String> userId2name = new HashMap<>();
        if (ObjectUtil.isNotEmpty(sysDealerSalesRelationPOS)) {
            List<String> salesUserIds = sysDealerSalesRelationPOS.stream().map(SysDealerSalesRelationPO::getSalesUserId).collect(Collectors.toList());
            List<SysUserPO> sysUserPOS = sysUserMapper.getUsersByUserIds(salesUserIds);
            for (SysUserPO sysUserPO : sysUserPOS) {
                userId2name.put(sysUserPO.getUserId(), sysUserPO.getUserName());
            }
        }

        Map<String, List<SysDealerSalesRelationPO>> dealerUserId2sysDealerSalesRelationPOS = sysDealerSalesRelationPOS.stream().collect(Collectors.groupingBy(SysDealerSalesRelationPO::getDealerUserId));
        List<SysUserRolePO> sysUserRolePOS = sysUserRoleMapperService.listByUserIds(dealerUserIds);
        Map<String, List<SysUserRolePO>> userId2sysUserRolePOS = sysUserRolePOS.stream().collect(Collectors.groupingBy(SysUserRolePO::getUserId));
        List<RolePO> rolePOS = roleMapperService.listByRoleIds(sysUserRolePOS.stream().map(SysUserRolePO::getRoleId).collect(Collectors.toList()));
        Map<String, String> roleId2name = rolePOS.stream().collect(Collectors.toMap(RolePO::getRoleId, RolePO::getRoleName));

        List<SysUserPO> users = sysUserMapper.getUsersByUserIds(dealerUserIds);
        List<SysEnterpriseUserRespDTO> userList = BeanUtil.copyToList(users, SysEnterpriseUserRespDTO.class);
        userList.forEach(user -> {
            List<SysUserRolePO> sysUserRolePOS1 = userId2sysUserRolePOS.get(user.getUserId());
            if (ObjectUtil.isNotEmpty(sysUserRolePOS1)) {
                List<SysRoleDTO> sysRoleDTOS = sysUserRolePOS1.stream().map(e -> new SysRoleDTO(e.getRoleId(), roleId2name.get(e.getRoleId()))).collect(Collectors.toList());
                user.setRoles(sysRoleDTOS);
            }
            List<SysDealerSalesRelationPO> sysDealerSalesRelationPOS02 = dealerUserId2sysDealerSalesRelationPOS.get(user.getUserId());
            if (ObjectUtil.isNotEmpty(sysDealerSalesRelationPOS02)) {
                user.setSalesUserNames(sysDealerSalesRelationPOS02.stream().map(e -> userId2name.get(e.getSalesUserId())).collect(Collectors.toList()));
            }
            user.setAuthStatus(dealerUserId2authStatus.get(user.getUserId()));
        });

        if(queryDTO.getIsQuickOrder()!=null&&queryDTO.getIsQuickOrder()){
            // 快速下单则过滤掉没有签署授权委托的用户
            userList=quickOrderFilterUser(userList,queryDTO.getEnterpriseId());
        }
        return userList;
    }

    /**
     * 快速下单过滤用户
     * @param userList
     */
    private List<SysEnterpriseUserRespDTO> quickOrderFilterUser(List<SysEnterpriseUserRespDTO> userList,String enterpriseId) {
        PageRequest<EnterpriseBizAuthorizeQueryDTO> pageRequest=new PageRequest<>();
        EnterpriseBizAuthorizeQueryDTO enterpriseBizAuthorizeQueryDTO=new EnterpriseBizAuthorizeQueryDTO();
        enterpriseBizAuthorizeQueryDTO.setEnterpriseId(enterpriseId);
        enterpriseBizAuthorizeQueryDTO.setAuthStatus("Access");
        enterpriseBizAuthorizeQueryDTO.setAuthYear(DateUtil.format(new Date(), DatePattern.NORM_YEAR_PATTERN));
        pageRequest.setQuery(enterpriseBizAuthorizeQueryDTO);
        pageRequest.setSize(Integer.MAX_VALUE);
        pageRequest.setIndex(1);
        Result<PageResponse<EnterpriseBizAuthorizeDTO>> result=enterpriseBizFeign.queryAuthorizeByPage(pageRequest);
        Assert.isTrue(result.getSuccess(),"查询授权委托书信息异常");
        if(ObjectUtil.isEmpty(result.getData().getRecords())){
            return Collections.EMPTY_LIST;
        }else{
            List<EnterpriseBizAuthorizeDTO> enterpriseBizAuthorizeDTOS=result.getData().getRecords();
            Set<String> userIdSet=enterpriseBizAuthorizeDTOS.stream().map(e->e.getAuthUserId()).collect(Collectors.toSet());
            userList=userList.stream().filter(e->userIdSet.contains(e.getUserId())).collect(Collectors.toList());
            return userList;
        }
    }

    @Override
    public List<SysEnterpriseUserRespDTO> getEnterpriseDealerUsers(String internalUserId, String enterpriseId) {
        List<SysDealerSalesRelationPO> list = sysDealerSalesRelationMapperService.lambdaQuery()
                .eq(SysDealerSalesRelationPO::getEnterpriseId, enterpriseId)
                .eq(SysDealerSalesRelationPO::getSalesUserId, internalUserId)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> userIds = list.stream().map(e -> e.getDealerUserId()).distinct().collect(Collectors.toList());
        return userRelationManager.getUsersWithRoles(userIds);
    }

    @Override
    public List<SysEnterpriseUserRespDTO> getEnterpriseUsers(EnterpriseUserQueryReqDTO reqDTO) {
        List<SysDealerSalesRelationPO> list;
        String redisKey = new StringBuilder()
                .append(UserConstant.DATA_SALE_DEALER_CACHE_PREFIX)
                .append(reqDTO.getEnterpriseUserType())
                .append(":")
                .append(reqDTO.getEnterpriseId())
                .append(reqDTO.getSalesUserId())
                .append(reqDTO.getDealerUserId())
                .toString();
        String cache = redisUtil.get(redisKey);
        if (StringUtils.isNotBlank(cache)) {
            return JacksonUtil.json2List(cache, SysEnterpriseUserRespDTO.class);
        }
        List<SysEnterpriseUserRespDTO> sysEnterpriseUserRespDTOList;
        if (EnterpriseUserTypeEnum.SALES.getType().equals(reqDTO.getEnterpriseUserType())) {
            list = sysDealerSalesRelationMapperService.lambdaQuery()
                    .eq(StringUtils.isNotBlank(reqDTO.getEnterpriseId()), SysDealerSalesRelationPO::getEnterpriseId, reqDTO.getEnterpriseId())
                    .eq(StringUtils.isNotBlank(reqDTO.getDealerUserId()), SysDealerSalesRelationPO::getDealerUserId, reqDTO.getDealerUserId())
                    .list();
            if (CollectionUtils.isEmpty(list)) {
                return Collections.emptyList();
            }
            List<String> userIds = list.stream().map(SysDealerSalesRelationPO::getSalesUserId).distinct().collect(Collectors.toList());
            sysEnterpriseUserRespDTOList = userRelationManager.getUsersWithRoles(userIds);
        } else {
            list = sysDealerSalesRelationMapperService.lambdaQuery()
                    .eq(StringUtils.isNotBlank(reqDTO.getEnterpriseId()), SysDealerSalesRelationPO::getEnterpriseId, reqDTO.getEnterpriseId())
                    .eq(StringUtils.isNotBlank(reqDTO.getSalesUserId()), SysDealerSalesRelationPO::getSalesUserId, reqDTO.getSalesUserId())
                    .list();
            if (CollectionUtils.isEmpty(list)) {
                return Collections.emptyList();
            }
            List<String> userIds = list.stream().map(SysDealerSalesRelationPO::getDealerUserId).distinct().collect(Collectors.toList());
            sysEnterpriseUserRespDTOList = userRelationManager.getUsersWithRoles(userIds);
        }
        String enterpriseUserStr = JacksonUtil.bean2Json(sysEnterpriseUserRespDTOList);
        redisUtil.set(redisKey, enterpriseUserStr, UserConstant.USER_CACHE_TIME);
        return sysEnterpriseUserRespDTOList;
    }

    @Override
    public AssociatedUserEnterpriseRespDTO associatedEnterprise(AssociatedEnterpriseQueryDTO reqDTO) {
        SysUserPO sysUserPO = sysUserMapper.selectByUserId(reqDTO.getDealerUserId());
        if (sysUserPO.getStatus().equals(SysUserStatusEnum.WITHDRAW.getCode())) {
            throw new BizException(UserResultCode.USER_CANCELLING.getCode(), "用户已注销, 无法查看该用户详情");
        }
        PageExternalEnterpriseQueryDTO queryDTO = PageExternalEnterpriseQueryDTO.builder()
                .dealerUserId(reqDTO.getDealerUserId())
                .authStatus(reqDTO.getAuthStatus())
                .build();
        // 根据登录人权限过滤,只能看到自己有权限的客户
        EnterpriseQueryDTO enterpriseQueryDTO = dealerSalesQueryBiz.buildInternalEnterpriseQueryDTO(InternalEnterpriseQueryDTO.builder().loginUserId(reqDTO.getLoginUserId()).build());
        if (ObjectUtil.isNotEmpty(enterpriseQueryDTO.getEnterpriseIds())) {
            queryDTO.setEnterpriseIds(enterpriseQueryDTO.getEnterpriseIds());
        }
        List<ExternalEnterpriseRespDTO> externalEnterpriseRespDTOS = listExternalEnterprise(queryDTO);
        return AssociatedUserEnterpriseRespDTO.builder()
                .headImg(sysUserPO.getHeadImg())
                .mobile(sysUserPO.getMobile())
                .userName(sysUserPO.getUserName())
                .userId(sysUserPO.getUserId())
                .enterpriseRespDTOS(externalEnterpriseRespDTOS)
                .build();
    }

    @Override
    public PageResponse<ExternalEnterpriseRespDTO> pageExternalEnterprise(PageRequest<PageExternalEnterpriseQueryDTO> pageReqDTO) {
        PageExternalEnterpriseQueryDTO query = pageReqDTO.getQuery();
        IPage<ExternalEnterpriseRespDTO> externalEnterpriseRespDTOIPage = sysDealerSalesRelationMapper.pageExternalEnterprise(new Page<>(pageReqDTO.getIndex(), pageReqDTO.getSize()), query);
        List<ExternalEnterpriseRespDTO> externalEnterpriseRespDTOS = externalEnterpriseRespDTOIPage.getRecords();
        dealerSalesRelationFillBiz.fillExternalEnterpriseRespDTO(externalEnterpriseRespDTOS, query.getDealerUserId());
        return PageResponse.toResult(
                pageReqDTO,
                (int) externalEnterpriseRespDTOIPage.getTotal(),
                externalEnterpriseRespDTOS);
    }

    @Override
    public List<ExternalEnterpriseRespDTO> listExternalEnterprise(PageExternalEnterpriseQueryDTO reqDTO) {
//        List<ExternalEnterpriseRespDTO> response = dealerSalesRelationCache.getPage(reqDTO.getDealerUserId(), reqDTO);
//        if (ObjectUtil.isNotNull(response)) {
//            return response;
//        }
        long tid = Thread.currentThread().getId();
        LocalDateTime start = LocalDateTime.now();
        List<ExternalEnterpriseRespDTO> externalEnterpriseRespDTOS = sysDealerSalesRelationMapper.listExternalEnterprise(reqDTO);
        log.info("线程id: {}, 我绑定的公司耗时-数据库列表查询：{}毫秒, 项目: user, 环境: {}", tid, Duration.between(start, LocalDateTime.now()).toMillis(), active);

        if (ObjectUtil.isEmpty(externalEnterpriseRespDTOS)) {
            return Collections.emptyList();
        }
        dealerSalesRelationFillBiz.fillExternalEnterpriseRespDTO(externalEnterpriseRespDTOS, reqDTO.getDealerUserId());

        // 企业名称/税号模糊查询
        if (ObjectUtil.isNotEmpty(reqDTO.getQueryP())) {
            externalEnterpriseRespDTOS = externalEnterpriseRespDTOS.stream().filter(e -> e.getEnterpriseName().contains(reqDTO.getQueryP()) || e.getTaxNumber().contains(reqDTO.getQueryP())).collect(Collectors.toList());
        }
//        dealerSalesRelationCache.putPage(reqDTO.getDealerUserId(),reqDTO,externalEnterpriseRespDTOS);
        return externalEnterpriseRespDTOS;
    }

    @Override
    public PageResponse<SysDealerUserRespDTO> pageAssociatedUsers(PageRequest<EnterpriseAssociatedUserQueryDTO> pageReqDTO) {
        // 权限处理
        boolean auth = userAuthBiz.handleExternalAuth(pageReqDTO.getQuery());
        if (!auth) {
            return PageResponse.empty(pageReqDTO);
        }
        IPage<SysDealerUserRespDTO> ipage = sysDealerSalesRelationMapper.pageAssociatedUsers(new Page<>(pageReqDTO.getIndex(), pageReqDTO.getSize()), pageReqDTO.getQuery());
        List<SysDealerUserRespDTO> records = ipage.getRecords();
        sysUserAggregateBiz.getAssociatedUsersAggregate(records);
        if (ObjectUtil.isNotEmpty(records)) {
            for (SysDealerUserRespDTO e : records) {
                e.setDealerUserStatusStr(SysUserStatusEnum.getDescByCode(e.getDealerUserStatus()));
                e.setDealerUserTypeStr(SysUserTypeEnum.getDescByCode(e.getDealerUserType()));
                e.setAuthStatusStr(DealerSalesRelationAuthStatus.getDescByCode(e.getAuthStatus()));
            }
        }
        return PageResponse.toResult(
                pageReqDTO,
                (int) (ipage.getTotal()),
                records);
    }

    @Override
    public void authenticationFail(AuthenticationFailedDTO reqDTO) {
        // 更新认证状态
        sysDealerSalesRelationMapperService.updateAuthStatus(reqDTO.getDealerUserId(), reqDTO.getEnterpriseId(), DealerSalesRelationAuthStatus.FAILED, reqDTO.getUpdateBy(), reqDTO.getUpdateName());
        // 发送消息
        dealerSalesSendMsgBiz.sendAuthenticationFailMsg(reqDTO.getDealerUserId(), reqDTO.getEnterpriseId(), reqDTO.getUpdateBy(), reqDTO.getUpdateName());
        // 处理下角色
        userRoleUpdateManager.removeRoleForUnbind(reqDTO.getDealerUserId(), reqDTO.getEnterpriseId());
        // 完成在2种情况下该用户和公司的认证待办
        String bizNo = reqDTO.getDealerUserId() + "|" + reqDTO.getEnterpriseId();
        todoListUpdateStatusBiz.doneBatch(TodoBizCodeEnum.CO_BIND_EXTERNAL_BIND_NEW, Collections.singletonList(bizNo), reqDTO.getUpdateBy(), reqDTO.getUpdateName());
        todoListUpdateStatusBiz.doneBatch(TodoBizCodeEnum.CO_BIND_EXTERNAL_BIND_EXIST, Collections.singletonList(bizNo), reqDTO.getUpdateBy(), reqDTO.getUpdateName());
        todoListUpdateStatusBiz.doneBatch(TodoBizCodeEnum.CO_BIND_ALLOCATION_SALE_UN_AUTH, Collections.singletonList(bizNo), reqDTO.getUpdateBy(), reqDTO.getUpdateName());
        todoListUpdateStatusBiz.doneBatch(TodoBizCodeEnum.CO_BIND_AUTH_FAILED_RE_AUTH, Collections.singletonList(bizNo), reqDTO.getUpdateBy(), reqDTO.getUpdateName());
    }

    @Transactional
    @Override
    public AuthenticationResDTO authenticationSuccess(AuthenticationSuccessDTO reqDTO) {
        SysUserPO sysUserPO = sysUserMapper.selectByUserId(reqDTO.getDealerUserId());
        if (sysUserPO.getStatus().equals(SysUserStatusEnum.WITHDRAW.getCode())) {
            throw new BizException(UserResultCode.USER_CANCELLING.getCode(), "用户已注销, 无法认证成功");
        }
        SysUserRoleReqDTO sysUserRoleReqDTO = new SysUserRoleReqDTO();
        sysUserRoleReqDTO.setUserId(reqDTO.getDealerUserId());

        EnterpriseDTO enterprise = enterpriseFeign.getEnterpriseByEnterpriseId(reqDTO.getEnterpriseId());

        if(ObjectUtil.isEmpty(enterprise)){
            throw new BizException(UserResultCode.ENTERPRISE_NOT_EXIST.getCode(), UserResultCode.ENTERPRISE_NOT_EXIST.getMessage());
        }
        Result<List<EnterpriseBizRelationResDTO>> enterpriseIdBizResult=enterpriseFeign.getEnterpriseBizRelationByEnterpriseId(reqDTO.getEnterpriseId());
        if(!enterpriseIdBizResult.getSuccess()||CollUtil.isEmpty(enterpriseIdBizResult.getData())){
            throw new BizException(UserResultCode.ENTERPRISE_BIZ_NOT_EXIST.getCode(), UserResultCode.ENTERPRISE_BIZ_NOT_EXIST.getMessage());
        }
        EnterpriseBizRelationResDTO enterpriseBizRelationResDTO=enterpriseIdBizResult.getData().get(0);
        //工商业客户认证通过后给用户分配“管理&采购”角色
        if("BUSINESS".equals(enterprise.getCustomerCategory())){
            sysUserRoleReqDTO.setRoleIds(Arrays.asList(UserConstant.Management_Procurement_ROLE_ID));
        }else {
            sysUserRoleReqDTO.setRoleIds(reqDTO.getRoleIds());
        }
        sysUserRoleReqDTO.setUpdateBy(reqDTO.getUpdateBy());
        sysUserRoleReqDTO.setUpdateName(reqDTO.getUpdateName());
        roleService.updateUserRoles(sysUserRoleReqDTO);
        // 更新认证状态
        sysDealerSalesRelationMapperService.updateAuthStatus(reqDTO.getDealerUserId(), reqDTO.getEnterpriseId(), DealerSalesRelationAuthStatus.SUCCESS, reqDTO.getUpdateBy(), reqDTO.getUpdateName());
        // 发送消息
        dealerSalesSendMsgBiz.sendAuthenticationSuccessMsg(reqDTO.getDealerUserId(), reqDTO.getEnterpriseId(), reqDTO.getUpdateBy(), reqDTO.getUpdateName());
        // 完成在2种情况下该用户和公司的认证待办
        String bizNo = reqDTO.getDealerUserId() + "|" + reqDTO.getEnterpriseId();
        todoListUpdateStatusBiz.doneBatch(TodoBizCodeEnum.CO_BIND_EXTERNAL_BIND_NEW, Collections.singletonList(bizNo), reqDTO.getUpdateBy(), reqDTO.getUpdateName());
        todoListUpdateStatusBiz.doneBatch(TodoBizCodeEnum.CO_BIND_EXTERNAL_BIND_EXIST, Collections.singletonList(bizNo), reqDTO.getUpdateBy(), reqDTO.getUpdateName());
        todoListUpdateStatusBiz.doneBatch(TodoBizCodeEnum.CO_BIND_ALLOCATION_SALE_UN_AUTH, Collections.singletonList(bizNo), reqDTO.getUpdateBy(), reqDTO.getUpdateName());
        todoListUpdateStatusBiz.doneBatch(TodoBizCodeEnum.CO_BIND_AUTH_FAILED_RE_AUTH, Collections.singletonList(bizNo), reqDTO.getUpdateBy(), reqDTO.getUpdateName());

        boolean supplementCoInfo = enterpriseBizFeign.verifyCoInfo(reqDTO.getEnterpriseId()).getData();

        syncContact(reqDTO);

        sysUserPO.setFollowStatus(FollowStatusEnum.VALID.getCode());
        sysUserPO.setValidTime(LocalDateTime.now());
        sysUserMapper.updateById(sysUserPO);

        if(enterpriseBizRelationResDTO.getBizOrganizationCode().equals(sysUserPO.getOrganizationCode())){
            //如果人员的所属区域和绑定公司的所属区域一致，则需要走新的逻辑

//            if(enterprise.getMainSalesUserId()!=null&&sysUserPO.getExclusiveSale()==null){
                //如果该企业已经分配过主销售，则将该企业主销售更新为该用户的专属销售
//            sysUserPO.setExclusiveSale(enterprise.getMainSalesUserId());
//            sysUserPO.setAssignStatus(AssignStatusEnum.VALID.getCode());
//            sysUserPO.setAssignSaleTime(LocalDateTime.now());
//            sysUserMapper.updateById(sysUserPO);

//            }else if(enterprise.getMainSalesUserId()==null&&sysUserPO.getExclusiveSale()!=null){
//                //如果该用户有专属销售，且该企业没有主销售，则将用户的专属销售设置为该企业的主销售
//                SysUserPO saleUser=sysUserService.getUserByUserId(sysUserPO.getExclusiveSale());
//                AllocateMainSalesDTO allocateMainSalesDTO=new AllocateMainSalesDTO();
//                allocateMainSalesDTO.setEnterpriseId(enterprise.getEnterpriseId());
//                allocateMainSalesDTO.setMainSalesUserId(sysUserPO.getExclusiveSale());
//                allocateMainSalesDTO.setMainSalesUserName(saleUser.getUserName());
//                allocateMainSalesDTO.setUpdatedBy(reqDTO.getUpdateBy());
//                allocateMainSalesDTO.setUpdatedName(reqDTO.getUpdateName());
//                allocateMainSalesDTO.setDealerUserId(reqDTO.getDealerUserId());
//                enterpriseFeign.allocateMainSales(allocateMainSalesDTO);
//            }
        }
        return AuthenticationResDTO.builder().setSupplementCoInfo(supplementCoInfo);
    }

    private void syncContact(AuthenticationSuccessDTO reqDTO) {
        // 发送mq消息-同步联系人
        ContactorSyncMqDTO contactorSyncMqDTO = ContactorSyncMqDTO.builder()
                .setSyncType(ContactorSyncConstant.SYNC_IN_USER_ON_CO)
                .setCreatedBy(reqDTO.getUpdateBy())
                .setCreatedName(reqDTO.getUpdateName())
                .setEnterpriseId(reqDTO.getEnterpriseId())
                .setUserIds(Collections.singletonList(reqDTO.getDealerUserId()));

        String data = JSONUtil.toJsonStr(contactorSyncMqDTO);
        log.info("认证通过发送mq消息-同步联系人：{}", data);
        mqManager.sendTopic(PartnerConstant.CONTACTOR_SYNC, data);
    }

    @Override
    public List<SysDealerSalesRelationRespDTO> listByEnterpriseIdAndDealerUserId(String enterpriseId, String dealerUserId) {
        List<SysDealerSalesRelationPO> sysDealerSalesRelationPOS = sysDealerSalesRelationMapperService.listByDealerUserIdAndEnterpriseId(dealerUserId, enterpriseId);
        List<SysDealerSalesRelationRespDTO> sysDealerSalesRelationRespDTOS = BeanUtil.copyToList(sysDealerSalesRelationPOS, SysDealerSalesRelationRespDTO.class);
        return sysDealerSalesRelationRespDTOS;
    }

    @Override
    public List<SysDealerSalesRelationRespDTO> listByEnterpriseIdAndDealerUserIdBatch(List<SysSalesDealerRelationReqDTO> reqDTOList) {
        if (CollUtil.isEmpty(reqDTOList)) {
            return Collections.emptyList();
        }
        return sysDealerSalesRelationMapper.listByEnterpriseIdAndDealerUserIdBatch(reqDTOList);
    }

    @Override
    public PageResponse<SalesEnterpriseRespDTO> pageInternalEnterprise(PageRequest<InternalEnterpriseQueryDTO> pageReqDTO) {
        EnterpriseQueryDTO enterpriseQueryDTO = dealerSalesQueryBiz.buildInternalEnterpriseQueryDTO(pageReqDTO.getQuery());
        if (ObjectUtil.isEmpty(enterpriseQueryDTO.getEnterpriseIds())) {
            return PageResponse.empty(pageReqDTO);
        }
        return pageWithEnterpriseQuery(new PageRequest<>(pageReqDTO.getIndex(), pageReqDTO.getSize(), enterpriseQueryDTO), pageReqDTO.getQuery().getLoginUserId());
    }

    @Override
    public PageResponse<SalesEnterpriseRespDTO> userDetailPageInternalEnterprise(PageRequest<InternalEnterpriseQueryDTO> pageReqDTO) {
        // 用户详情查看关联企业只看该用户作为专属销售的企业
        List<SysDealerSalesRelationPO> sysDealerSalesRelationPOS = sysDealerSalesRelationMapperService.listBySalesUserId(pageReqDTO.getQuery().getLoginUserId());
        if (ObjectUtil.isEmpty(sysDealerSalesRelationPOS)) {
            return PageResponse.empty(pageReqDTO);
        }
        EnterpriseQueryDTO enterpriseQueryDTO = EnterpriseQueryDTO.builder()
                .customerCategory(pageReqDTO.getQuery().getCustomerCategory())
                .enterpriseIds(sysDealerSalesRelationPOS.stream().map(SysDealerSalesRelationPO::getEnterpriseId).distinct().collect(Collectors.toList()))
                .build();
        return pageWithEnterpriseQuery(new PageRequest<>(pageReqDTO.getIndex(), pageReqDTO.getSize(), enterpriseQueryDTO), pageReqDTO.getQuery().getLoginUserId());
    }

    private PageResponse<SalesEnterpriseRespDTO> pageWithEnterpriseQuery(PageRequest<EnterpriseQueryDTO> pageReqDTO, String loginUserId) {
        Result<PageResponse<EnterpriseDTO>> pageResponseResultDTO = enterpriseFeign.pageByQuery(new PageRequest<>(pageReqDTO.getIndex(), pageReqDTO.getSize(), pageReqDTO.getQuery()));
        if (!pageResponseResultDTO.getSuccess()) {
            throw new BizException(pageResponseResultDTO.getCode(), pageResponseResultDTO.getMessage());
        }
        PageResponse<EnterpriseDTO> pageResponse = pageResponseResultDTO.getData();
        List<EnterpriseDTO> enterpriseDTOS = pageResponse.getRecords();
        if (ObjectUtil.isEmpty(enterpriseDTOS)) {
            return PageResponse.empty(pageReqDTO);
        }
        List<SalesEnterpriseRespDTO> salesEnterpriseRespDTOS = dealerSalesConvertBiz.enterpriseDTO2salesEnterpriseRespDTO(enterpriseDTOS, loginUserId);
        return PageResponse.toResult(
                pageReqDTO,
                pageResponse.getTotal(),
                salesEnterpriseRespDTOS);
    }

    @Override
    public List<SalesEnterpriseRespDTO> listInternalEnterprise(InternalEnterpriseQueryDTO reqDTO) {
        long tid = Thread.currentThread().getId();
        LocalDateTime start = LocalDateTime.now();
        EnterpriseQueryDTO enterpriseQueryDTO = dealerSalesQueryBiz.buildInternalEnterpriseQueryDTO(reqDTO);
        log.info("线程id: {}, 我的客户耗时-权限{}毫秒, 项目: user, 环境: {}", tid, Duration.between(start, LocalDateTime.now()).toMillis(), active);
        start = LocalDateTime.now();
        if (ObjectUtil.isEmpty(enterpriseQueryDTO.getEnterpriseIds())) {
            return Collections.emptyList();
        }
        Result<List<EnterpriseDTO>> pageResponseResultDTO = enterpriseFeign.listByQuery(enterpriseQueryDTO);
        log.info("线程id: {}, 我的客户耗时-查询企业{}毫秒, 项目: user, 环境: {}", tid, Duration.between(start, LocalDateTime.now()).toMillis(), active);
        start = LocalDateTime.now();
        List<EnterpriseDTO> enterpriseDTOS = pageResponseResultDTO.getData();
        if (ObjectUtil.isEmpty(enterpriseDTOS)) {
            return Collections.emptyList();
        }
        List<SalesEnterpriseRespDTO> salesEnterpriseRespDTOS = dealerSalesConvertBiz.enterpriseDTO2salesEnterpriseRespDTO(enterpriseDTOS, reqDTO.getLoginUserId());
        log.info("线程id: {}, 我的客户耗时-企业DTO转换成resp{}毫秒, 项目: user, 环境: {}", tid, Duration.between(start, LocalDateTime.now()).toMillis(), active);
        start = LocalDateTime.now();
        if (ObjectUtil.isNotNull(reqDTO.getHasAuthStatusSuccess()) && reqDTO.getHasAuthStatusSuccess()) {
            // 只返回有认证成功用户的企业
            List<String> enterpriseIds = salesEnterpriseRespDTOS.stream().map(SalesEnterpriseRespDTO::getEnterpriseId).collect(Collectors.toList());
            Set<String> authStatusSuccessEnterpriseIdSet = sysDealerSalesRelationMapperService.listByEnterpriseIdsAndAuthStatus(enterpriseIds, DealerSalesRelationAuthStatus.SUCCESS.getCode()).stream().map(SysDealerSalesRelationPO::getEnterpriseId).collect(Collectors.toSet());
            salesEnterpriseRespDTOS = salesEnterpriseRespDTOS.stream().filter(e -> authStatusSuccessEnterpriseIdSet.contains(e.getEnterpriseId())).collect(Collectors.toList());
            log.info("线程id: {}, 我的客户耗时-查询认证成功{}毫秒, 项目: user, 环境: {}", tid, Duration.between(start, LocalDateTime.now()).toMillis(), active);
            start = LocalDateTime.now();
        }
        // 企业名称/税号模糊查询
        if (ObjectUtil.isNotEmpty(reqDTO.getQueryP())) {
            salesEnterpriseRespDTOS = salesEnterpriseRespDTOS.stream().filter(e -> e.getEnterpriseName().contains(reqDTO.getQueryP()) || e.getTaxNumber().contains(reqDTO.getQueryP())).collect(Collectors.toList());
        }

        return salesEnterpriseRespDTOS;
    }

    @Override
    public PageResponse<SysDealerRespDTO> pageMyDealers(PageRequest<MyDealerQueryDTO> pageReqDTO) {
        long tid = Thread.currentThread().getId();
        MyDealerQueryDTO query = pageReqDTO.getQuery();
        List<String> roleIds = sysUserRoleService.listRoleIdsByUserId(query.getSalesUserId());
        LocalDateTime start = LocalDateTime.now();
        if (roleIds.contains(UserConstant.SALES_AREA_MANAGER_ROLE_ID)) {
            // 1. 销售区域总能查看到自己所在战区的所有客户
            SysUserPO sysUserPO = sysUserMapper.selectByUserId(query.getSalesUserId());
            Result<List<EnterpriseBizRelationResDTO>> listResult = enterpriseBizRelationFeign.listByBizOrganizationCode(sysUserPO.getOrganizationCode());
            List<EnterpriseBizRelationResDTO> enterpriseBizRelationResDTOS = listResult.getData();
            if (ObjectUtil.isEmpty(enterpriseBizRelationResDTOS)) {
                return PageResponse.empty(pageReqDTO);
            }
            // 根据企业id去查询，不根据专属销售id去查询
            query.setEnterpriseIds(enterpriseBizRelationResDTOS.stream().map(EnterpriseBizRelationResDTO::getEnterpriseId).collect(Collectors.toList()));
            query.setSalesUserId(null);
            log.info("线程id: {}, 我的客户耗时-权限处理{}毫秒, 项目: user, 环境: {}", tid, Duration.between(start, LocalDateTime.now()).toMillis(), active);
            start = LocalDateTime.now();
        }
        IPage<SysDealerRespDTO> sysDealerRespDTOIPage = sysDealerSalesRelationMapper.pageMyDealers(new Page<>(pageReqDTO.getIndex(), pageReqDTO.getSize()), query);
        log.info("线程id: {}, 我的客户耗时-分页查询{}毫秒, 项目: user, 环境: {}", tid, Duration.between(start, LocalDateTime.now()).toMillis(), active);
        return PageResponse.toResult(
                pageReqDTO,
                (int) sysDealerRespDTOIPage.getTotal(),
                sysDealerRespDTOIPage.getRecords());
    }

    @Override
    public List<SysDealerSalesRespDTO> salesByEnterpriseIdAndDealerUserId(String enterpriseId, String dealerUserId) {
        List<SysDealerSalesRelationPO> sysDealerSalesRelationPOS = sysDealerSalesRelationMapperService.listByDealerUserIdAndEnterpriseId(dealerUserId, enterpriseId);
        List<String> salesUserIds = sysDealerSalesRelationPOS.stream().map(SysDealerSalesRelationPO::getSalesUserId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(salesUserIds)) {
            return Collections.emptyList();
        }
        List<SysUserPO> sysUserPOS = sysUserMapper.getUsersByUserIds(salesUserIds);
        Map<String, SysUserPO> userId2sysUserPO = sysUserPOS.stream().collect(Collectors.toMap(SysUserPO::getUserId, Function.identity()));
        return sysUserPOS.stream().map(userPO -> new SysDealerSalesRespDTO(userPO.getUserId(), userId2sysUserPO.get(userPO.getUserId()).getUserName(), userId2sysUserPO.get(userPO.getUserId()).getMobile())).collect(Collectors.toList());
    }

    @Override
    public void updateMainSalesToSales(UpdateMainSalesToSalesDTO updateMainSalesToSalesDTO) {
        String loginUserId = updateMainSalesToSalesDTO.getUpdatedBy();
        if (ObjectUtil.isNotEmpty(updateMainSalesToSalesDTO.getDealerUserId())) {
            SysUserPO sysUserPO = sysUserMapper.selectByUserId(updateMainSalesToSalesDTO.getDealerUserId());
            if (sysUserPO.getStatus().equals(SysUserStatusEnum.WITHDRAW.getCode())) {
                // 既然用户已经被注销了，那么就删除待办
                TodoListLogicDeleteReqDTO todoListLogicDeleteReqDTO = new TodoListLogicDeleteReqDTO(TodoBizCodeEnum.CO_BIND_EXTERNAL_BIND_NEW.getCode(),
                        updateMainSalesToSalesDTO.getDealerUserId() + "|" + updateMainSalesToSalesDTO.getEnterpriseId(), loginUserId);
                todoListFeign.logicDelete(Arrays.asList(todoListLogicDeleteReqDTO));
                throw new BizException(UserResultCode.USER_CANCELLING.getCode(), "用户已注销, 无法分配主销售");
            }
        }
        // 如果有用户id，那么替换该用户旧主销售作为专属销售的数据，如果没有用户id，则替换该企业下所有用户旧主销售作为专属销售的数据
        List<SysDealerSalesRelationPO> sysDealerSalesRelationPOS;
        if (ObjectUtil.isNotEmpty(updateMainSalesToSalesDTO.getDealerUserId())) {
            sysDealerSalesRelationPOS = sysDealerSalesRelationMapperService.listByDealerUserIdAndEnterpriseId(updateMainSalesToSalesDTO.getDealerUserId(), updateMainSalesToSalesDTO.getEnterpriseId());
        } else {
            sysDealerSalesRelationPOS = sysDealerSalesRelationMapperService.listByEnterpriseId(updateMainSalesToSalesDTO.getEnterpriseId());
        }

        List<SysDealerSalesRelationPO> deletedList = new ArrayList<>();
        List<SysDealerSalesRelationPO> addList = new ArrayList<>();
        Map<String, List<SysDealerSalesRelationPO>> uk2sysDealerSalesRelationPOS = sysDealerSalesRelationPOS.stream().collect(Collectors.groupingBy(e -> e.getDealerUserId() + "_" + e.getEnterpriseId()));
        for (List<SysDealerSalesRelationPO> items : uk2sysDealerSalesRelationPOS.values()) {
            // 情况1， 有就新主销售的id，那么直接干掉旧主销售的那条
            Optional<SysDealerSalesRelationPO> newSysDealerSalesRelationPOOptional = items.stream().filter(e -> updateMainSalesToSalesDTO.getMainSalesUserId().equals(e.getSalesUserId())).findAny();
            if (newSysDealerSalesRelationPOOptional.isPresent()) {
                Optional<SysDealerSalesRelationPO> oldSysDealerSalesRelationPOOptional = items.stream().filter(e -> updateMainSalesToSalesDTO.getOldMainSalesUserId().equals(e.getSalesUserId())).findAny();
                if (oldSysDealerSalesRelationPOOptional.isPresent()) {
                    deletedList.add(oldSysDealerSalesRelationPOOptional.get());
                }
            } else {
                // 情况2， 没有新主销售的id，那么删除旧主销售的那条，增加新主销售的这条
                Optional<SysDealerSalesRelationPO> oldSysDealerSalesRelationPOOptional = items.stream().filter(e -> updateMainSalesToSalesDTO.getOldMainSalesUserId().equals(e.getSalesUserId())).findAny();
                if (oldSysDealerSalesRelationPOOptional.isPresent()) {
                    deletedList.add(oldSysDealerSalesRelationPOOptional.get());
                    SysDealerSalesRelationPO newSysDealerSalesRelationPO = BeanUtil.copyProperties(oldSysDealerSalesRelationPOOptional.get(), SysDealerSalesRelationPO.class);
                    newSysDealerSalesRelationPO.setId(null);
                    // 替换为新主销售
                    newSysDealerSalesRelationPO.setSalesUserId(updateMainSalesToSalesDTO.getMainSalesUserId());
                    newSysDealerSalesRelationPO.setOriginSalesUserId(updateMainSalesToSalesDTO.getMainSalesUserId());
                    addList.add(newSysDealerSalesRelationPO);
                }
            }

        }
        if (ObjectUtil.isNotEmpty(deletedList)) {
            sysDealerSalesRelationMapperService.removeByIds(deletedList.stream().map(SysDealerSalesRelationPO::getId).collect(Collectors.toList()));
        }
        log.info("更新主销售为专属销售-企业id: {}, 用户id: {}, 新的主销售id: {}, 删除条数：{}", updateMainSalesToSalesDTO.getEnterpriseId(), updateMainSalesToSalesDTO.getDealerUserId(), updateMainSalesToSalesDTO.getMainSalesUserId(), deletedList.size());
        if (ObjectUtil.isNotEmpty(addList)) {
            sysDealerSalesRelationMapperService.saveBatch(addList);
        }
        log.info("更新主销售为专属销售-企业id: {}, 用户id: {}, 新的主销售id: {}, 增加条数：{}", updateMainSalesToSalesDTO.getEnterpriseId(), updateMainSalesToSalesDTO.getDealerUserId(), updateMainSalesToSalesDTO.getMainSalesUserId(), addList.size());
        // 处理专属销售应该发送的消息，应该取消的消息，以及应该删除的待办
        allocateExclusiveSalesBiz.handleMsgAndTodolist(deletedList, addList, loginUserId, updateMainSalesToSalesDTO.getUpdatedName());
        handleMainSalesToSalesMsgAndTodolist(updateMainSalesToSalesDTO);
    }

    /**
     * 主销售设置为专属销售应该完成的待办和发送的消息
     */
    private void handleMainSalesToSalesMsgAndTodolist(UpdateMainSalesToSalesDTO updateMainSalesToSalesDTO) {
        // 给主销售发送消息
        dealerSalesSendMsgBiz.sendMainSalesMsg(updateMainSalesToSalesDTO.getEnterpriseId(), updateMainSalesToSalesDTO.getMainSalesUserId(), updateMainSalesToSalesDTO.getUpdatedBy(), updateMainSalesToSalesDTO.getUpdatedName());
        // 完成该公司所有的分配主销售待办
        Result<List<TodoListResDTO>> result = todoListFeign.listTodoByBizCodeAndLikeBizNo(TodoBizCodeEnum.CO_BIND_EXTERNAL_BIND_NEW.getCode(), updateMainSalesToSalesDTO.getEnterpriseId());
        List<TodoListResDTO> todoListResDTOS = result.getData();
        if (ObjectUtil.isNotEmpty(todoListResDTOS)) {
            log.info("完成该公司所有的分配主销售待办-企业id: {}, 用户id: {}, 主销售id: {}, 完成的待办条数: {}", updateMainSalesToSalesDTO.getEnterpriseId(), updateMainSalesToSalesDTO.getDealerUserId(), updateMainSalesToSalesDTO.getMainSalesUserId(), todoListResDTOS.size());
            List<String> bizNos = todoListResDTOS.stream().map(TodoListResDTO::getBizNo).collect(Collectors.toList());
            todoListUpdateStatusBiz.doneBatch(TodoBizCodeEnum.CO_BIND_EXTERNAL_BIND_NEW, bizNos, updateMainSalesToSalesDTO.getUpdatedBy(), updateMainSalesToSalesDTO.getUpdatedName());
        }
    }

    @Override
    public PageResponse<AssociatedDealerUserRespDTO> pageMyAssociatedDealerUser(PageRequest<MyAssociatedDealerUserQueryDTO> pageReqDTO) {
        IPage<AssociatedDealerUserRespDTO> associatedDealerUserRespDTOIPage = sysDealerSalesRelationMapper.pageMyAssociatedDealerUser(new Page<>(pageReqDTO.getIndex(), pageReqDTO.getSize()), pageReqDTO.getQuery());
        List<AssociatedDealerUserRespDTO> records = associatedDealerUserRespDTOIPage.getRecords();
        if (ObjectUtil.isNotEmpty(records)) {
            for (AssociatedDealerUserRespDTO e : records) {
                e.setStatusStr(SysUserStatusEnum.getDescByCode(e.getStatus()));
            }
        }
        return PageResponse.toResult(
                pageReqDTO,
                (int) associatedDealerUserRespDTOIPage.getTotal(),
                records);
    }

    @Override
    public List<String> listEnterpriseIdBySalesIds(List<String> salesUserIds) {
        List<SysDealerSalesRelationPO> sysDealerSalesRelationPOS = sysDealerSalesRelationMapperService.listBySalesUserIds(salesUserIds);
        return sysDealerSalesRelationPOS.stream().map(SysDealerSalesRelationPO::getEnterpriseId).distinct().collect(Collectors.toList());
    }

    @Override
    public PageResponse<RequirementChangeExternalUserRespDTO> pageRequirementEnterpriseUser(PageRequest<RequirementChangeUserQueryDTO> pageReqDTO) {
        RequirementChangeUserQueryDTO query = pageReqDTO.getQuery();
        RequirementChangeEnterpriseUserQueryDTO requirementChangeEnterpriseUserQueryDTO = requirementPageChangeUserBiz.buildEnterpriseUserQuery(query);
        if (ObjectUtil.isEmpty(requirementChangeEnterpriseUserQueryDTO.getSalesUserIds()) && ObjectUtil.isEmpty(requirementChangeEnterpriseUserQueryDTO.getEnterpriseIds())) {
            return PageResponse.empty(pageReqDTO);
        }
        return requirementPageChangeUserBiz.pageRequirementEnterpriseUser(requirementChangeEnterpriseUserQueryDTO, pageReqDTO);
    }

    @Override
    public List<RequirementEnterpriseRespDTO> requirementEnterprises(RequirementEnterpriseQueryDTO reqDTO) {
        LambdaQueryWrapper<SysDealerSalesRelationPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotEmpty(reqDTO.getDealerUserId()), SysDealerSalesRelationPO::getDealerUserId, reqDTO.getDealerUserId())
                .eq(ObjectUtil.isNotEmpty(reqDTO.getSalesUserId()), SysDealerSalesRelationPO::getSalesUserId, reqDTO.getSalesUserId())
                .ne(SysDealerSalesRelationPO::getAuthStatus, DealerSalesRelationAuthStatus.FAILED.getCode())
                .orderByDesc(SysDealerSalesRelationPO::getCreatedTime);
        List<SysDealerSalesRelationPO> sysDealerSalesRelationPOS = sysDealerSalesRelationMapperService.list(queryWrapper);
        List<RequirementEnterpriseRespDTO> requirementEnterpriseRespDTOS = BeanUtil.copyToList(sysDealerSalesRelationPOS, RequirementEnterpriseRespDTO.class);
        // 根据enterpriseId去重
        Set<String> enterpriseIdSet = new HashSet<>();
        requirementEnterpriseRespDTOS = requirementEnterpriseRespDTOS.stream().filter(e -> {
            if (!enterpriseIdSet.contains(e.getEnterpriseId())) {
                enterpriseIdSet.add(e.getEnterpriseId());
                return true;
            } else {
                return false;
            }
        }).collect(Collectors.toList());
//        requirementEnterpriseRespDTOS = requirementEnterpriseRespDTOS.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RequirementEnterpriseRespDTO::getEnterpriseId))), ArrayList::new));
        if (ObjectUtil.isEmpty(requirementEnterpriseRespDTOS)) {
            return Collections.emptyList();
        }

        Map<String, EnterpriseDTO> enterpriseId2enterpriseDTO = new HashMap<>();
        List<String> dbEnterpriseIds = requirementEnterpriseRespDTOS.stream().map(RequirementEnterpriseRespDTO::getEnterpriseId).collect(Collectors.toList());
        Result<List<EnterpriseDTO>> enterpriseDTOResult = enterpriseFeign.listByEnterpriseIds(dbEnterpriseIds);
        List<EnterpriseDTO> enterpriseDTOS = enterpriseDTOResult.getData();
        if (!enterpriseDTOResult.getSuccess()) {
            throw new BizException(enterpriseDTOResult.getCode(), enterpriseDTOResult.getMessage());
        }
        if (ObjectUtil.isNotEmpty(enterpriseDTOS)) {
            enterpriseDTOS.forEach(e -> enterpriseId2enterpriseDTO.put(e.getEnterpriseId(), e));
        }
        OptionItemQueryReqDTO qryCondition = new OptionItemQueryReqDTO();
        qryCondition.setOptionGroup(OptionGroupConstant.OPTION_GROUP_REG_STATUS);
        List<OptionItemResDTO> optionItemList = optionFeign.qryOptionItemList(qryCondition).getData();
        List<EnterpriseAddressRegRespDTO> enterpriseAddressRegRespDTOS = enterpriseAddressFeign.listRegistByEnterpriseIds(dbEnterpriseIds);
        Map<String, String> enterpriseId2addressDetail = enterpriseAddressRegRespDTOS.stream().collect(Collectors.toMap(EnterpriseAddressRegRespDTO::getEnterpriseId, EnterpriseAddressRegRespDTO::getAddressDetail));
        List<RequirementEnterpriseRespDTO> result = new ArrayList<>();
        // 填充数据
        for (RequirementEnterpriseRespDTO e : requirementEnterpriseRespDTOS) {
            EnterpriseDTO enterpriseDTO = enterpriseId2enterpriseDTO.get(e.getEnterpriseId());
            // 只返回工商业类型的企业
            if (enterpriseDTO.getCustomerCategory().contains(EnterpriseTypeEnum.BUSINESS.getCode()) || enterpriseDTO.getCustomerCategory().contains(EnterpriseTypeEnum.PARTNER.getCode())) {
                e.setEnterpriseName(enterpriseDTO.getName());
                e.setLegalPersonName(enterpriseDTO.getLegalPersonName());
                e.setTaxNumber(enterpriseDTO.getTaxNumber());
                e.setRegLocation(enterpriseId2addressDetail.get(e.getEnterpriseId()));
                e.setRegStatus(enterpriseDTO.getRegStatus());
                e.setSubsisting(optionItemList.stream().anyMatch(e2 -> e2.getOptionValue().equals(e.getRegStatus())));
                e.setCustomerCategory(enterpriseDTO.getCustomerCategory());
                result.add(e);
            }
        }
        return result;


    }

    @Override
    public PageResponse<EnterpriseSalesUserRespDTO> pageEnterpriseSaleUser(PageRequest<EnterpriseSalesUserQueryDTO> pageReqDTO) {
        // 权限处理
        EnterpriseSalesUserQueryDTO reqDTO = pageReqDTO.getQuery();
        boolean auth = userAuthBiz.handleInternalAuth(reqDTO);
        if (!auth) {
            return PageResponse.empty(pageReqDTO);
        }
        List<SysDealerSalesRelationPO> sysDealerSalesRelationPOS = ObjectUtil.isNotEmpty(reqDTO.getUserIds()) ?
                sysDealerSalesRelationMapperService.listByDealerUserIdsAndEnterpriseId(reqDTO.getUserIds(), reqDTO.getEnterpriseId()) :
                sysDealerSalesRelationMapperService.listByEnterpriseId(reqDTO.getEnterpriseId());
        if (ObjectUtil.isEmpty(sysDealerSalesRelationPOS)) {
            return PageResponse.empty(pageReqDTO);
        }
        IPage<EnterpriseSalesUserRespDTO> ipage = sysUserMapper.pageEnterpriseSaleUser(new Page<>(pageReqDTO.getIndex(), pageReqDTO.getSize()), sysDealerSalesRelationPOS.stream().map(SysDealerSalesRelationPO::getSalesUserId).distinct().collect(Collectors.toList()));
        userFillBiz.fillEnterpriseSalesUserRespDTO(ipage.getRecords());
        return PageResponse.toResult(
                pageReqDTO,
                (int) ipage.getTotal(),
                ipage.getRecords());
    }

    @Override
    public List<String> dealerUserByEnterpriseIdAndAuthStatus(String enterpriseId, Integer authStatus) {
        return sysDealerSalesRelationMapper.dealerUserByEnterpriseIdAndAuthStatus(enterpriseId, authStatus);
    }

    @Transactional
    @Override
    public Result<String> enterpriseUserReboot(EnterpriseUserConfirmReqDTO req) {
        return enterpriseUserRebootBiz.enterpriseUserReboot(req);
    }

    @Override
    public void allocateExclusiveSales(AllocateExclusiveSalesDTO allocateExclusiveSalesDTO) {
        if (ObjectUtil.isEmpty(allocateExclusiveSalesDTO.getSalesUserIdList())) {
            throw new BizException(ResultCode.FAIL.getCode(), "请至少选择一个销售");
        }
        // 分配专属销售
        allocateExclusiveSalesBiz.allocate(allocateExclusiveSalesDTO);
    }

    @Override
    public Result<String> unBinding(EnterpriseRelUnBindReqDTO reqDTO) {
        return dealerSalesRelDelBiz.unBinding(reqDTO);
    }


    @Override
    public List<ContactorSyncResDTO> qyrBindInfoByCondition(CoBindInfReqDTO req) {
        return coBindRelQryBiz.qyrBindInfoByCondition(req);
    }

    @Override
    public Result<Void> modifyContactInfo(List<ModifyContactReqDTO> req) {
        return coBindRelModifyBiz.modifyContactInfo(req);
    }

    @Override
    public List<SysDealerRespDTO> listContactByEnterpriseId(ContactEnterpriseUserQueryDTO queryDTO) {
        String loginUserId = queryDTO.getLoginUserId();
        String enterpriseId = queryDTO.getEnterpriseId();
        // 如果是专属销售，那么就只返回作为专属销售
        List<SysDealerSalesRelationPO> sysDealerSalesRelationPOS = sysDealerSalesRelationMapperService.lambdaQuery()
                .eq(SysDealerSalesRelationPO::getEnterpriseId, enterpriseId)
                .eq(SysDealerSalesRelationPO::getSalesUserId, loginUserId)
                .ne(SysDealerSalesRelationPO::getAuthStatus, DealerSalesRelationAuthStatus.FAILED.getCode())
                .list();
        if (ObjectUtil.isEmpty(sysDealerSalesRelationPOS)) {
            // 如果通过销售id查不到数据，说明是非专属销售，则返回公司下全部认证通过的用户
            sysDealerSalesRelationPOS = sysDealerSalesRelationMapperService.lambdaQuery()
                    .eq(SysDealerSalesRelationPO::getEnterpriseId, enterpriseId)
                    .eq(SysDealerSalesRelationPO::getAuthStatus, DealerSalesRelationAuthStatus.SUCCESS.getCode())
                    .list();
        } else {
            // 只返回认证通过的
            sysDealerSalesRelationPOS = sysDealerSalesRelationPOS.stream().filter(e -> DealerSalesRelationAuthStatus.SUCCESS.getCode().equals(e.getAuthStatus())).collect(Collectors.toList());
        }
        if (ObjectUtil.isEmpty(sysDealerSalesRelationPOS)) {
            return Collections.emptyList();
        }

        // 开始转换成前端需要的数据对象
        List<String> dealerUserIds = sysDealerSalesRelationPOS.stream().map(SysDealerSalesRelationPO::getDealerUserId).distinct().collect(Collectors.toList());

        List<SysUserPO> sysUserPOS = sysUserMapper.getUsersByUserIds(dealerUserIds);
        return sysUserPOS.stream().map(e -> SysDealerRespDTO.builder()
                .dealerUserName(e.getUserName())
                .dealerUserId(e.getUserId())
                .dealerUserMobile(e.getMobile())
                .build()).collect(Collectors.toList());
    }

    @Override
    public List<SysDealerSalesRelationRespDTO> listByEnterpriseIdsAndAuthStatus(EnterpriseIdsAndAuthStatusQueryDTO queryDTO) {
        List<SysDealerSalesRelationPO> sysDealerSalesRelationPOS = sysDealerSalesRelationMapperService.listByEnterpriseIdsAndAuthStatus(queryDTO.getEnterpriseIds(), queryDTO.getAuthStatus());
        return BeanUtil.copyToList(sysDealerSalesRelationPOS, SysDealerSalesRelationRespDTO.class);
    }

    @Override
    public void updateEnterpriseCustomerCategory(String enterpriseId, String customerCategory) {
        sysDealerSalesRelationMapperService.lambdaUpdate()
                .eq(SysDealerSalesRelationPO::getEnterpriseId, enterpriseId)
                .set(SysDealerSalesRelationPO::getEnterpriseCustomerCategory, customerCategory)
                .update();
    }

    @Override
    public List<String> authPassedEnterpriseByDealer(String dealerUserId) {
        List<SysDealerSalesRelationPO> poList = sysDealerSalesRelationMapperService.lambdaQuery()
                .eq(SysDealerSalesRelationPO::getDealerUserId, dealerUserId)
                .eq(SysDealerSalesRelationPO::getAuthStatus, DealerSalesRelationAuthStatus.SUCCESS.getCode())
                .list();
        if (CollUtil.isEmpty(poList)) {
            return Collections.emptyList();
        } else {
            return poList.stream().map(SysDealerSalesRelationPO::getEnterpriseId).distinct().collect(Collectors.toList());
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("数据源连接工厂：{}", redisTemplate.getConnectionFactory());
    }

    @Override
    public Result<List<CoAttachBindResDTO>> qryCoBindAttach(CoAttachReqDTO req) {
        return coAttachQueryBiz.qryCoBindAttach(req);
    }

    @Override
    public List<SysDealerSalesRelationRespDTO> listSysDealerSalesRelationBySalesIds(List<String> salesUserIds) {
        List<SysDealerSalesRelationPO> sysDealerSalesRelationPOS = sysDealerSalesRelationMapperService.listBySalesUserIds(salesUserIds);
        return sysDealerSalesRelationPOS.stream().map(e->{
            SysDealerSalesRelationRespDTO sysDealerSalesRelationRespDTO=new SysDealerSalesRelationRespDTO();
            BeanUtils.copyProperties(e,sysDealerSalesRelationRespDTO);
            return sysDealerSalesRelationRespDTO;
        }).filter(e->e.getAuthStatus()==DealerSalesRelationAuthStatus.SUCCESS.getCode()).collect(Collectors.toList());
    }

    @Override
    public List<SalesEnterpriseRespDTO> quickOrderListInternalEnterprise(InternalEnterpriseQueryDTO reqDTO) {
        Result<QuickOrderAuthDTO> quickOrderAuthDTOResult=enterpriseBizFeign.canQuickOrder(reqDTO.getLoginUserId());
        if(quickOrderAuthDTOResult.getSuccess()){
            QuickOrderAuthDTO quickOrderAuthDTO=quickOrderAuthDTOResult.getData();
            if(quickOrderAuthDTO.getCanQuickOrder()&&CollectionUtils.isNotEmpty(quickOrderAuthDTO.getQuickOrderCompanyIdList())){
                EnterpriseQueryDTO enterpriseQueryDTO=new EnterpriseQueryDTO();
                enterpriseQueryDTO.setEnterpriseIds(quickOrderAuthDTO.getQuickOrderCompanyIdList().stream().map(e -> e.getEnterpriseId()).collect(Collectors.toList()));
                Result<List<EnterpriseDTO>> pageResponseResultDTO = enterpriseFeign.listByQuery(enterpriseQueryDTO);
                List<EnterpriseDTO> enterpriseDTOS = pageResponseResultDTO.getData();
                if (ObjectUtil.isEmpty(enterpriseDTOS)) {
                    return Collections.emptyList();
                }
                List<SalesEnterpriseRespDTO> salesEnterpriseRespDTOS = dealerSalesConvertBiz.enterpriseDTO2salesEnterpriseRespDTO(enterpriseDTOS, reqDTO.getLoginUserId());

                Map<String,Boolean> enterpriseIdMap = quickOrderAuthDTO.getQuickOrderCompanyIdList().stream().collect(Collectors.toMap(QuickOrderEnterpriseDTO::getEnterpriseId, e -> e.getIsManager(), (v1, v2) -> v1));
                salesEnterpriseRespDTOS.forEach(e->{
                    e.setIsManager(enterpriseIdMap.get(e.getEnterpriseId()));
                });
                return salesEnterpriseRespDTOS;
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<ExternalEnterpriseRespDTO> quickOrderListExternalEnterprise(PageExternalEnterpriseQueryDTO reqDTO) {
        Result<QuickOrderAuthDTO> quickOrderAuthDTOResult=enterpriseBizFeign.canQuickOrder(reqDTO.getDealerUserId());
        if(quickOrderAuthDTOResult.getSuccess()){
            QuickOrderAuthDTO quickOrderAuthDTO=quickOrderAuthDTOResult.getData();
            if(quickOrderAuthDTO.getCanQuickOrder()&&CollectionUtils.isNotEmpty(quickOrderAuthDTO.getQuickOrderCompanyIdList())){
                List<ExternalEnterpriseRespDTO> externalEnterpriseRespDTOS =quickOrderAuthDTO.getQuickOrderCompanyIdList().stream()
                        .map(item->{
                            ExternalEnterpriseRespDTO externalEnterpriseRespDTO=new ExternalEnterpriseRespDTO();
                            externalEnterpriseRespDTO.setEnterpriseId(item.getEnterpriseId());
                            externalEnterpriseRespDTO.setIsManager(item.getIsManager());
                            externalEnterpriseRespDTO.setDealerUserId(reqDTO.getDealerUserId());
                            return externalEnterpriseRespDTO;
                        }).collect(Collectors.toList());
                dealerSalesRelationFillBiz.fillExternalEnterpriseRespDTO(externalEnterpriseRespDTOS, reqDTO.getDealerUserId());
                return externalEnterpriseRespDTOS;
            }
        }
        return Collections.emptyList();
    }

    @Override
    public Result<List<CoAttachSalesResDTO>> qryCoSaleAttach(CoAttachReqDTO req) {
        return coAttachQueryBiz.qryCoSaleAttach(req);
    }
}