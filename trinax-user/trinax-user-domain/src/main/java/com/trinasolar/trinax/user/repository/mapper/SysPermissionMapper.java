package com.trinasolar.trinax.user.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trinasolar.trinax.user.repository.po.SysPermissionPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <pre>
 * Title: 系统功能权限dao
 *
 * Description:
 * <pre>
 * <AUTHOR>
 **/
@Mapper
public interface SysPermissionMapper extends BaseMapper<SysPermissionPO> {

    List<SysPermissionPO> getPermissionByUserId(String userId);
}
