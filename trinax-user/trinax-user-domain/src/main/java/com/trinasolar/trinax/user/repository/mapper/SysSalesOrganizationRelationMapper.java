package com.trinasolar.trinax.user.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trinasolar.trinax.user.dto.input.query.SearchConditionReqDTO;
import com.trinasolar.trinax.user.dto.output.IntentSalesUserRespDTO;
import com.trinasolar.trinax.user.dto.output.SalesOrganizationOrganizationRespDTO;
import com.trinasolar.trinax.user.repository.dao.SalesOrganizationDTO;
import com.trinasolar.trinax.user.repository.po.SysSalesOrganizationRelationPO;
import com.trinasolar.trinax.user.repository.po.SysUserPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【sys_sales_organization_relation(销售在战区汇报对象)】的数据库操作Mapper
 * @createDate 2023-11-16 14:59:38
 * @Entity com.trinasolar.trinax.user.repository.SysSalesOrganizationRelation
 */
@Mapper
public interface SysSalesOrganizationRelationMapper extends BaseMapper<SysSalesOrganizationRelationPO> {

    List<IntentSalesUserRespDTO> getIntentInfoByBizOrganizationCode(@Param("salesUserId") String salesUserId, @Param("bizOrganizationCode") String bizOrganizationCode);

    List<SalesOrganizationOrganizationRespDTO> listSalesOrganizationOperationRespDTO(@Param("salesUserId") String salesUserId);

    List<SalesOrganizationDTO> qryOrgInfoBySalesIds(@Param("salesUserIds") List<String> userIds);

    List<SysUserPO> qryUserByOrgCode(@Param("query") SearchConditionReqDTO req);
}




