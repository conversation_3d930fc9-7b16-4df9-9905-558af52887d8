package com.trinasolar.trinax.user.repository.mapper;

import java.util.Date;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trinasolar.trinax.user.repository.po.SysRolePermissionPO;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <pre>
 * Title: 系统功能权限dao
 *
 * Description:
 * <pre>
 * <AUTHOR>
 **/
@Mapper
public interface SysRolePermissionMapper extends BaseMapper<SysRolePermissionPO> {
	
	default int deleteByRoleId(String roleId) {
		if(StrUtil.isBlank(roleId)) {
			return 0;
		}
		
        QueryWrapper<SysRolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        
        SysRolePermissionPO updPO = new SysRolePermissionPO();
        updPO.setIsDeleted(1);
        updPO.setUpdateTime(DateUtil.toLocalDateTime(new Date()));
        return this.update(updPO, queryWrapper);
    }
	
	default SysRolePermissionPO seleteByRolePermissionId(String roleId, String permissionId) {
        QueryWrapper<SysRolePermissionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        queryWrapper.eq("permission_id", permissionId);
        return this.selectOne(queryWrapper);
    }
	
}
