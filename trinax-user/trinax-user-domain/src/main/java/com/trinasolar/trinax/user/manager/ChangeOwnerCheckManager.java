package com.trinasolar.trinax.user.manager;

import com.trinasolar.trinax.basic.api.ChangeOwnerFeign;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeOwnerCheckManager {

    private final ChangeOwnerFeign changeOwnerFeign;

    public void checkRunningTask(String newUserId, String originalUserId) {

        Result<Void> result = changeOwnerFeign.validTaskUserExist(originalUserId, newUserId);

        if (Boolean.FALSE == result.getSuccess()) {
            throw new BizException(ResultCode.FAIL.getCode(), result.getMessage());
        }
    }
}
