package com.trinasolar.trinax.user.service.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.user.api.SysOrganizationFeign;
import com.trinasolar.trinax.user.constants.*;
import com.trinasolar.trinax.user.dto.output.*;
import com.trinasolar.trinax.user.manager.UserRelationManager;
import com.trinasolar.trinax.user.repository.atomicservice.SysOrganizationMapperService;
import com.trinasolar.trinax.user.repository.atomicservice.SysUserMapperService;
import com.trinasolar.trinax.user.repository.mapper.SysUserMapper;
import com.trinasolar.trinax.user.repository.po.SysOrganizationPO;
import com.trinasolar.trinax.user.repository.po.SysUserPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <pre>
 * Title: 内部用户列表数据组装
 *
 * Description:
 * <pre>
 * <AUTHOR>
 **/
@Service
@Slf4j
public class SysUserAggregateBiz {

    @Resource
    private SysUserMapper userMapper;
    @Autowired
    private SysUserMapperService sysUserMapperService;

    @Autowired
    private UserRelationManager userRelationManager;
    @Autowired
    private SysOrganizationMapperService sysOrganizationMapperService;

    @Autowired
    private BindEnterpriseBiz bindEnterpriseBiz;

    @Autowired
    SysOrganizationFeign sysOrganizationFeign;

    public Integer convertAssignManager(Integer assignStatus) {
        switch (assignStatus){
            case 0:
                return 0;
            case 1:
            case 2:
                return 1;
            default:
                return null;
        }
    }

    public Integer convertAssignSale(Integer assignStatus) {
        switch (assignStatus){
            case 0:
            case 1:
                return 0;
            case 2:
                return 1;
            default:
                return null;
        }
    }


    public List<SysExternalUserRespDTO> getExternalUserPageAggregate(List<SysUserPO> userPos) {
        List<SysExternalUserRespDTO> users = new ArrayList<>();
        Map<String, Map<String,String>> unionEnterprise = this.getBindEnterprise(userPos);
        List<String> userIds = userPos.stream().map(SysUserPO::getUserId).collect(Collectors.toList());
        List<SysEnterpriseUserRespDTO> usersWithRoles = userRelationManager.getUsersWithRoles(userIds);
        Map<String, String> bindRoleForDisplay = this.getBindRoleForDisplay(usersWithRoles);
        List<String> orgnizationCodes=new ArrayList<>(userPos.stream().map(SysUserPO::getOrganizationCode).collect(Collectors.toSet()));
        Map<String, String> orgnizationMap = this.getOrgnizationNameMap(orgnizationCodes);

        userPos.forEach(user -> {
            SysExternalUserRespDTO respVO = BeanUtil.copyProperties(user, SysExternalUserRespDTO.class);
            respVO.setStatusText(SysUserStatusEnum.getDescByCode(user.getStatus()));
            Map<String,String> unionNameMap=unionEnterprise.getOrDefault(user.getUserId(), new HashMap<>());

            respVO.setBindEnterprise(unionNameMap.getOrDefault(UserConstant.UNION_ENTERPRISE_NAME, ""));
            respVO.setEtpOrganizationName(unionNameMap.getOrDefault(UserConstant.UNION_ENTERPRISE_ORG,""));
            respVO.setOrganizationName(orgnizationMap.getOrDefault(user.getOrganizationCode(),""));
            respVO.setRolesText(bindRoleForDisplay.getOrDefault(user.getUserId(), ""));
            respVO.setFollowStatusText(user.getFollowStatus()!=null?FollowStatusEnum.valueOfCode(user.getFollowStatus()).getDescription():"");

            respVO.setIsPurchasingIntentionText(user.getIsPurchasingIntention()!=null?IsPurchasingIntentionEnum.valueOfCode(user.getIsPurchasingIntention()).getDescription():"");
            respVO.setIsAssignManager(user.getAssignStatus()!=null? convertAssignManager(user.getAssignStatus()):null);
            respVO.setIsAssignSale(user.getAssignStatus()!=null? convertAssignSale(user.getAssignStatus()):null);
            respVO.setSaleFollowStatusText(user.getSaleFollowStatus()!=null?SaleFollowStatusEnum.valueOfCode(user.getSaleFollowStatus()).getDescription():"");
//            BindEnterpriseEnum bindEnterpriseEnum=convertIsBindEnterprise(user);
//            dto.setIsBindEnterprise(bindEnterpriseEnum.getCode());
            respVO.setIsBindEnterpriseText(BindEnterpriseEnum.valueOfCode(respVO.getIsBindEnterprise()).getDescription());
            respVO.setIsFollowed(convertIsFollowed(user));
            respVO.setIsSaleFollowed(convertIsSaleFollowed(user));
            if(ObjectUtil.isNotEmpty(user.getProductClass())){
                String[] productClass = user.getProductClass().split(",");
                respVO.setProductClassText(Arrays.stream(productClass).map(ProductClassEnum::valueOfCode).map(ProductClassEnum::getDesc).collect(Collectors.joining(",")));
            }

            if(ObjectUtil.isNotEmpty(user.getCustomerBranch())){
                String[] customerBranch = user.getCustomerBranch().split(",");
                respVO.setCustomerBranchText(Arrays.stream(customerBranch).map(CustomerBranchEnum::valueOfCode).map(CustomerBranchEnum::getDesc).collect(Collectors.joining(",")));
            }

            if(ObjectUtil.isNotEmpty(user.getFollowFeedback())){
                String[] followFeedback = user.getFollowFeedback().split(",");
                respVO.setFollowFeedbackText(Arrays.stream(followFeedback).map(FollowFeedbackEnum::valueOfCode).map(FollowFeedbackEnum::getDescription).collect(Collectors.joining(",")));
            }
            if(ObjectUtil.isNotEmpty(user.getSaleFollowFeedback())){
                String[] followFeedback = user.getSaleFollowFeedback().split(",");
                respVO.setSaleFollowFeedbackText(Arrays.stream(followFeedback).map(FollowFeedbackEnum::valueOfCode).map(FollowFeedbackEnum::getDescription).collect(Collectors.joining(",")));
            }

            users.add(respVO);
        });

        return users;
    }

    public Integer convertIsSaleFollowed(SysUserPO user) {
        if(FollowStatusEnum.VALID.getCode().equals(user.getSaleFollowStatus())){
            return 1;
        }
        return 0;
    }

    public Integer convertIsFollowed(SysUserPO user) {
        if(FollowStatusEnum.INVALID.getCode().equals(user.getFollowStatus())||
                (AssignStatusEnum.FOLLOWING.getCode().equals(user.getAssignStatus()))
                ||AssignStatusEnum.VALID.getCode().equals(user.getAssignStatus())){
            return 1;
        }
        return 0;
    }

    private Map<String, String> getOrgnizationNameMap(List<String> orgnizationCodes) {
        Result<List<SysOrganizationRespDTO>>  result=sysOrganizationFeign.listOrgByOrganizationCodes(orgnizationCodes);
        if(result.getSuccess()&& CollUtil.isNotEmpty(result.getData())){
            return result.getData().stream().collect(Collectors.toMap(SysOrganizationRespDTO::getOrganizationCode,SysOrganizationRespDTO::getOrganizationName));
        }
        return new HashMap<>();
    }

    public List<SysInternalUserRespDTO> getInternalUserPageAggregate(List<SysUserPO> userPos) {
        List<SysInternalUserRespDTO> users = new ArrayList<>();
        List<String> userIds = userPos.stream().map(SysUserPO::getUserId).collect(Collectors.toList());
        //获取展示的角色
        List<SysEnterpriseUserRespDTO> usersWithRoles = userRelationManager.getUsersWithRoles(userIds);
        ;
        Map<String, String> bindRoleForDisplay = this.getBindRoleForDisplay(usersWithRoles);
        //获取展示的所属组织
        List<String> orgCodes = userPos.stream().map(SysUserPO::getOrganizationCode).collect(Collectors.toList());
        LambdaQueryWrapper<SysOrganizationPO> orgWrapper = new LambdaQueryWrapper<>();
        orgWrapper.in(SysOrganizationPO::getOrganizationCode, orgCodes);
        List<SysOrganizationPO> orgPos = sysOrganizationMapperService.list(orgWrapper);
        Map<String, String> orgNameMapping = new HashMap<>();
        orgPos.forEach(org -> {
            orgNameMapping.put(org.getOrganizationCode(), org.getOrganizationName());
        });
        //获取展示的上级
        List<String> parentUserIds = userPos.stream().map(SysUserPO::getParentUserId).collect(Collectors.toList());
        LambdaQueryWrapper<SysUserPO> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.in(SysUserPO::getUserId, parentUserIds);
        List<SysUserPO> parentUserPos = sysUserMapperService.list(userWrapper);
        Map<String, String> parentNameMapping = new HashMap<>();
        parentUserPos.forEach(superior -> {
            parentNameMapping.put(superior.getUserId(), superior.getUserName());
        });

        userPos.forEach(user -> {
            SysInternalUserRespDTO respVO = BeanUtil.copyProperties(user, SysInternalUserRespDTO.class);
            respVO.setStatusText(SysUserStatusEnum.getDescByCode(user.getStatus()));
            respVO.setRolesText(bindRoleForDisplay.getOrDefault(user.getUserId(), ""));
            String organizationCode = user.getOrganizationCode();
            respVO.setOrganizationText(orgNameMapping.getOrDefault(organizationCode, ""));
            String parentUserId = user.getParentUserId();
            respVO.setSuperiorText(parentNameMapping.getOrDefault(parentUserId, ""));
            users.add(respVO);
        });

        return users;
    }

    /**
     * @param usersWithRoles
     * @return 组装为要展现的格式:角色1,角色2
     */
    @NotNull
    private static Map<String, String> getBindRoleForDisplay(List<SysEnterpriseUserRespDTO> usersWithRoles) {
        Map<String, String> userRolesMapping = new HashMap<>();
        usersWithRoles.forEach(user -> {
            String display = user.getRoles().stream().filter(role -> CharSequenceUtil.isNotBlank(role.getRoleName()))
                    .map(role -> role.getRoleName())
                    .collect(Collectors.joining(", "));
            userRolesMapping.put(user.getUserId(), display);
        });

        return userRolesMapping;
    }


    public Map<String, Map<String,String>> getBindEnterprise(List<SysUserPO> userPos) {
        return bindEnterpriseBiz.getBindEnterprise(userPos);
    }


    public void testCreateExternalUser() {
        List<SysUserPO> list = Lists.newArrayList();

        for (int i = 1; i <= 1000; i++) {
            String mobile = "188" + RandomUtil.randomString("0123456789", 8);
            SysUserPO po = new SysUserPO();
            po.setCreatedTime(LocalDateTime.now());
            po.setUpdatedTime(po.getCreatedTime());
            po.setCreatedBy("压测初始化用户数据");
            po.setCreatedName("压测初始化用户数据");
            po.setUpdatedName("压测初始化用户数据");
            po.setUpdatedName("压测初始化用户数据");
            po.setStatus(SysUserStatusEnum.ENABLE.getCode());
            po.setUserType(SysUserTypeEnum.EXTERNAL.getType());
            po.setUserName("压测初始化用户" + i);
            po.setRegistrationTime(po.getCreatedTime());
            po.setMobile(mobile);
            po.setUserEmail(mobile + "@qq.com");
            po.setUserCode(mobile);
            po.setUserId("Test-" + mobile);
            list.add(po);
        }
        sysUserMapperService.saveBatch(list);

    }

    public void getAssociatedUsersAggregate(List<SysDealerUserRespDTO> records) {
        List<String> userIds=records.stream().map(SysDealerUserRespDTO::getDealerUserId).collect(Collectors.toList());
        List<SysEnterpriseUserRespDTO> usersWithRoles = userRelationManager.getUsersWithRoles(userIds);
        Map<String, String> bindRoleForDisplay = this.getBindRoleForDisplay(usersWithRoles);
        records.forEach(e->{
            e.setRoleText(bindRoleForDisplay.getOrDefault(e.getDealerUserId(),""));
        });
    }
}
