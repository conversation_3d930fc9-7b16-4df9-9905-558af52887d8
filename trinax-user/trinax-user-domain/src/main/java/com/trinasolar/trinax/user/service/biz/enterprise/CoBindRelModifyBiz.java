package com.trinasolar.trinax.user.service.biz.enterprise;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.user.constants.DealerSalesRelationAuthStatus;
import com.trinasolar.trinax.user.dto.input.relation.ModifyContactReqDTO;
import com.trinasolar.trinax.user.repository.atomicservice.SysDealerSalesRelationMapperService;
import com.trinasolar.trinax.user.repository.po.SysDealerSalesRelationPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Slf4j
public class CoBindRelModifyBiz {
    @Autowired
    SysDealerSalesRelationMapperService sysDealerSalesRelationMapperService;

    @Transactional
    public Result<Void> modifyContactInfo(List<ModifyContactReqDTO> req) {
        req.forEach(a -> {
            sysDealerSalesRelationMapperService.lambdaUpdate()
                    .set(SysDealerSalesRelationPO::getDealerSfId, a.getSfContactId())
                    .eq(SysDealerSalesRelationPO::getEnterpriseId, a.getEnterpriseId())
                    .eq(SysDealerSalesRelationPO::getDealerUserId, a.getContactId())
                    .eq(SysDealerSalesRelationPO::getAuthStatus, DealerSalesRelationAuthStatus.SUCCESS.getCode())
                    .update();
        });
        return Result.ok();
    }
}
