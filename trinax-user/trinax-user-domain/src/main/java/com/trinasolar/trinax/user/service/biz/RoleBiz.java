package com.trinasolar.trinax.user.service.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.lang.tree.parser.NodeParser;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.user.constants.DeleteFlagEnum;
import com.trinasolar.trinax.user.constants.UserResultCode;
import com.trinasolar.trinax.user.dto.input.RoleReqDTO;
import com.trinasolar.trinax.user.dto.input.SysPermissionsReqDTO;
import com.trinasolar.trinax.user.dto.input.SysRolePermissionReqDTO;
import com.trinasolar.trinax.user.dto.input.SysUserRoleReqDTO;
import com.trinasolar.trinax.user.dto.output.SysPermRespDTO;
import com.trinasolar.trinax.user.dto.output.SysPermTreeRespDTO;
import com.trinasolar.trinax.user.manager.UserRoleUpdateManager;
import com.trinasolar.trinax.user.repository.atomicservice.RoleMapperService;
import com.trinasolar.trinax.user.repository.atomicservice.SysUserRoleMapperService;
import com.trinasolar.trinax.user.repository.mapper.*;
import com.trinasolar.trinax.user.repository.po.*;
import dtt.segment.id.generator.service.IdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
@Service
@Slf4j
public class RoleBiz {

    @Autowired
    private IdService idService;

    @Autowired
    private SysRoleMapper roleMapper;


    @Autowired
    private RoleMapperService roleMapperService;

    @Resource
    private SysUserMapper userMapper;

    @Resource
    private SysRolePermissionMapper sysRolePermissionMapper;

    @Resource
    private SysUserRoleMapper sysUserRoleMapper;

    @Resource
    private SysUserRoleMapperService sysUserRoleMapperService;

    @Resource
    private SysPermissionMapper sysPermissionMapper;

    @Resource
    private UserRoleUpdateManager userRoleUpdateManager;


    private static final String UN_DELETE = "0";

    public Result<String> createOrUpdate(RoleReqDTO roleReqDTO) {
        return doCreateOrUpdate(roleReqDTO);
    }


    private Result<String> doCreateOrUpdate(RoleReqDTO roleReqDTO) {
        //if role id is not blank, update role information
        log.info("创建role入参 {}", roleReqDTO);
        LocalDateTime createTime = LocalDateTime.now();
        RolePO rolePO;
        if (StringUtils.isNotBlank(roleReqDTO.getRoleId())) {
            rolePO = roleMapper.selectOne(new LambdaQueryWrapper<RolePO>().eq(RolePO::getRoleId, roleReqDTO.getRoleId()).eq(RolePO::getIsDeleted, 0));
            rolePO.setRoleName(roleReqDTO.getRoleName());
            if (existedRoleName(roleReqDTO)) return Result.fail("当前角色名称已存在");
            rolePO.setRoleDesc(roleReqDTO.getRoleDesc() == null ? rolePO.getRoleDesc() : roleReqDTO.getRoleDesc());
            rolePO.setRoleLabel(roleReqDTO.getRoleLabel() == null ? rolePO.getRoleLabel() : roleReqDTO.getRoleLabel());
            rolePO.setRoleStatus(roleReqDTO.getRoleStatus() == null ? rolePO.getRoleStatus() : roleReqDTO.getRoleStatus());
        } else {
            if (existedRoleName(roleReqDTO)) return Result.fail("当前角色名称已存在");
            String id = String.format("%06d", idService.nextId("role", ""));
            String roleId = "R" + id;
            rolePO = new RolePO();
            rolePO.setRoleId(roleId);
            rolePO.setRoleName(roleReqDTO.getRoleName());
            rolePO.setRoleStatus(roleReqDTO.getRoleStatus());
            rolePO.setRoleDesc(roleReqDTO.getRoleDesc());
            rolePO.setRoleLabel(roleReqDTO.getRoleLabel());
            rolePO.setCreatedBy(roleReqDTO.getUserId());
            rolePO.setCreatedName(roleReqDTO.getUserName());
            rolePO.setCreateTime(createTime);
        }
        rolePO.setSpecialType(roleReqDTO.getSpecialType());
        rolePO.setUpdatedBy(roleReqDTO.getUserId());
        rolePO.setUpdatedName(roleReqDTO.getUserName());
        rolePO.setUpdateTime(createTime);
        if (roleMapperService.saveOrUpdate(rolePO)) {
            return Result.ok(rolePO.getRoleId());
        }
        return Result.ok("0");
    }

    /**
     * 判断当前角色名称是否存在
     *
     * @param req 请求
     * @return 判断当前角色名称是否存在
     */
    private boolean existedRoleName(RoleReqDTO req) {
        Integer count = roleMapperService.lambdaQuery()
                .eq(RolePO::getRoleLabel, req.getRoleLabel())
                .eq(RolePO::getRoleName, req.getRoleName())
                .eq(RolePO::getIsDeleted, UN_DELETE)
                .ne(RolePO::getRoleId, req.getRoleId() == null ? "" : req.getRoleId())
                .count();
        log.info("existedRoleName count {}", count);
        return count != 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean delete(List<String> roleIds, String userId, String userName) {
        List<SysUserRolePO> sysUserRolePOS = sysUserRoleMapperService.listByRoleIds(roleIds);
        Map<String, List<SysUserRolePO>> roleId2sysUserRoles = sysUserRolePOS.stream().collect(Collectors.groupingBy(SysUserRolePO::getRoleId));
        List<RolePO> rolePOS = roleMapperService.listByRoleIds(roleIds);
        rolePOS.forEach(rolePO -> {
            List<SysUserRolePO> sysUserRolePOS1 = roleId2sysUserRoles.get(rolePO.getRoleId());
            if (ObjectUtil.isNotEmpty(sysUserRolePOS1)) {
                throw new BizException(ResultCode.FAIL.getCode(), String.format("角色 %s 中存在关联的用户，无法删除", rolePO.getRoleName()));
            }
            rolePO.setIsDeleted(Integer.valueOf(DeleteFlagEnum.DELETED.getCode()));
            rolePO.setUpdatedBy(userId);
            rolePO.setUpdatedName(userName);
            rolePO.setUpdateTime(LocalDateTime.now());
        });
        return roleMapperService.updateBatchById(rolePOS);
    }

    public PageResponse<RolePO> getRolePage(PageRequest<RoleReqDTO> request) {
        Page<RolePO> page = new Page<>(request.getIndex(), request.getSize());
        LambdaQueryWrapper<RolePO> queryWrapper = new LambdaQueryWrapper<RolePO>();
        if (StringUtils.isNotBlank(request.getQuery().getRoleId())) {
            queryWrapper.eq(RolePO::getRoleId, request.getQuery().getRoleId());
        }
        if (StringUtils.isNotBlank(request.getQuery().getRoleName())) {
            queryWrapper.like(RolePO::getRoleName, request.getQuery().getRoleName());
        }
        if (StringUtils.isNotBlank(request.getQuery().getRoleLabel())) {
            queryWrapper.eq(RolePO::getRoleLabel, request.getQuery().getRoleLabel());
        }
        if (StringUtils.isNotBlank(request.getQuery().getRoleDesc())) {
            queryWrapper.eq(RolePO::getRoleDesc, request.getQuery().getRoleDesc());
        }
        if (request.getQuery().getRoleStatus() != null) {
            queryWrapper.eq(RolePO::getRoleStatus, request.getQuery().getRoleStatus());
        }
        Page<RolePO> rolePOPage = roleMapper.selectPage(page, queryWrapper.eq(RolePO::getIsDeleted, 0).orderByDesc(RolePO::getCreateTime));
        return PageResponse.toResult(
                request.getIndex(),
                request.getSize(),
                (int) rolePOPage.getTotal(),
                rolePOPage.getRecords());
    }

    public List<RolePO> getRoleList(String roleId, String roleName, String roleLabel, Integer roleStatus, String roleDesc) {
        LambdaQueryWrapper<RolePO> queryWrapper = new LambdaQueryWrapper<RolePO>();
        if (StringUtils.isNotBlank(roleId)) {
            queryWrapper.eq(RolePO::getRoleId, roleId);
        }
        if (StringUtils.isNotBlank(roleName)) {
            queryWrapper.like(RolePO::getRoleName, roleName);
        }
        if (StringUtils.isNotBlank(roleLabel)) {
            queryWrapper.eq(RolePO::getRoleLabel, roleLabel);
        }
        if (roleStatus != null) {
            queryWrapper.eq(RolePO::getRoleStatus, roleStatus);
        }
        if (StringUtils.isNotBlank(roleDesc)) {
            queryWrapper.like(RolePO::getRoleDesc, roleDesc);
        }
        return roleMapper.selectList(queryWrapper.eq(RolePO::getIsDeleted, 0).orderByDesc(RolePO::getCreateTime));
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRolePermissions(SysRolePermissionReqDTO reqVO) {
        String roleId = reqVO.getRoleId();
        //检查roleId
        if (selectByRoleId(roleId) == null) {
            throw new BizException(UserResultCode.ROLE_NOT_EXIST.getCode(), UserResultCode.ROLE_NOT_EXIST.getMessage());
        }

        //删除
        sysRolePermissionMapper.deleteByRoleId(roleId);

        if (CollUtil.isNotEmpty(reqVO.getPermissionIds())) {
            LocalDateTime nowDate = DateUtil.toLocalDateTime(new Date());
            for (String perm : reqVO.getPermissionIds()) {
                SysRolePermissionPO po = sysRolePermissionMapper.seleteByRolePermissionId(roleId, perm);
                if (po == null) {
                    //新增
                    po = new SysRolePermissionPO();
                    po.setRoleId(roleId);
                    po.setPermissionId(perm);
                    po.setIsDeleted(0);
                    po.setCreatedBy(reqVO.getUpdateBy());
                    po.setCreatedName(reqVO.getUpdateName());
                    po.setCreateTime(nowDate);
                    po.setUpdatedBy(reqVO.getUpdateBy());
                    po.setUpdatedName(reqVO.getUpdateName());
                    po.setUpdateTime(nowDate);
                    sysRolePermissionMapper.insert(po);
                } else {
                    //更新
                    po.setIsDeleted(0);
                    po.setUpdatedBy(reqVO.getUpdateBy());
                    po.setUpdatedName(reqVO.getUpdateName());
                    po.setUpdateTime(nowDate);
                    sysRolePermissionMapper.updateById(po);
                }
            }
        }
    }

    public void updateUserRoles(SysUserRoleReqDTO reqVO) {
        String userId = reqVO.getUserId();
        //检查userId
        if (userMapper.selectByUserId(userId) == null) {
            throw new BizException(UserResultCode.USER_NOT_EXIST.getCode(), UserResultCode.USER_NOT_EXIST.getMessage());
        }

        //删除
        userRoleUpdateManager.deleteByUserIdWithoutBase(userId);

        if (CollUtil.isNotEmpty(reqVO.getRoleIds())) {
            LocalDateTime nowDate = DateUtil.toLocalDateTime(new Date());
            for (String role : reqVO.getRoleIds()) {
                SysUserRolePO po = sysUserRoleMapper.seleteByUserRole(userId, role);
                if (po == null) {
                    //新增
                    po = new SysUserRolePO();
                    po.setRoleId(role);
                    po.setUserId(userId);
                    po.setIsDeleted(0);
                    po.setCreatedBy(reqVO.getUpdateBy());
                    po.setCreatedName(reqVO.getUpdateName());
                    po.setCreateTime(nowDate);
                    po.setUpdatedBy(reqVO.getUpdateBy());
                    po.setUpdatedName(reqVO.getUpdateName());
                    po.setUpdateTime(nowDate);
                    sysUserRoleMapper.insert(po);
                } else {
                    //更新
                    po.setIsDeleted(0);
                    po.setUpdatedBy(reqVO.getUpdateBy());
                    po.setUpdatedName(reqVO.getUpdateName());
                    po.setUpdateTime(nowDate);
                    sysUserRoleMapper.updateById(po);
                }
            }
        }
    }

    public List<SysPermTreeRespDTO> getPermTree(SysPermissionsReqDTO reqVO) {
        QueryWrapper<SysPermissionPO> queryWrapper = new QueryWrapper<SysPermissionPO>();
        if (StrUtil.isNotBlank(reqVO.getPermissionApplication())) {
            queryWrapper.eq("permission_application", reqVO.getPermissionApplication());
        }
        if (StrUtil.isNotBlank(reqVO.getRoleId())) {
            queryWrapper.inSql("permission_id",
                    "select srp.permission_id from sys_role_permission srp where srp.is_deleted=0 and srp.role_id='" + reqVO.getRoleId() + "'");
        }
        if (StrUtil.isNotBlank(reqVO.getUserId())) {
            queryWrapper.inSql("permission_id",
                    "select srp.permission_id from sys_user_role sur, sys_role_permission srp " +
                            "where sur.is_deleted=0 and srp.is_deleted=0 and sur.role_id=srp.role_id and sur.user_id='" + reqVO.getUserId() + "'");
        }
        List<SysPermissionPO> perms = sysPermissionMapper.selectList(queryWrapper);

        String topParentId = "0";
        if (StrUtil.isNotBlank(reqVO.getTopParentId())) {
            topParentId = reqVO.getTopParentId();
        }
        List<Tree<String>> treeList = TreeUtil.build(perms, topParentId, new SysPermNodeParser());

        return setTreeDTO(treeList);
    }

    private RolePO selectByRoleId(String roleId) {
        if (StrUtil.isBlank(roleId)) {
            return null;
        }

        QueryWrapper<RolePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        return roleMapper.selectOne(queryWrapper);
    }

    private List<SysPermTreeRespDTO> setTreeDTO(List<Tree<String>> treeList) {
        List<SysPermTreeRespDTO> dtolist = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(treeList)) {
            for (Tree<String> t : treeList) {
                SysPermTreeRespDTO dto = JSONUtil.toBean(t.getName().toString(), SysPermTreeRespDTO.class);
                if (CollUtil.isNotEmpty(t.getChildren())) {
                    dto.setChildren(setTreeDTO(t.getChildren()));
                }
                dtolist.add(dto);
            }
        }
        return dtolist;
    }

    static class SysPermNodeParser implements NodeParser<SysPermissionPO, String> {
        @Override
        public void parse(SysPermissionPO obj, Tree<String> tree) {
            if (tree != null) {
                tree.setId(obj.getPermissionId());
                tree.setParentId(obj.getParentId());
                tree.setWeight(obj.getSeq());
                tree.setName(JSONUtil.toJsonStr(obj));
            }
        }
    }

    public List<SysPermRespDTO> getRolePerms(Collection<String> roleIds) {
        List<SysPermRespDTO> result = new ArrayList<>();
        List<SysRolePermissionPO> sysRolePermissionPOList = sysRolePermissionMapper.
                selectList(new LambdaQueryWrapper<SysRolePermissionPO>().
                        in(SysRolePermissionPO::getRoleId, roleIds).eq(SysRolePermissionPO::getIsDeleted, 0));
        if (sysRolePermissionPOList.isEmpty()) {
            return result;
        }
        List<String> permIdList = sysRolePermissionPOList.stream().map(SysRolePermissionPO::getPermissionId).collect(Collectors.toList());

        QueryWrapper<SysPermissionPO> wrapper = new QueryWrapper<>();
        wrapper.in("permission_id", permIdList);
        List<SysPermissionPO> sysPermissionPos = sysPermissionMapper.selectList(wrapper);
        sysPermissionPos.forEach(permission -> {
            SysPermRespDTO sysPermRespDTO = new SysPermRespDTO();
            BeanUtils.copyProperties(permission, sysPermRespDTO);
            result.add(sysPermRespDTO);
        });
        return result;
    }
}
