package com.trinasolar.trinax.user.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.trinasolar.trinax.user.dto.input.ExternalUserGiftReceiveReqDTO;
import com.trinasolar.trinax.user.dto.output.ExternalUserGiftReceiveResDTO;
import com.trinasolar.trinax.user.repository.po.ExternalUserGiftReceivePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 外部用户礼品认领登记表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Mapper
public interface ExternalUserGiftReceiveMapper extends BaseMapper<ExternalUserGiftReceivePO> {

    IPage<ExternalUserGiftReceiveResDTO> getPage(IPage<?> page, @Param("req") ExternalUserGiftReceiveReqDTO query);

    List<ExternalUserGiftReceiveResDTO> getList(@Param("req") ExternalUserGiftReceiveReqDTO query);
}
