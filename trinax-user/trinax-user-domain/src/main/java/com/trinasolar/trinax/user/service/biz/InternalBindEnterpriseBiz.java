package com.trinasolar.trinax.user.service.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.partner.api.EnterpriseBizFeign;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.constants.PartnerConstant;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseTypeEnum;
import com.trinasolar.trinax.partner.dto.mq.ContactorSyncMqDTO;
import com.trinasolar.trinax.partner.dto.output.AllocateMainSalesDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseDTO;
import com.trinasolar.trinax.user.constants.CoBindMessageSituationEnum;
import com.trinasolar.trinax.user.constants.UserConstant;
import com.trinasolar.trinax.user.dto.input.SysUserRoleReqDTO;
import com.trinasolar.trinax.user.dto.input.relation.EnterpriseBindReqDTO;
import com.trinasolar.trinax.user.dto.mq.EnterpriseBindMqDTO;
import com.trinasolar.trinax.user.dto.output.authentication.AuthenticationResDTO;
import com.trinasolar.trinax.user.manager.EnterpriseRemoteManager;
import com.trinasolar.trinax.user.manager.UserRoleUpdateManager;
import com.trinasolar.trinax.user.repository.atomicservice.SysDealerSalesRelationMapperService;
import com.trinasolar.trinax.user.repository.mapper.SysUserMapper;
import com.trinasolar.trinax.user.repository.po.SysDealerSalesRelationPO;
import com.trinasolar.trinax.user.repository.po.SysUserPO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;

@Service
@Slf4j
public class InternalBindEnterpriseBiz {

    @Autowired
    private MqManager mqManager;

    @Autowired
    private ExecutorService executorService;

    @Autowired
    private EnterpriseFeign enterpriseFeign;

    @Autowired
    private SysDealerSalesRelationMapperService sysDealerSalesRelationMapperService;

    @Autowired
    private EnterpriseRemoteManager enterpriseRemoteManager;

    @Autowired
    private UserRoleUpdateManager userRoleUpdateManager;

    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    EnterpriseBizFeign enterpriseBizFeign;


    /**
     * 内部用户帮外部用户绑定企业：
     * 如果主销售存在，则将当前用户绑定为专属销售；
     * 如果主销售不存在，则将当前用户绑定为主销售 & 专属销售；
     *
     * @param reqDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<AuthenticationResDTO> bindEnterprise(EnterpriseBindReqDTO reqDTO) {
        //代绑企业时，校验当前人员战区是否与企业战区一致
//        verifyBizOrgCode(reqDTO.getCurrentUserId(),reqDTO.getEnterpriseId());

        EnterpriseDTO enterpriseDTO = enterpriseRemoteManager.findByEnterpriseId(reqDTO.getEnterpriseId());
        String mainSaleUserId = enterpriseDTO.getMainSalesUserId();
        String enterpriseId = reqDTO.getEnterpriseId();
        if (StringUtils.isEmpty(mainSaleUserId)) {
            log.info("主销售不存在时() 需要设置主销售");
            //设置企业主销售信息
            updateMainSale(enterpriseId, reqDTO);
        }
        //新增企业，销售，经销商 关联关系
        boolean isSaved = saleDealerRelation(enterpriseId, reqDTO);

        //更新角色信息
        updateRole(reqDTO);

        //给外部用户通知消息
        executorService.execute(() -> sendMessage(reqDTO));
        boolean supplementCoInfo = false;
        String customerCategory = enterpriseDTO.getCustomerCategory();
        if (StringUtils.isNotBlank(customerCategory) && customerCategory.contains(EnterpriseTypeEnum.BUSINESS.getCode())) {
            supplementCoInfo = enterpriseBizFeign.verifyCoInfo(reqDTO.getEnterpriseId()).getData();
        }
        List<String> userIds = new ArrayList<>();
        userIds.add(reqDTO.getExternalUserId());
        ContactorSyncMqDTO mqDTO = ContactorSyncMqDTO.builder().setEnterpriseId(reqDTO.getEnterpriseId()).setUserIds(userIds).setSyncType(2);
        mqManager.sendTopic(PartnerConstant.CONTACTOR_SYNC, JacksonUtil.bean2Json(mqDTO));
        log.info("内部用户代绑企业end");
        return Result.ok(AuthenticationResDTO.builder().setSupplementCoInfo(supplementCoInfo).setEnterpriseId(enterpriseId));
    }

    /**
     * 校验当前人员战区是否与企业战区一致
     *
     * @param currentUserId
     * @param enterpriseId
     */
    private void verifyBizOrgCode(String currentUserId, String enterpriseId) {
        SysUserPO sysUserPO = sysUserMapper.selectByUserId(currentUserId);
        String bizOrgCode = enterpriseRemoteManager.findBizOrgCodeByEnterpriseId(enterpriseId);
        if (!sysUserPO.getOrganizationCode().equals(bizOrgCode)) {
            throw new BizException(ResultCode.FAIL.getCode(), "您只能代客户绑定您服务区域内的企业，请引导客户自行完成绑定");
        }
    }

    /**
     * 更新主销售信息
     *
     * @param enterpriseId
     * @param reqDTO
     */
    private void updateMainSale(String enterpriseId, EnterpriseBindReqDTO reqDTO) {
        //设置企业主销售信息
        AllocateMainSalesDTO allocateMainSalesDTO = new AllocateMainSalesDTO();
        allocateMainSalesDTO.setEnterpriseId(enterpriseId);
        allocateMainSalesDTO.setMainSalesUserId(reqDTO.getCurrentUserId());
        allocateMainSalesDTO.setMainSalesUserName(reqDTO.getCurrentUserName());
        allocateMainSalesDTO.setUpdatedBy(reqDTO.getCurrentUserId());
        allocateMainSalesDTO.setUpdatedName(reqDTO.getCurrentUserName());
        Result<Void> allocateResult = enterpriseFeign.updateMainSales(allocateMainSalesDTO);
        if (Boolean.FALSE.equals(allocateResult.getSuccess())) {
            throw new BizException(ResultCode.FAIL.getCode(), "绑定主销售信息失败");
        }
    }

    /**
     * 保存企业，经销商用户，销售管理信息
     *
     * @param enterpriseId
     * @param reqDTO
     * @return
     */
    private boolean saleDealerRelation(String enterpriseId, EnterpriseBindReqDTO reqDTO) {
        EnterpriseDTO enterpriseDTO = enterpriseFeign.getEnterpriseByEnterpriseId(enterpriseId);

        //新增企业，销售，经销商 关联关系
        SysDealerSalesRelationPO relationPO = new SysDealerSalesRelationPO();
        relationPO.setEnterpriseId(enterpriseDTO.getEnterpriseId());
        relationPO.setEnterpriseCustomerCategory(enterpriseDTO.getCustomerCategory());
        relationPO.setCreatedBy(reqDTO.getCurrentUserId());
        relationPO.setCreatedName(reqDTO.getCurrentUserName());
        relationPO.setDealerUserId(reqDTO.getExternalUserId());
        relationPO.setOriginDealerUserId(reqDTO.getExternalUserId());
        relationPO.setCreatedTime(LocalDateTime.now());
        relationPO.setEnabled(1);
        relationPO.setSalesUserId(reqDTO.getCurrentUserId());
        relationPO.setOriginSalesUserId(reqDTO.getCurrentUserId());
        relationPO.setAuthStatus(1);//待绑默认认证通过
        relationPO.setUpdatedBy(reqDTO.getCurrentUserId());
        relationPO.setUpdatedName(reqDTO.getCurrentUserName());
        relationPO.setUpdatedTime(LocalDateTime.now());

        LambdaUpdateWrapper<SysDealerSalesRelationPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysDealerSalesRelationPO::getDealerUserId, reqDTO.getExternalUserId());
        updateWrapper.eq(SysDealerSalesRelationPO::getEnterpriseId, reqDTO.getEnterpriseId());
        updateWrapper.eq(SysDealerSalesRelationPO::getSalesUserId, reqDTO.getCurrentUserId());
        return sysDealerSalesRelationMapperService.saveOrUpdate(relationPO, updateWrapper);
    }

    /**
     * 内部用户代绑公司时，需要给外部用户发系统通知
     *
     * @param reqDTO
     */
    private void sendMessage(EnterpriseBindReqDTO reqDTO) {
        log.info("sendMessage 内部用户代绑公司时，需要给外部用户发系统通知发消息");
        EnterpriseBindMqDTO messageMqDTO = EnterpriseBindMqDTO
                .builder()
                .setExternalUserId(reqDTO.getExternalUserId())
                .setEnterpriseId(reqDTO.getEnterpriseId())
                .setEnterpriseName(reqDTO.getEnterpriseName())
                .setCurrentUserId(reqDTO.getCurrentUserId())
                .setCurrentUserName(reqDTO.getCurrentUserName())
                .setSituationType(CoBindMessageSituationEnum.CO_BIND_INTERNAL_BIND.getCode());
        mqManager.sendTopic(UserConstant.CO_BIND_TOPIC, JSONUtil.toJsonStr(messageMqDTO), true);
    }


    /**
     * 更新角色信息
     *
     * @param reqDTO
     */
    private void updateRole(EnterpriseBindReqDTO reqDTO) {
        if (CollUtil.isEmpty(reqDTO.getRoleIdList())) {
            return;
        }
        SysUserRoleReqDTO roleReqDTO = new SysUserRoleReqDTO();
        roleReqDTO.setRoleIds(reqDTO.getRoleIdList());
        roleReqDTO.setUserId(reqDTO.getExternalUserId());
        roleReqDTO.setUpdateBy(reqDTO.getCurrentUserId());
        roleReqDTO.setUpdateName(reqDTO.getCurrentUserName());
        userRoleUpdateManager.updateUserRoles(roleReqDTO);
    }
}
