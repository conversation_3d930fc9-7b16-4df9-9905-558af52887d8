package com.trinasolar.trinax.user.service.biz;

import cn.hutool.core.util.RandomUtil;
import com.trinasolar.trinax.basic.api.SmsSendFeign;
import com.trinasolar.trinax.basic.dto.input.TrinaSolarSmsReqDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import com.trinasolar.trinax.user.dto.input.SendSmsReqDTO;
import com.trinasolar.trinax.user.dto.input.VerifySmsReqDTO;
import com.trinasolar.trinax.user.manager.UserSyncContactManager;
import com.trinasolar.trinax.user.repository.atomicservice.SysUserMapperService;
import com.trinasolar.trinax.user.repository.mapper.SysUserMapper;
import com.trinasolar.trinax.user.repository.po.SysUserPO;
import com.trinasolar.trinax.user.utils.MaskUtil;
import com.trinasolar.trinax.user.utils.constants.RedisKey;
import dtt.cache.redisclient.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;


/**
 * <pre>
 * Title: 修改外部用户手机号发送消息通知biz
 *
 * Description:
 * <pre>
 * <AUTHOR>
 **/
@Service
@Slf4j
public class ModifyPhoneSmsBiz {

    @Value("${trina.phone.modify.sms.templateCode}")
    private String modifyTemplateCode;

    @Value("${trina.phone.modify.notice.originTemplateCode}")
    private String noticeOriginTemplate;

    @Value("${trina.phone.modify.notice.newTemplateCode}")
    private String noticeNewTemplate;

    @Autowired
    private SmsSendFeign smsSendFeign;

    @Autowired
    private SysUserMapperService sysUserMapperService;

    @Autowired
    private ExecutorService executorService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private UserSyncContactManager userSyncContactManager;

    //验证码缓存key
    private static final String CACHE_CODE = "code";
    //验证码有效期缓存key
    private static final String CACHE_EXPIRE_IN = "expireIn";


    /**
     * 修改手机时，获取原手机验证码
     * @param reqDTO
     * @return
     */
    public Result<String> sendModifySmsCode(SendSmsReqDTO reqDTO) {
        log.info("外部用户修改手机，获取原手机号验证码入参：{}",reqDTO);
        //如果缓存存在，说明上次验证码还未过1分钟
        boolean hasKey = redisUtil.hasKey(RedisKey.SEND_MODIFY_SMS_CODE_FREQUENCY + reqDTO.getPhoneNumber());
        if(hasKey){
            throw new BizException(ResultCode.FAIL.getCode(), "获取原手机验证码太频繁，请稍后再试！");
        }
        //发送短信
        String randomCode = RandomUtil.randomString("0123456789", 6);
        log.info("外部用户修改手机，获取原手机号验证码,验证码为：{}",randomCode);
        boolean bool = sendSms(modifyTemplateCode,reqDTO,randomCode);

        //发送短信成功，缓存验证码
        if(bool){
            setCache(RedisKey.SEND_MODIFY_SMS_CODE_FREQUENCY + reqDTO.getPhoneNumber(),
                    RedisKey.SEND_MODIFY_SMS_CODE_EFFECTIVE + reqDTO.getPhoneNumber(),randomCode);
            return Result.ok("原手机短信验证码发送成功");
        }else{
            return Result.fail("原手机短信验证码发送失败");
        }
    }

    /**
     * 验证原手机号短信
     * @param reqDTO
     * @return
     */
    public Result<String> verifyModifySmsCode(VerifySmsReqDTO reqDTO){
        String cacheKey = RedisKey.SEND_MODIFY_SMS_CODE_EFFECTIVE + reqDTO.getPhoneNumber();
        //校验验证码
        verifySmsCode(cacheKey,reqDTO.getVerifyCode());
        return Result.ok("验证码验证成功");
    }


    /**
     * 发送确认手机号短信
     * @param reqDTO
     * @return
     */
    public Result<String> sendConfirmSmsCode(SendSmsReqDTO reqDTO) {
        //如果缓存存在，说明上次验证码还未过1分钟
        boolean hasKey = redisUtil.hasKey(RedisKey.SEND_CONFIRM_SMS_CODE_FREQUENCY + reqDTO.getPhoneNumber());
        if(hasKey){
            return Result.fail("获取新手机验证码太频繁，请稍后再试");
        }

        SysUserPO sysUserPO = sysUserMapper.selectByMobile(reqDTO.getPhoneNumber(),SysUserTypeEnum.EXTERNAL.getType());
        if(ObjectUtils.isNotEmpty(sysUserPO)){
            return Result.fail("手机号已被绑定");
        }

        //生成6位随机验证码
        String randomCode = RandomUtil.randomString("0123456789", 6);
        boolean bool = sendSms(modifyTemplateCode,reqDTO,randomCode);
        //发送短信成功，缓存验证码
        if(bool){
            setCache(RedisKey.SEND_CONFIRM_SMS_CODE_FREQUENCY + reqDTO.getPhoneNumber(),
                    RedisKey.SEND_CONFIRM_SMS_CODE_EFFECTIVE + reqDTO.getPhoneNumber(),randomCode);
            return Result.ok("短信验证码发送成功");
        }else{
            return Result.fail("短信验证码发送失败");
        }
    }

    /**
     * 验证码不存在，提示：验证码失效
     * 验证码不匹配：提示验证码错误
     * @param reqDTO
     */
    public Result<String> verifyConfirmSmsCode(VerifySmsReqDTO reqDTO) {
        String cacheKey = RedisKey.SEND_CONFIRM_SMS_CODE_EFFECTIVE + reqDTO.getNewPhoneNumber();
        verifySmsCode(cacheKey,reqDTO.getVerifyCode());
        //验证成功，更新手机号码
        boolean bool = sysUserMapperService.lambdaUpdate()
                .eq(SysUserPO::getMobile,reqDTO.getPhoneNumber())
                .eq(SysUserPO::getUserType, SysUserTypeEnum.EXTERNAL.getType())
                .set(SysUserPO::getMobile,reqDTO.getNewPhoneNumber())
                .update();
        if(bool){
            executorService.execute(()->sendNotice(reqDTO));
            //发送mq消息-同步联系人
            String userId = sysUserMapperService.lambdaQuery()
                    .eq(SysUserPO::getMobile, reqDTO.getNewPhoneNumber())
                    .eq(SysUserPO::getUserType, SysUserTypeEnum.EXTERNAL.getType())
                    .one().getUserId();
            userSyncContactManager.syncContact(userId, reqDTO.getCreateBy(), reqDTO.getCreateName());
            return  Result.ok("手机号码修改成功");
        }else{
            return  Result.fail("手机号码修改失败，请联系管理员");
        }

    }


    /**
     * 修改手机号码后，需要给老手机号与新手机号发送通知短信
     * @param reqDTO
     */
    public void sendNotice(VerifySmsReqDTO reqDTO){
        //旧手机号通知短信
        TrinaSolarSmsReqDTO smsReqDTO = new TrinaSolarSmsReqDTO();
        smsReqDTO.setPhoneNumber(reqDTO.getPhoneNumber());
        smsReqDTO.setTemplateCode(noticeOriginTemplate);
        smsReqDTO.setCreateBy(reqDTO.getCreateBy());
        smsReqDTO.setCreateName(reqDTO.getCreateName());
        smsReqDTO.setUpdateBy(reqDTO.getUpdateBy());
        smsReqDTO.setUpdateName(reqDTO.getUpdateName());
        Map<String ,String> templateParam = new HashMap<>();
        templateParam.put("phonenumber", MaskUtil.maskPhoneNumber(reqDTO.getPhoneNumber()));
        smsReqDTO.setTemplateParam(templateParam);
        smsSendFeign.trinaSolarSend(smsReqDTO);

        //新手机号通知短信
        smsReqDTO.setTemplateCode(noticeNewTemplate);
        smsReqDTO.setPhoneNumber(reqDTO.getNewPhoneNumber());
        templateParam.put("phonenumber",MaskUtil.maskPhoneNumber(reqDTO.getNewPhoneNumber()));
        smsSendFeign.trinaSolarSend(smsReqDTO);
    }


    /**
     * 发送短信
     * @param templateCode
     * @param reqDTO
     * @param randomCode
     * @return
     */
    private boolean sendSms(String templateCode,SendSmsReqDTO reqDTO,String randomCode){
        TrinaSolarSmsReqDTO smsReqDTO = new TrinaSolarSmsReqDTO();
        smsReqDTO.setPhoneNumber(reqDTO.getPhoneNumber());
        smsReqDTO.setTemplateCode(templateCode);
        smsReqDTO.setCreateBy(reqDTO.getCreateBy());
        smsReqDTO.setCreateName(reqDTO.getCreateName());
        smsReqDTO.setUpdateBy(reqDTO.getUpdateBy());
        smsReqDTO.setUpdateName(reqDTO.getUpdateName());

        Map<String ,String> templateParam = new HashMap<>();
        templateParam.put("code",randomCode);
        smsReqDTO.setTemplateParam(templateParam);

        Result<Boolean> result = smsSendFeign.trinaSolarSend(smsReqDTO);
        return Boolean.TRUE.equals(result.getSuccess());
    }


    /**
     * 验证手机号
     * @param cacheKey
     * @param verifyCode
     * @return
     */
    public void verifySmsCode(String cacheKey,String verifyCode){
        Map<String,Object> cache = redisUtil.hashMGet(cacheKey);
        if(CollectionUtils.isEmpty(cache)){
            throw new BizException(ResultCode.FAIL.getCode(), "请获取验证信息");
        }
        Long expireIn = (Long) cache.get(CACHE_EXPIRE_IN);
        String cacheCode = String.valueOf(cache.get(CACHE_CODE));
        if(expireIn > System.currentTimeMillis()){
            if(!verifyCode.equals(cacheCode)){
                throw new BizException(ResultCode.FAIL.getCode(), "验证码错误");
            }
        }else{
            throw new BizException(ResultCode.FAIL.getCode(), "验证码失效");
        }
        redisUtil.delete(cacheKey);
    }

    /**
     * 设置缓存
     * @param frequencyKey
     * @param cacheKey
     * @param randomCode
     */
    private void setCache(String frequencyKey,String cacheKey,String randomCode){
        log.info("外部用户修改手机，获取原手机号验证码,验证码为：{}",randomCode);
        Map<String,Object> cache = new HashMap<>();
        cache.put(CACHE_CODE,randomCode);
        cache.put(CACHE_EXPIRE_IN,System.currentTimeMillis() + 3 * 60 * 1000);
        redisUtil.hashMSet(cacheKey,cache,500);
        redisUtil.set(frequencyKey,randomCode,60);
    }

}
