package com.trinasolar.trinax.user.service.biz.message;

import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.messagetemplate.MessageTemplateNoticeTypeEnum;
import com.trinasolar.trinax.basic.dto.input.MessageSendCommonReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendNoticeReqDTO;
import com.trinasolar.trinax.basic.event.MessageBizCodeEnum;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.user.dto.mq.EnterpriseBindMqDTO;
import com.trinasolar.trinax.user.manager.CoBindMessageManager;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class CoBindAuthFailedMessageBiz {

    @Autowired
    private MqManager mqManager;

    @Autowired
    private CoBindMessageManager coBindMessageManager;

    @Value("${coBind.auth.failed.notice}")
    private String noticeTemplateCode;


    public void execute(EnterpriseBindMqDTO reqDTO) {
        log.info("企业用户绑定认证失败,给外部用户发系统通知消息。入参：{}", reqDTO);
        MessageSendCommonReqDTO noticeMessageReqDTO = new MessageSendCommonReqDTO();
        noticeMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.NOTICE.getCode());
        List<MessageSendNoticeReqDTO> noticeList = new ArrayList<>();
        MessageSendNoticeReqDTO noticeReqDTO = coBindMessageManager.generateNotice(
                reqDTO, "", MessageBizCodeEnum.CO_BIND_AUTH_FAILED.getValue(),reqDTO.getEnterpriseId(),
                noticeTemplateCode, Collections.singletonList(reqDTO.getExternalUserId()),  Collections.emptyMap());

        noticeList.add(noticeReqDTO);
        noticeMessageReqDTO.setNoticeList(noticeList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC, JacksonUtil.bean2Json(noticeMessageReqDTO));
        log.info("企业用户绑定认证失败,给外部用户发系统通知消息完成，param:{}", JacksonUtil.bean2Json(noticeMessageReqDTO));
    }


}
