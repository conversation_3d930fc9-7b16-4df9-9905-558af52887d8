package com.trinasolar.trinax.user.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 销售在战区汇报对象
 * @TableName sys_sales_organization_relation
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value ="sys_sales_organization_relation")
@Data
public class SysSalesOrganizationRelationPO implements Serializable {

    @TableId(type = IdType.AUTO)
    @Schema(description = "自增主键")
    private Long id;

    @Schema(description = "销售人员ID")
    private String salesUserId;

    @Schema(description = "服务组织CODE")
    private String bizOrganizationCode;

    @Schema(description = "汇报对象人员ID")
    private String reportToUserId;

    @Schema(description = "Change Owner 原始的 id")
    private String originReportToUserId;

    @Schema(description = "是否已删除;1：已删除 0：未删除")
    private Integer isDeleted;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "创建人姓名")
    private String createdName;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "更新人")
    private String updatedBy;

    @Schema(description = "更新人姓名")
    private String updatedName;

    @Schema(description = "更新时间")
    private LocalDateTime updatedTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}