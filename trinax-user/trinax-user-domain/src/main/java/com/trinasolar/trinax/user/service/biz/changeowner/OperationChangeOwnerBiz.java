package com.trinasolar.trinax.user.service.biz.changeowner;

import cn.hutool.core.collection.CollUtil;
import com.trinasolar.trinax.basic.api.ChangeOwnerFeign;
import com.trinasolar.trinax.basic.api.TodoListFeign;
import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.changeowner.ChangeTaskStatusEnum;
import com.trinasolar.trinax.basic.constants.enums.changeowner.ChangeUserTypeEnum;
import com.trinasolar.trinax.basic.dto.input.changeOwner.ChangeOwnerRecordMqDTO;
import com.trinasolar.trinax.basic.dto.input.changeOwner.ChangeOwnerStatusUpdateReqDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.user.dto.input.changeowner.UserChangeOwnerReqDTO;
import com.trinasolar.trinax.user.repository.atomicservice.SysSalesOperationRelationMapperService;
import com.trinasolar.trinax.user.repository.mapper.SysSalesOperationRelationMapper;
import com.trinasolar.trinax.user.repository.po.SysSalesOperationRelationPO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class OperationChangeOwnerBiz {

    @Autowired
    private SysSalesOperationRelationMapper sysSalesOperationRelationMapper;
    @Autowired
    private SysSalesOperationRelationMapperService sysSalesOperationRelationMapperService;
    @Autowired
    private MqManager mqManager;
    @Autowired
    private ChangeOwnerFeign changeOwnerFeign;
    @Autowired
    private TodoListFeign todoListFeign;


    /**
     * changeOwner
     * @param reqDTO
     * @return
     */
    public Result<String> changeOwner(UserChangeOwnerReqDTO reqDTO) {
        log.info("Operation change owner 入参ContractChangeOwnerReqDTO：{}", reqDTO);
        List<SysSalesOperationRelationPO> relationPOList = sysSalesOperationRelationMapper.selectChangeOwnerRecord(reqDTO);

        if(CollUtil.isEmpty(relationPOList)){
            //不存在变更数据时，
            // 只需要修改任务状态
            updateTaskStatus(reqDTO,true,0,"");
            //添加任务记录
            sendChangeMq(reqDTO);
        }else{
            //填充ChangeOwner数据
            fillOperationData(relationPOList,reqDTO);
            boolean changed = false;
            String errMessage = "";
            //修改业务数据
            try{
                changed = sysSalesOperationRelationMapperService.updateBatchById(relationPOList);
            }catch (Exception e){
                errMessage = e.getMessage();
                log.error("Exception message:",e);
            }finally {
                //修改任务状态
                updateTaskStatus(reqDTO,changed,relationPOList.size(),errMessage);
                //发送任务记录MQ
                sendChangeMq(reqDTO);
            }
        }
        return Result.ok();

    }

    private void fillOperationData(List<SysSalesOperationRelationPO> contractBusinessPOList, UserChangeOwnerReqDTO reqDTO){
         if(ChangeUserTypeEnum.OPERATION_USER_CHANGE.getCode().equals(reqDTO.getChangeType())){
            contractBusinessPOList.forEach(e->{
                e.setOperationUserId(reqDTO.getNewUserId());
            });
        }
    }

    private void updateTaskStatus(UserChangeOwnerReqDTO reqDTO, boolean changed,int amount,String errMessage){
        ChangeOwnerStatusUpdateReqDTO updateReqDTO =  new ChangeOwnerStatusUpdateReqDTO();
        updateReqDTO.setTaskNo(reqDTO.getTaskNo());
        updateReqDTO.setSubTaskNo(reqDTO.getSubTaskNo());
        updateReqDTO.setUpdateUserId(reqDTO.getUpdatedBy());
        updateReqDTO.setUpdateUserName(reqDTO.getUpdatedName());
        updateReqDTO.setSubTaskStatus(changed ? ChangeTaskStatusEnum.SUCCESS.getCode():ChangeTaskStatusEnum.FAIL.getCode());
        updateReqDTO.setOriginUserId(reqDTO.getOriginUserId());
        updateReqDTO.setNewUserId(reqDTO.getNewUserId());
        updateReqDTO.setChangeAmount(amount);
        updateReqDTO.setErrMessage(errMessage);
        changeOwnerFeign.updateTaskStatus(updateReqDTO);
    }

    private void sendChangeMq(UserChangeOwnerReqDTO reqDTO){
        ChangeOwnerRecordMqDTO mqDTO = new ChangeOwnerRecordMqDTO();
        mqDTO.setTaskNo(reqDTO.getTaskNo());
        mqDTO.setSubTaskNo(reqDTO.getSubTaskNo());
        mqDTO.setSituationCode(reqDTO.getSituationCode());
        mqDTO.setOldValue(reqDTO.getOriginUserId());
        mqDTO.setNewValue(reqDTO.getNewUserId());
        mqDTO.setChangeTable("trinax_user.sys_sales_operation_relation");
        mqDTO.setChangeField("operation_user_id");
        mqDTO.setCurrentUserId(reqDTO.getUpdatedBy());
        mqDTO.setCurrentUserName(reqDTO.getUpdatedName());
        log.info("Operation ChangeOwnerRecord mqDTO：{}", reqDTO);
        mqManager.sendTopic(BasicCommonConstant.CHANGE_OWNER_RECORD_TOPIC, JacksonUtil.bean2Json(mqDTO));
    }


}
