package com.trinasolar.trinax.user.repository.atomicservice.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.user.constants.DealerSalesRelationAuthStatus;
import com.trinasolar.trinax.user.repository.atomicservice.SysDealerSalesRelationMapperService;
import com.trinasolar.trinax.user.repository.mapper.SysDealerSalesRelationMapper;
import com.trinasolar.trinax.user.repository.po.SysDealerSalesRelationPO;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class SysDealerSalesRelationMapperServiceImpl extends ServiceImpl<SysDealerSalesRelationMapper, SysDealerSalesRelationPO> implements SysDealerSalesRelationMapperService {
    @Override
    public List<SysDealerSalesRelationPO> listByUserId(String userId) {
        return list(new LambdaQueryWrapper<SysDealerSalesRelationPO>().eq(SysDealerSalesRelationPO::getDealerUserId, userId));
    }

    @Override
    public List<SysDealerSalesRelationPO> listByDealerUserIdAndEnterpriseId(String dealerUserId, String enterpriseId) {
        return list(new LambdaQueryWrapper<SysDealerSalesRelationPO>()
                .eq(SysDealerSalesRelationPO::getEnterpriseId, enterpriseId)
                .eq(SysDealerSalesRelationPO::getDealerUserId, dealerUserId));
    }

    @Override
    public List<SysDealerSalesRelationPO> listByDealerUserIdsAndEnterpriseId(List<String> dealerUserIds, String enterpriseId) {
        if (ObjectUtil.isEmpty(dealerUserIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SysDealerSalesRelationPO>()
                .in(SysDealerSalesRelationPO::getDealerUserId, dealerUserIds)
                .eq(SysDealerSalesRelationPO::getEnterpriseId, enterpriseId));
    }

    @Override
    public List<SysDealerSalesRelationPO> listByEnterpriseId(String enterpriseId) {
        return list(new LambdaQueryWrapper<SysDealerSalesRelationPO>()
                .eq(SysDealerSalesRelationPO::getEnterpriseId, enterpriseId));
    }

    @Override
    public void updateAuthStatus(String dealerUserId, String enterpriseId, DealerSalesRelationAuthStatus dealerSalesRelationAuthStatus, String updateBy, String updateName) {
        LambdaUpdateWrapper<SysDealerSalesRelationPO> updateWrapper = new LambdaUpdateWrapper<SysDealerSalesRelationPO>()
                .eq(SysDealerSalesRelationPO::getDealerUserId, dealerUserId)
                .eq(SysDealerSalesRelationPO::getEnterpriseId, enterpriseId)
                .set(SysDealerSalesRelationPO::getUpdatedBy, updateBy)
                .set(SysDealerSalesRelationPO::getUpdatedName, updateName)
                .set(SysDealerSalesRelationPO::getAuthStatus, dealerSalesRelationAuthStatus.getCode());
        update(updateWrapper);
    }

    @Override
    public List<SysDealerSalesRelationPO> listBySalesUserId(String salesUserId) {
        return list(new LambdaQueryWrapper<SysDealerSalesRelationPO>()
                .eq(SysDealerSalesRelationPO::getSalesUserId, salesUserId));
    }

    @Override
    public List<SysDealerSalesRelationPO> listByDealerUserId(String dealerUserId) {
        LambdaQueryWrapper<SysDealerSalesRelationPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDealerSalesRelationPO::getDealerUserId, dealerUserId);
        return list(queryWrapper);
    }

    @Override
    public List<SysDealerSalesRelationPO> listBySalesUserIds(List<String> salesUserIds) {
        if (ObjectUtil.isEmpty(salesUserIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SysDealerSalesRelationPO>()
                .in(SysDealerSalesRelationPO::getSalesUserId, salesUserIds));
    }

    @Override
    public List<SysDealerSalesRelationPO> listByEnterpriseIds(List<String> enterpriseIds) {
        if (ObjectUtil.isEmpty(enterpriseIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SysDealerSalesRelationPO>()
                .in(SysDealerSalesRelationPO::getEnterpriseId, enterpriseIds));
    }

    @Override
    public List<SysDealerSalesRelationPO> listByDealerUserIdsAndAuthStatus(List<String> dealerUserIds, DealerSalesRelationAuthStatus dealerSalesRelationAuthStatus) {
        if (ObjectUtil.isEmpty(dealerUserIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SysDealerSalesRelationPO>()
                .eq(SysDealerSalesRelationPO::getAuthStatus, dealerSalesRelationAuthStatus.getCode())
                .in(SysDealerSalesRelationPO::getDealerUserId, dealerUserIds));
    }

    @Override
    public List<SysDealerSalesRelationPO> listByEnterpriseIdsAndAuthStatus(List<String> enterpriseIds, Integer authStatus) {
        if (ObjectUtil.isEmpty(enterpriseIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SysDealerSalesRelationPO>()
                .eq(SysDealerSalesRelationPO::getAuthStatus, authStatus)
                .in(SysDealerSalesRelationPO::getEnterpriseId, enterpriseIds));
    }

    @Override
    public void removeByDealerUserIdAndAuthStatus(String dealerUserId, DealerSalesRelationAuthStatus authStatus) {
        remove(new LambdaQueryWrapper<SysDealerSalesRelationPO>()
                .eq(SysDealerSalesRelationPO::getAuthStatus, authStatus.getCode())
                .eq(SysDealerSalesRelationPO::getDealerUserId, dealerUserId));
    }

}
