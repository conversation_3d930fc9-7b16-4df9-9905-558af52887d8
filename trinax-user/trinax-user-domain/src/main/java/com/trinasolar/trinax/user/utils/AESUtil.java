package com.trinasolar.trinax.user.utils;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

@Slf4j
public class AESUtil {

    private static final String ALGORITHM = "AES";

    private static final String key = "6372815490123478";

    public static String encryptIn(String data) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedData = cipher.doFinal(data.getBytes());
            return Base64.getEncoder().encodeToString(encryptedData);
        } catch (Exception e) {
            log.error("AES加密失败", e);
        }
        return null;
    }

    public static String decryptOut(String encryptedData) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decodedData = Base64.getDecoder().decode(encryptedData);
            byte[] decryptedData = cipher.doFinal(decodedData);
            return new String(decryptedData);
        } catch (Exception e) {
            log.error("AES解密失败", e);
        }
        return null;
    }

    public static void main(String[] args) {
        String originalData = "1221xx222zjc@78243055112fref22233";
        String encryptedData = encryptIn(originalData);
        String decryptedData = decryptOut(encryptedData);
        System.out.println("原始数据: " + originalData);
        System.out.println("加密数据: " + encryptedData);
        System.out.println("解密数据: " + decryptedData);
    }
}
