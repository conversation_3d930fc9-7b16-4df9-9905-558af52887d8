package com.trinasolar.trinax.user.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trinasolar.trinax.user.repository.mapper.AppClientMapper;
import com.trinasolar.trinax.user.repository.mapper.ClientDetailsMapper;
import com.trinasolar.trinax.user.repository.po.AppClientPO;
import com.trinasolar.trinax.user.repository.po.ClientDetailsPO;
import com.trinasolar.trinax.user.utils.ShiroSimpleHashUtil;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;


/**
 * <pre>
 * Title: 客户端service
 *
 * Description:
 * <pre>
 * <AUTHOR>
 **/
@Service
public class AppClientBiz {

    @Autowired
    private AppClientMapper clientDAO;

    @Autowired
    private ClientDetailsMapper clientDetailsService;

    @Transactional(rollbackFor = Exception.class)
    public AppClientPO create(AppClientPO appClientModel, ClientDetailsPO clientDetailsModel) {

        String apiKey = RandomStringUtils.randomAlphanumeric(24);
        String secretKey = RandomStringUtils.randomAlphanumeric(32);

        LambdaQueryWrapper<AppClientPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppClientPO::getAppName, appClientModel.getAppName());
        AppClientPO appClientModel1 = clientDAO.selectOne(wrapper);
        if (!ObjectUtils.isEmpty(appClientModel1)) {
            throw new RuntimeException("应用名称已存在");
        }

        // 保存客户端基本信息
        appClientModel.setClientId(apiKey);
        appClientModel.setClientSecret(secretKey);
        appClientModel.setStatus(appClientModel.getStatus());
        appClientModel.setCreateTime(LocalDateTime.now());
        clientDAO.insert(appClientModel);

        // 保存客户端信息详情
        ClientDetailsPO detailsModel = new ClientDetailsPO();
        detailsModel.setClientId(apiKey);
        detailsModel.setClientSecret(secretKey);
        detailsModel.setScope("userProfile");
        detailsModel.setAuthorizedGrantTypes(clientDetailsModel.getAuthorizedGrantTypes());
        detailsModel.setClientSecret(ShiroSimpleHashUtil.simpleHash(secretKey));
        detailsModel.setAutoapprove(Boolean.FALSE.toString());
        detailsModel.setAccessTokenValidity(clientDetailsModel.getAccessTokenValidity());
        detailsModel.setRefreshTokenValidity(clientDetailsModel.getRefreshTokenValidity());
        clientDetailsService.insert(detailsModel);

        // 新增客户端和资源关联

        return appClientModel;


    }

    /***
     * 通过clientId查询详情
     * @param clientId
     * @return
     */
    public ClientDetailsPO findClientsDetail(String clientId) {
        LambdaQueryWrapper<ClientDetailsPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ClientDetailsPO::getClientId, clientId);
        wrapper.orderByAsc(ClientDetailsPO::getId);
        return clientDetailsService.selectOne(wrapper);
    }

    /**
     * 根据clientId查询客户端信息
     *
     * @param clientId clientId
     * @return {@link AppClientPO}
     */
    public AppClientPO findClientByClientId(String clientId) {

        LambdaQueryWrapper<AppClientPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppClientPO::getClientId, clientId);
        AppClientPO client = clientDAO.selectOne(wrapper);

        return client;
    }

}
