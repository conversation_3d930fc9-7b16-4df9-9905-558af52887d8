package com.trinasolar.trinax.user.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("sys_role_permission")
public class RolePermissionPO {

    /** 自增主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 系统角色ID */
    @TableField("role_id")
    private String roleId;

    /** 系统权限ID */
    @TableField("permission_id")
    private String permissionId;

    /**
     * 是否已删除;1：已删除 0：未删除
     */
    @TableField("is_deleted")
    private int isDelete;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建人姓名
     */
    @TableField("created_name")
    private String createdName;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private Date createdTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新人姓名
     */
    @TableField("update_name")
    private String updateName;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private String updateTime;
}
