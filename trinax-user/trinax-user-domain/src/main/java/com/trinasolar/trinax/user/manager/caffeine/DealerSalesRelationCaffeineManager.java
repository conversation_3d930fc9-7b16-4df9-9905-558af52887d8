package com.trinasolar.trinax.user.manager.caffeine;

import com.github.benmanes.caffeine.cache.Cache;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;


@RefreshScope
@Service
public abstract class DealerSalesRelationCaffeineManager<Q, R> {
    @Value("${caffeine.switch.dealerSalesRelation:closed}")
    private String cacheSwitch;

    private final Object lockKey = new Object();

    abstract Cache<String, Object> getCache();

    abstract String buildKey(Q pageReqDTO);

    public R getPage(String userId, Q pageReqDTO) {
        if(!"open".equals(cacheSwitch)){
          return null;
        }

        String key = buildKey(pageReqDTO);
        Map<String, R> map = (Map<String,R>) getCache().getIfPresent(userId);
        if (Objects.isNull(map)) {
            return null;
        }
        return map.get(key);
    }

    public void putPage(String userId, Q pageReqDTO, R pageResponse) {
        if(!"open".equals(cacheSwitch)){
            return;
        }
        Map<String, R> map;
        synchronized (lockKey) {
            map = (Map<String, R>) getCache().getIfPresent(userId);
            if (map == null) {
                map = new ConcurrentHashMap<>();
                getCache().put(userId, map);
            }
        }
        String key = buildKey(pageReqDTO);
        map.putIfAbsent(key, pageResponse);

    }

    public void clearByUserId(String userId) {
        getCache().invalidate(userId);
    }

    public void clearByUserIds(List<String> userIds) {
        getCache().invalidateAll(userIds);
    }
    public void clearAll() {
        getCache().cleanUp();
    }

}
