package com.trinasolar.trinax.user.manager;

import cn.hutool.json.JSONUtil;
import com.trinasolar.trinax.partner.constants.ContactorSyncConstant;
import com.trinasolar.trinax.partner.constants.PartnerConstant;
import com.trinasolar.trinax.partner.dto.mq.ContactorSyncMqDTO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserSyncContactManager {
    private final MqManager mqManager;

    public void syncContact(String userId, String updatedBy, String updatedName) {
        // 发送mq消息-同步联系人
        ContactorSyncMqDTO contactorSyncMqDTO = ContactorSyncMqDTO.builder()
                .setSyncType(ContactorSyncConstant.SYNC_ALL_ON_USER)
                .setCreatedBy(updatedBy)
                .setCreatedName(updatedName)
                .setUserId(userId);
        String data = JSONUtil.toJsonStr(contactorSyncMqDTO);
        log.info("修改手机号/姓名发送mq消息-同步联系人：{}", data);
        mqManager.sendTopic(PartnerConstant.CONTACTOR_SYNC, data);
    }

}
