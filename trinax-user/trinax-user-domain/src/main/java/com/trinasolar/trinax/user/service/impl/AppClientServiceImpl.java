package com.trinasolar.trinax.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.user.dto.output.AppClientResDTO;
import com.trinasolar.trinax.user.repository.po.AppClientPO;
import com.trinasolar.trinax.user.service.AppClientService;
import com.trinasolar.trinax.user.service.biz.AppClientBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AppClientServiceImpl implements AppClientService {

    @Autowired
    private AppClientBiz appClientBiz;


    @Override
    public Result<AppClientResDTO> findClientByClientId(String clientId) {

        AppClientPO appClientPO = appClientBiz.findClientByClientId(clientId);

        AppClientResDTO appClientResDTO = BeanUtil.copyProperties(appClientPO,AppClientResDTO.class);
        return Result.ok(appClientResDTO);
    }
}
