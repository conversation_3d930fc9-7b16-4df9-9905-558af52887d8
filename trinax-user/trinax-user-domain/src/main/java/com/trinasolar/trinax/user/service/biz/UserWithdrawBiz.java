package com.trinasolar.trinax.user.service.biz;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trinasolar.trinax.basic.api.SmsSendFeign;
import com.trinasolar.trinax.basic.dto.input.TrinaSolarSmsReqDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.common.utils.SensitiveDataHasher;
import com.trinasolar.trinax.user.constants.DeleteFlagEnum;
import com.trinasolar.trinax.user.constants.SysUserStatusEnum;
import com.trinasolar.trinax.user.dto.input.SysUserUpdateReqDTO;
import com.trinasolar.trinax.user.dto.input.SysUserWithdrawReqDTO;
import com.trinasolar.trinax.user.repository.mapper.SysUserMapper;
import com.trinasolar.trinax.user.repository.po.SysUserPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;


/**
 * <pre>
 * Title: 用户注销
 *
 * Description:
 * <pre>
 * <AUTHOR>
 **/
@Service
@Slf4j
public class UserWithdrawBiz {
    @Value("${trina.sms.withdraw-templateCode}")
    private String withdrawSmsTemplateCode;

    @Autowired
    private SmsSendFeign smsSendFeign;

    @Resource
    private SysUserMapper userMapper;

    @Autowired
    private ExecutorService executorService;

    /**
     * 注销用户
     *
     * @param sysUserUpdateReqDTO
     * @return
     */
    public Result<Boolean> withdraw(SysUserUpdateReqDTO sysUserUpdateReqDTO) {
        String mobile = sysUserUpdateReqDTO.getMobile();
        //注销后，敏感数据不可逆加密
        sysUserUpdateReqDTO.setMobile(SensitiveDataHasher.hash(mobile));
        sysUserUpdateReqDTO.setUserName(SensitiveDataHasher.hash(sysUserUpdateReqDTO.getUserName()));
        SysUserPO updateObj = BeanUtil.copyProperties(sysUserUpdateReqDTO, SysUserPO.class);
        int count = userMapper.updateByUserId(updateObj);
        if (count == 0) {
            log.error("注销更新数据库失败，userId={}", sysUserUpdateReqDTO.getUserId());
            throw new BizException("", "注销失败");
        }
        // 发短信
        Map<String, String> map = new HashMap<>(8);
        map.put("phonenumber", mobile);

        TrinaSolarSmsReqDTO smsReqDTO = new TrinaSolarSmsReqDTO();
        smsReqDTO.setTemplateCode(withdrawSmsTemplateCode);
        smsReqDTO.setPhoneNumber(mobile);
        smsReqDTO.setTemplateParam(map);
        executorService.execute(() -> smsSendFeign.trinaSolarSend(smsReqDTO));
        return Result.ok();
    }

    /**
     * 注销内部用户
     *
     * @param dto
     * @return
     */
    public void withdrawInternalUser(SysUserWithdrawReqDTO dto) {
        //注销后，敏感数据不可逆加密
        LambdaQueryWrapper<SysUserPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserPO::getUserId, dto.getUserId()).eq(SysUserPO::getUserType, dto.getUserType()).eq(SysUserPO::getIsDeleted, DeleteFlagEnum.NOT_DELETE.getCode());

        SysUserPO query = userMapper.selectOne(queryWrapper);

        if (ObjectUtils.isEmpty(query)) {
            throw new BizException(ResultCode.FAIL_DATA_NOT_EXIST.getCode(), "用户不存在");
        }

        if (SysUserStatusEnum.WITHDRAW.getCode().equals(query.getStatus())) {
            throw new BizException(ResultCode.REQ_VALIDATE_ERROR.getCode(), "用户已注销,不能重复注销");
        }
        SysUserPO sysUserPO = new SysUserPO();
        sysUserPO.setUserId(dto.getUserId());
        sysUserPO.setUserEmail(SensitiveDataHasher.hash(dto.getUserEmail()));
        sysUserPO.setUserName(SensitiveDataHasher.hash(dto.getUserName()));
        if(StringUtils.isNotBlank(query.getSfId())){
            sysUserPO.setSfId(SensitiveDataHasher.hash(query.getSfId()));
        }
        if(StringUtils.isNotBlank(query.getMobile())){
            sysUserPO.setMobile(SensitiveDataHasher.hash(query.getMobile()));
        }
        if(StringUtils.isNotBlank(query.getUserCode())){
            sysUserPO.setUserCode(SensitiveDataHasher.hash(query.getUserCode()));
        }
        sysUserPO.setUpdatedTime(LocalDateTime.now());
        sysUserPO.setUpdatedBy(dto.getUpdatedBy());
        sysUserPO.setUpdatedName(dto.getUpdatedName());
        sysUserPO.setStatus(SysUserStatusEnum.WITHDRAW.getCode());
        userMapper.updateByUserId(sysUserPO);
    }
}
