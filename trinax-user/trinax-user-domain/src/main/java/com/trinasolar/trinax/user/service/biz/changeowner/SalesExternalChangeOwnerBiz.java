package com.trinasolar.trinax.user.service.biz.changeowner;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.trinasolar.trinax.basic.api.ChangeOwnerFeign;
import com.trinasolar.trinax.basic.api.TodoListFeign;
import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.changeowner.ChangeTaskStatusEnum;
import com.trinasolar.trinax.basic.constants.enums.changeowner.ChangeUserTypeEnum;
import com.trinasolar.trinax.basic.dto.input.TodoListChangeOwnerReqDTO;
import com.trinasolar.trinax.basic.dto.input.changeOwner.ChangeOwnerRecordMqDTO;
import com.trinasolar.trinax.basic.dto.input.changeOwner.ChangeOwnerStatusUpdateReqDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.user.dto.input.changeowner.UserChangeOwnerReqDTO;
import com.trinasolar.trinax.user.repository.atomicservice.SysDealerSalesRelationMapperService;
import com.trinasolar.trinax.user.repository.mapper.SysDealerSalesRelationMapper;
import com.trinasolar.trinax.user.repository.po.SysDealerSalesRelationPO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Service
@Slf4j
public class SalesExternalChangeOwnerBiz {

    @Autowired
    private SysDealerSalesRelationMapper sysDealerSalesRelationMapper;
    @Autowired
    private SysDealerSalesRelationMapperService sysDealerSalesRelationMapperService;
    @Autowired
    private MqManager mqManager;
    @Autowired
    private ChangeOwnerFeign changeOwnerFeign;
    @Autowired
    private TodoListFeign todoListFeign;


    /**
     * changeOwner
     * @param reqDTO
     * @return
     */
    public Result<String> changeOwner(UserChangeOwnerReqDTO reqDTO) {
        log.info("Sales External change owner 入参ContractChangeOwnerReqDTO：{}", reqDTO);
        List<SysDealerSalesRelationPO> relationPOList = sysDealerSalesRelationMapper.selectChangeOwnerRecord(reqDTO);

        if(CollUtil.isEmpty(relationPOList)){
            //不存在变更数据时，
            // 只需要修改任务状态
            updateTaskStatus(reqDTO,true,0,"");
            //添加任务记录
            sendChangeMq(reqDTO);
        }else{
            //填充ChangeOwner数据
            fillRelationData(relationPOList,reqDTO);
            boolean changed = false;
            String errMessage = "";
            //修改业务数据
            try{
                //针对继任者，如果转交后会存在重复的关联关系（企业，外部用户，销售），需要将继任者已有关联关系删除
                List<SysDealerSalesRelationPO> existRelationList = sysDealerSalesRelationMapper.selectExistsRelation(relationPOList);
                if(CollUtil.isNotEmpty(existRelationList)){
                    List<Long> existIdList = existRelationList.stream().map(SysDealerSalesRelationPO::getId).collect(Collectors.toList());
                    sysDealerSalesRelationMapperService.removeByIds(existIdList);
                }
                changed = sysDealerSalesRelationMapperService.updateBatchById(relationPOList);
            }catch (Exception e){
                errMessage = e.getMessage();
                log.error("Exception message:",e);
            }finally {
                //修改任务状态
                updateTaskStatus(reqDTO,changed,relationPOList.size(),errMessage);
                //修改待办状态
                if(changed){
                    updateTodoList(reqDTO,relationPOList);
                }
                //发送任务记录MQ
                sendChangeMq(reqDTO);
            }
        }
        return Result.ok();

    }

    private void updateTodoList(UserChangeOwnerReqDTO reqDTO,List<SysDealerSalesRelationPO> relationPOList){
        TodoListChangeOwnerReqDTO todoChangeReq = BeanUtil.toBean(reqDTO,TodoListChangeOwnerReqDTO.class);
        List<String> bizNoList= new ArrayList<>();
        relationPOList.forEach(e->{
            bizNoList.add(e.getDealerUserId() + "|" + e.getEnterpriseId());
        });
        todoChangeReq.setBizNoList(bizNoList);
        todoChangeReq.setBizPrefix("CO_BIND_");
        todoListFeign.changeOwner(todoChangeReq);
    }

    private void fillRelationData(List<SysDealerSalesRelationPO> relationPOList, UserChangeOwnerReqDTO reqDTO){
        if(ChangeUserTypeEnum.SALES_USER_CHANGE.getCode().equals(reqDTO.getChangeType())){
            relationPOList.forEach(e->{
                e.setSalesUserId(reqDTO.getNewUserId());
            });
        } if(ChangeUserTypeEnum.EXTERNAL_USER_CHANGE.getCode().equals(reqDTO.getChangeType())){
            relationPOList.forEach(e->{
                e.setDealerUserId(reqDTO.getNewUserId());
            });
        }
    }

    private void updateTaskStatus(UserChangeOwnerReqDTO reqDTO, boolean changed,int amount,String errMessage){
        ChangeOwnerStatusUpdateReqDTO updateReqDTO =  new ChangeOwnerStatusUpdateReqDTO();
        updateReqDTO.setTaskNo(reqDTO.getTaskNo());
        updateReqDTO.setSubTaskNo(reqDTO.getSubTaskNo());
        updateReqDTO.setUpdateUserId(reqDTO.getUpdatedBy());
        updateReqDTO.setUpdateUserName(reqDTO.getUpdatedName());
        updateReqDTO.setSubTaskStatus(changed ? ChangeTaskStatusEnum.SUCCESS.getCode():ChangeTaskStatusEnum.FAIL.getCode());
        updateReqDTO.setOriginUserId(reqDTO.getOriginUserId());
        updateReqDTO.setNewUserId(reqDTO.getNewUserId());
        updateReqDTO.setChangeAmount(amount);
        updateReqDTO.setErrMessage(errMessage);
        changeOwnerFeign.updateTaskStatus(updateReqDTO);
    }

    private void sendChangeMq(UserChangeOwnerReqDTO reqDTO){
        ChangeOwnerRecordMqDTO mqDTO = new ChangeOwnerRecordMqDTO();
        mqDTO.setTaskNo(reqDTO.getTaskNo());
        mqDTO.setSubTaskNo(reqDTO.getSubTaskNo());
        mqDTO.setSituationCode(reqDTO.getSituationCode());
        mqDTO.setOldValue(reqDTO.getOriginUserId());
        mqDTO.setNewValue(reqDTO.getNewUserId());
        mqDTO.setChangeTable("trinax_user.sys_dealer_sales_relation");
        mqDTO.setOldValue(reqDTO.getOriginUserId());
        mqDTO.setNewValue(reqDTO.getNewUserId());
        if(ChangeUserTypeEnum.SALES_USER_CHANGE.getCode().equals(reqDTO.getChangeType())){
            mqDTO.setChangeField("sales_user_id");
        }else if(ChangeUserTypeEnum.EXTERNAL_USER_CHANGE.getCode().equals(reqDTO.getChangeType())){
            mqDTO.setChangeField("dealer_user_id");
        }
        mqDTO.setCurrentUserId(reqDTO.getUpdatedBy());
        mqDTO.setCurrentUserName(reqDTO.getUpdatedName());
        log.info("Sales External ChangeOwnerRecord mqDTO：{}", reqDTO);
        mqManager.sendTopic(BasicCommonConstant.CHANGE_OWNER_RECORD_TOPIC, JacksonUtil.bean2Json(mqDTO));
    }


}
