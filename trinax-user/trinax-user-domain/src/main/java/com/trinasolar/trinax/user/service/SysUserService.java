package com.trinasolar.trinax.user.service;


import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.user.constants.BindEnterpriseEnum;
import com.trinasolar.trinax.user.dto.input.*;
import com.trinasolar.trinax.user.dto.input.query.CoAttachReqDTO;
import com.trinasolar.trinax.user.dto.input.query.SearchConditionReqDTO;
import com.trinasolar.trinax.user.dto.output.*;
import com.trinasolar.trinax.user.dto.output.query.OperatorInfoResDTO;
import com.trinasolar.trinax.user.dto.output.query.SearchResDTO;
import com.trinasolar.trinax.user.repository.po.SysUserPO;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 后台用户 Service 接口
 *
 * <AUTHOR>
 */
public interface SysUserService {


    /**
     * auth-center服务在使用这方法
     * 通过userid，修改用户.已经拆分为内部与外部用户
     *
     * @param reqVO 用户信息
     * @return true表示需要对外部用户的旧手机号（对应）账号下线操作
     */
    @Deprecated
    boolean updateUser(@Valid SysUserUpdateReqDTO reqVO);
    boolean updateExternalUser(@Valid SysExternalUserUpdateReqDTO reqVO);

    @Transactional(rollbackFor = Exception.class)
    boolean updateInternalUser(SysInternalUserUpdateReqDTO reqDTO);

    /**
     * 通过userid，修改用户
     *
     * @param reqVO 用户信息
     */
    void updateUserNameByUserId(@Valid SysUserUpdateReqDTO reqVO);

    Result<List<String>> updateUserStatusBatch(Collection<String> userIds,String status);

    Result<List<String>> batchUpdateAssignStatus(Collection<String> userIds,Integer status);

    /**
     * 修改状态
     *
     * @param id     用户编号
     * @param status 状态
     */
    void updateUserStatus(Long id, String status);

    /**
     * 修改用户极光设备id
     *
     * @param userId     用户编号
     * @param registrationId 设备id
     */
    void updateUserRegistrationId(String userId, String registrationId);

    Result<Boolean> withdraw(SysUserUpdateReqDTO sysUserUpdateReqDTO);

    /***
     * 注销内部用户
     * @param dto
     */
    void withdrawInternalUser(SysUserWithdrawReqDTO dto);

    SysUserPO getUserByEmail(String email);

    /**
     * 通过手机号获取用户
     *
     * @param mobile 手机号
     * @return 用户对象信息
     */
    SysUserPO getUserByMobile(String mobile,String userType);

    List<SysUserRespDTO> getInternalUsersByUserCodes(List<String> userCodeSet);

    PageResponse<SysUserPO> getDefaultUserPage(String userType, UsersQueryDTO queryDTO, long page , long rows);

    PageResponse<SysUserPO> getExternalUserPage(UsersQueryDTO usersQueryDTO, long current, long size);

    PageResponse<SysUserPO> getUnDealUserPage(DealUsersQueryDTO usersQueryDTO, long current, long size);

    List<SysUserPO> exportUnDealUserList(DealUsersQueryDTO usersQueryDTO);

    PageResponse<SysUserPO> getDealUserPage(DealUsersQueryDTO usersQueryDTO, long current, long size);

    List<SysUserPO> exportDealUserList(DealUsersQueryDTO usersQueryDTO);

    List<SysExternalUserRespDTO> getExternalUserPageAggregate(List<SysUserPO> userPos);

    List<DealUserDto> getDealUserVoPage(List<SysUserPO> userPos);

    List<UnDealUserDto> getUnDealUserVoPage(List<SysUserPO> userPos);

    BindEnterpriseEnum convertIsBindEnterprise(SysUserPO user);

    List<SysInternalUserRespDTO> getInternalUserPageAggregate(List<SysUserPO> userPos);

    PageResponse<SysInternalUserRespDTO> getInternalUserPage(PageRequest<UsersQueryDTO> request);
    PageResponse<SysUserPO> getInternalUserPage(UsersQueryDTO usersQueryDTO, long current, long size);

    /**
     * 通过用户 ID 查询用户
     *
     * @param id 用户ID
     * @return 用户对象信息
     */
    SysUserPO getUser(Long id);
    /**
     * 根据职位和区域获取所有的上级人员
     */

    List<SysUserPO> getSuperiors(UserSuperiorReqDTO reqDTO);

    /**
     * 获取这个组织的职位，所有的上级人员
     *
     * @param enterpriseId 企业
     * @return 上级用户对象列表
     */
    List<SysUserPO> getExternalSuperiors(String enterpriseId);

    SysUserPO getUserByUserId(String userId);

    /**
     * 查询user表中所有生效的用户
     * @param userIds
     * @return
     */
    List<SysUserPO> getUsersByUserIds(List<String> userIds);

    List<SysUserPO> getExportUsersByType(String userType, UsersQueryDTO usersQueryDTO);

    SysUserRespDTO getUserByEmailOrUserCode(String emailOrUserCode,String userType);

    Result<String> sendModifySmsCode(SendSmsReqDTO reqDTO);

    Result<String> verifyModifySmsCode(VerifySmsReqDTO reqDTO);

    Result<String> sendConfirmSmsCode(SendSmsReqDTO reqDTO);

    Result<String> verifyConfirmSmsCode(VerifySmsReqDTO reqDTO);

    List<SysUserRespDTO> getUserByCondition(SysUserQueryReqDTO reqDTO);
    List<OperatorRespDTO> getOperators();


    /**
     * @param userPos 外部用户列表
     * @return key:外部用户uerId,value：要显示的字符串
     */
    Map<String, Map<String, String>> getBindEnterprise(List<SysUserPO> userPos);

    List<SysUserRespDTO> listUserByRoleId(String roleId);

    List<SysUserRespDTO> listUser(SysUserQueryDTO reqDTO);

    /**
     * 暂时为了处理其他地方使用循环查询db的问题。因此返回值直接使用了SysEnterpriseUserRespDTO
     * @param userIds
     * @return
     */
    List<SysEnterpriseUserRespDTO> getUsersWithRoles(List<String> userIds);

    List<SysInternelSalesRelationRespDTO> getInternalSalesRelation(String userId);

    SysUserRespDTO getSysUserRespDTOByUserId(String userId);

    SysUseAndRoleRespDTO getUserAndRoleByUserId(String userId);

    List<SysSalesSubordinateRespDTO> getSalesSubordinate(SubordinateQueryReqDTO reqDTO);

    /***
     * 测试增加外部用户
     */
    void testCreateExternalUser();

    FeedbackUserRespDTO getFeedbackUserInfoByUserId(String userId);

    Result<String> saveSysUser(SysUserSaveReqDTO reqDTO);

    List<SalesUserSelectRespDTO> listSalesUserSelect(String loginUserId, String userId, String enterpriseId);

    List<SalesUserSelectRespDTO> listMainSalesUserSelect(String loginUserId, String enterpriseId);

    void externalAdd(SysExternalUserAddReqDTO reqDTO);

    void internalAdd(SysInternalUserAddReqDTO reqDTO);

    UserDetailRespDTO pcUserDetail(String userId);

    Result<SysUserRespDTO> getAreaManagerByOrgCode(String bizOrgCode);

    PageResponse<QuestionnaireInternalUserRespDTO> pageQuestionnaireInternalUser(PageRequest<QuestionnaireInternalUserQueryDTO> pageReqDTO);

    PageResponse<QuestionnaireExternalUserRespDTO> pageQuestionnaireExternalUser(PageRequest<QuestionnaireExternalUserQueryDTO> pageReqDTO);

    List<String> listClearUserIds(List<String> userIds);

    Result<List<SearchResDTO>> searchUser(SearchConditionReqDTO req);

    Result<List<OperatorInfoResDTO>> qryOperatorBindSales(CoAttachReqDTO req);
    PageResponse<UserBasicInfoRespDTO> fuzzyQueryByKeyword(PageRequest<String> pageRequest);

    Result<Boolean> updateUserInfo(UpdateUserDto reqDTO);

    Boolean configSale(ConfigSaleDto configSaleDto);

    Boolean validateEmail(SysUserUpdateReqDTO reqDTO);

    Boolean updateEmail(SysUserUpdateReqDTO reqDTO);


    Boolean sendSmsCode(String phone,String channel);
    Boolean checkSmsCode(String phone, String smsCode, String channel);

    Boolean checkOrganizationCode(String userId);
}
