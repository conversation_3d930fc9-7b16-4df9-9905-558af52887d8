package com.trinasolar.trinax.user.threadpool;

import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description thread pool monitor, extends from ThreadPoolExecutor
 * <AUTHOR>
 * @Date 12/10/2023 16:27
 */
@Slf4j
public class UserThreadPoolExecutor extends ThreadPoolExecutor {

    /**
     * Save the task start execution time.
     * When the task ends, the task execution time is calculated by subtracting the start time from the task end time
     */
    private ConcurrentHashMap<String, Date> startTimes;

    /**
     * The thread pool name is generally named after the business name
     */
    private String poolName;

    /**
     * Enable monitor
     */
    private boolean isMonitor = false;

    /**
     * Call the parent class constructor and initialize the HashMap and thread name
     *
     * @param corePoolSize     core pool size
     * @param maximumPoolSize  max pool size
     * @param keepAliveTime    keep alive time
     * @param unit             keep alive time unit
     * @param workQueue        work queue
     * @param handler          rejected policy
     * @param poolName         thread pool name
     */
    public UserThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime,
                                  TimeUnit unit, BlockingQueue<Runnable> workQueue,
                                  RejectedExecutionHandler handler, String poolName){
        this(corePoolSize,maximumPoolSize,keepAliveTime,unit,workQueue,
                Executors.defaultThreadFactory(),handler,poolName);
    }

    /**
     * Call the parent class constructor and initialize the HashMap and thread name
     *
     * @param corePoolSize     core pool size
     * @param maximumPoolSize  max pool size
     * @param keepAliveTime    keep alive time
     * @param unit             keep alive time unit
     * @param workQueue        work queue
     * @param threadFactory    thread factory
     * @param handler          rejected policy
     * @param poolName         thread pool name
     */
    public UserThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime,
                                  TimeUnit unit, BlockingQueue<Runnable> workQueue,
                                  ThreadFactory threadFactory, RejectedExecutionHandler handler, String poolName) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
        this.startTimes = new ConcurrentHashMap<>();
        this.poolName = poolName;
    }

    /**
     * Call the parent class constructor and initialize the HashMap and thread name
     *
     * @param corePoolSize     core pool size
     * @param maximumPoolSize  max pool size
     * @param keepAliveTime    keep alive time
     * @param unit             keep alive time unit
     * @param workQueue        work queue
     * @param threadFactory    thread factory
     * @param handler          rejected policy
     * @param poolName         thread pool name
     * @param isMonitor        enable monitor
     */
    public UserThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime,
                                  TimeUnit unit, BlockingQueue<Runnable> workQueue,
                                  ThreadFactory threadFactory, RejectedExecutionHandler handler,
                                  String poolName, boolean isMonitor) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
        this.startTimes = new ConcurrentHashMap<>();
        this.poolName = poolName;
        this.isMonitor = isMonitor;
    }

    /**
     * When a thread is delayed to close (waiting for all tasks in the thread pool to be executed), the thread pool status is counted
     */
    @Override
    public void shutdown(){
        if(isMonitor) {
            //Count the number of executed tasks, executing tasks and unexecuted tasks
            log.info("{}-pool-monitor: delayed shutdown. number of tasks executed:{}, number of tasks being executed:{}, number of tasks not executed:{}",
                    this.poolName, this.getCompletedTaskCount(), this.getActiveCount(), this.getQueue().size());
        }
        super.shutdown();
    }

    /**
     * When a thread is closed immediately, count the thread pool
     */
    @Override
    public List<Runnable> shutdownNow(){
        if(isMonitor) {
            //Count the number of executed tasks, executing tasks and unexecuted tasks
            log.info("{}-pool-monitor: shutdown immediately. number of tasks executed:{}, number of tasks being executed:{}, number of tasks not executed:{}",
                    this.poolName, this.getCompletedTaskCount(), this.getActiveCount(), this.getQueue().size());
        }
        return super.shutdownNow();
    }

    /**
     * Record the start time before task execution
     *
     * @param t
     * @param r
     */
    @Override
    protected void beforeExecute(Thread t,Runnable r){
        if(isMonitor) {
            startTimes.put(String.valueOf(r.hashCode()), new Date());
        }
    }

    /**
     * Calculate the task end time after the task ends
     *
     * @param r
     * @param t
     */
    @Override
    public void afterExecute(Runnable r,Throwable t){
        if(isMonitor) {
            Date startDate = startTimes.remove(String.valueOf(r.hashCode()));
            Date finishDate = new Date();
            long diff = finishDate.getTime() - startDate.getTime();
            //Statistics of task time consumption, number of initial threads, number of core threads, and number of tasks being executed
            //Number of completed tasks, total number of tasks, number of cached tasks in the queue, and the maximum number of threads in the pool
            //The maximum number of threads allowed, the idle time of threads, whether the thread pool is closed, and whether the thread pool is terminated
            log.info("{}-pool-monitor: " +
                            "spend time:{} ms, initial thread size:{}, core thread size:{}, executing task count:{}, " +
                            "completed task count:{}, task count:{}, task queue size:{}, largest pool size:{}, " +
                            "max pool size:{},  keep alive time:{}, is shutdown:{}, is terminated:{}",
                    this.poolName,
                    diff, this.getPoolSize(), this.getCorePoolSize(), this.getActiveCount(),
                    this.getCompletedTaskCount(), this.getTaskCount(), this.getQueue().size(), this.getLargestPoolSize(),
                    this.getMaximumPoolSize(), this.getKeepAliveTime(TimeUnit.MILLISECONDS), this.isShutdown(), this.isTerminated());
        }
    }

    /**
     * Create a fixed thread pool. The code is from executors Newfixedthreadpool method, where poolName is added
     *
     * @param nThreads    thread count
     * @param poolName    pool name
     * @return  ExecutorService
     */
    public static ExecutorService newFixedThreadPool(int nThreads,String poolName){
        return new UserThreadPoolExecutor(nThreads,nThreads,0L,TimeUnit.MILLISECONDS,new LinkedBlockingDeque<>(),new AbortPolicy(),poolName);
    }

    /**
     * Create a cache thread pool. The code is from executors Newcachedthreadpool method, where poolName is added
     *
     * @param poolName  pool name
     * @return  ExecutorService
     */
    public static ExecutorService newCachedThreadPool(String poolName) {
        return new UserThreadPoolExecutor(0, Integer.MAX_VALUE, 60L, TimeUnit.SECONDS, new SynchronousQueue(), new AbortPolicy(), poolName);
    }

    /**
     * The thread used to generate the thread pool only overwrites the default thread factory of the thread pool and passes in the thread pool name for problem tracking
     */
    static class EventThreadFactory implements ThreadFactory {
        private static final AtomicInteger poolNumber = new AtomicInteger(1);
        private final ThreadGroup group;
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        /**
         * Initialize thread factory
         *
         * @param poolName pool name
         */
        EventThreadFactory(String poolName) {
            SecurityManager s = System.getSecurityManager();
            group = Objects.nonNull(s) ? s.getThreadGroup() : Thread.currentThread().getThreadGroup();
            namePrefix = poolName + "-pool-" + poolNumber.getAndIncrement() + "-thread-";
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(group, r, namePrefix + threadNumber.getAndIncrement(), 0);
            if (t.isDaemon()) {
                t.setDaemon(false);
            }
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }

    /**
     * With monitor thread pool builder
     */
    public static final class Builder {

        private Integer corePoolSize = 1;

        private Integer maximumPoolSize = 2;

        private Long keepAliveTime = 180L;

        private TimeUnit unit = TimeUnit.SECONDS;

        private Integer taskQueueCapacity = 512;

        private ThreadFactory threadFactory = Executors.defaultThreadFactory();

        private RejectedExecutionHandler executionHandler = new AbortPolicy();

        private String poolName = "";

        private boolean isMonitor = false;

        public Builder corePoolSize(int value) {
            corePoolSize = value;
            return this;
        }

        public Builder maximumPoolSize(int value) {
            maximumPoolSize = value;
            return this;
        }

        public Builder keepAliveTime(long value) {
            keepAliveTime = value;
            return this;
        }

        public Builder unit(TimeUnit value) {
            unit = value;
            return this;
        }

        public Builder taskQueueCapacity(int value) {
            taskQueueCapacity = value;
            return this;
        }

        public Builder threadFactory(ThreadFactory factory) {
            threadFactory = factory;
            return this;
        }

        public Builder handler(RejectedExecutionHandler handler) {
            executionHandler = handler;
            return this;
        }

        public Builder poolName(String value) {
            poolName = value;
            return this;
        }

        public Builder isMonitor(boolean value) {
            isMonitor = value;
            return this;
        }

        /**
         * create object
         * @return ThreadPoolMonitorExecutor
         */
        public UserThreadPoolExecutor build() {
            return new UserThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime,
                    unit, new LinkedBlockingQueue<>(taskQueueCapacity), threadFactory, executionHandler, poolName, isMonitor);
        }
    }
}

