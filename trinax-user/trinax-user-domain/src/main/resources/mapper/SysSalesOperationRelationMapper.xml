<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trinasolar.trinax.user.repository.mapper.SysSalesOperationRelationMapper">

  <select id="getSysSalesOrganizationByOps"
          resultType="com.trinasolar.trinax.user.dto.output.SysOrganizationSalesRespDTO">
<!--      SELECT a.*,-->
<!--             b.organization_name as userOrganizationName-->
<!--      from (SELECT u.user_name           as salesName,-->
<!--                   u.user_code,-->
<!--                   so.biz_organization_code,-->
<!--                   u.organization_code,-->
<!--                   org.organization_name as organizationName-->
<!--            FROM sys_sales_operation_relation so-->
<!--                     LEFT JOIN sys_user u on-->
<!--                so.sales_user_id = u.user_id-->
<!--                     LEFT JOIN sys_organization org on-->
<!--                so.biz_organization_code = org.organization_code-->
<!--            WHERE so.operation_user_id = #{operationId}) a-->
<!--               LEFT JOIN sys_organization b-->
<!--                         on-->
<!--                             a.organization_code = b.organization_code-->
<!--      where 1 = 1-->
      SELECT u.user_name            as salesName,
             u.user_code,
             ssor.biz_organization_code,
             so01.organization_name,
             u.organization_code,
             so02.organization_name as userOrganizationName,
             u.user_email,
             u.mobile
      FROM sys_sales_operation_relation ssor
               LEFT JOIN sys_user u on
          ssor.sales_user_id = u.user_id
               LEFT JOIN sys_organization so01 on
          ssor.biz_organization_code = so01.organization_code
               LEFT JOIN sys_organization so02 on
          u.organization_code = so02.organization_code
      WHERE ssor.operation_user_id = #{operationId};
  </select>

    <select id="selectChangeOwnerRecord" resultType="com.trinasolar.trinax.user.repository.po.SysSalesOperationRelationPO">
        select t1.*
        from
        trinax_user.sys_sales_operation_relation t1
        where 1=1
        <if test="req.changeType=='OPERATION_USER_CHANGE' and req.conditionList != null and req.conditionList.size() > 0">
            and t1.operation_user_id = #{req.originUserId}
            and (t1.biz_organization_code,t1.sales_user_id) in
            <foreach collection="req.conditionList" item="item" index="index" open="(" close=")" separator=",">
                (#{item.organizationCode}, #{item.userId})
            </foreach>
        </if>
    </select>
</mapper>