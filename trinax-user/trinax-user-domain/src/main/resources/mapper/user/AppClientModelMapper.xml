<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trinasolar.trinax.user.repository.mapper.AppClientMapper" >
    <!-- 应用客户端 -->
    <select id="findByPage" resultType="com.trinasolar.trinax.user.repository.po.AppClientPO">
        SELECT
              *
          FROM
              authority_app_client
        <where>
            <if test="dto.appName != null and dto.appName != ''">
                AND app_name LIKE CONCAT('%',#{dto.appName},'%')
            </if>
            <if test="dto.appPlatform != null and dto.appPlatform != ''">
                AND app_platform LIKE CONCAT('%',#{dto.appPlatform},'%')
            </if>
            <if test="dto.status != null and dto.status != ''">
                AND status = #{dto.status}
            </if>
            <if test="dto.appEnName != null and dto.appEnName != ''">
                AND app_en_name LIKE CONCAT('%',#{dto.appEnName})
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>