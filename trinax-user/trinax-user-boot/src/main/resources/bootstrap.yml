# Tomcat
server:
  port: 81
#日志组件
trinasolar:
  team: dtt-team
  project:
    name: trinax-user-service
# Spring
spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  application:
    # 应用名称
    name: trinax-user-service
    version: 1.0
  profiles:
    # 环境配置
    active: ${ACTIVE_PROFILE:local}
  #    active: dev
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
#      username: trinasolarx
#      password: ${NACOS_PASSWORD}
      discovery:
        # 使用德勤分配的 namespace 代替，如没有则注释掉这一样
        namespace: trinax
        # 服务注册地址
        server-addr: ${NACOS:localhost}:8848
      #        server-addr: mynacos:8848
      config:
        # 使用德勤分配的 namespace 代替，如没有则注释掉这一样
#        namespace: trinax
        # 使用德勤分配的 group 代替，如没有则注释掉这一样
        #        group: trinax-public
        # 配置中心地址
        server-addr: ${NACOS:localhost}:8848
        #        server-addr: mynacos:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-dataids: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: mysentinel:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: ${NACOS:localhost}:8848
            #            server-addr: mynacos:8848
            dataId: sentinel-user-service
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: flow
#  mail:
#    host: smtp.163.com
#    username: <EMAIL>
#    password: SPLTIJVCNPCCCP
#    default-encoding: UTF-8
#    protocol: smtp
#    test-connection: true
#    properties:
#      mail:
#        smtp:
#          auth: true
#          starttls:
#            enable: true
#            required: true
#          socketFactory:
#            class: javax.net.ssl.SSLSocketFactory
#            port: 465

mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  global-config:
    db-config:
      id-type: none

management:
  health.defaults.enabled: false

  #SPLTIJVCNPCCCPFU

trina:
  phone:
    modify:
      sms:
        templateCode: SMS_464095717
      notice:
        originTemplateCode: SMS_464030773
        newTemplateCode: SMS_464065778
  sms:
    withdraw-templateCode: SMS_464085754
    market-smsCode: SMS_476750664

# id生成配置
id-generator.segments:
  user-id:
    step: 3
    limitId: 999999
    preloadNext: false

# 企业绑定消息模板配置
coBind:
  allocation:
    mainSale:
      notice: CO_BIND_ALLOCATION_MAIN_SALE_NOTICE
    sale:
      notice: CO_BIND_ALLOCATION_SALE_NOTICE
      unAuth:
        todo: CO_BIND_ALLOCATION_SALE_UN_AUTH_TODO
  auth:
    failed:
      notice: CO_BIND_AUTH_FAILED_NOTICE
      reAuthTodo: CO_BIND_AUTH_FAILED_RE_AUTH_TODO
    passed:
      notice: CO_BIND_AUTH_PASSED_NOTICE
  external:
    bind:
      new:
        todo: CO_BIND_EXTERNAL_BIND_NEW_TODO
      exist:
        todo: CO_BIND_EXTERNAL_BIND_EXIST_TODO
    cancelBind:
      notice: CO_BIND_EXTERNAL_CANCEL_BIND_NOTICE
  internal:
    bind:
      notice: CO_BIND_INTERNAL_BIND_NOTICE
    cancelBind:
      externalNotice: CO_BIND_INTERNAL_CANCEL_BIND_NOTICE_EXTERNAL
      internalNotice: CO_BIND_INTERNAL_CANCEL_BIND_NOTICE_SALE
  unbind:
    sale:
      notice: CO_BIND_UNBIND_SALE_NOTICE