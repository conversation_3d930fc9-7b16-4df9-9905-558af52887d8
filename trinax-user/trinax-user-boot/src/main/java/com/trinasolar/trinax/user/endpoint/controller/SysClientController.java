package com.trinasolar.trinax.user.endpoint.controller;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.user.api.SysClientFeign;
import com.trinasolar.trinax.user.dto.input.SysClientInfoReqDTO;
import com.trinasolar.trinax.user.dto.output.SysClientDetailResDTO;
import com.trinasolar.trinax.user.dto.output.SysClientInfoResDTO;
import com.trinasolar.trinax.user.dto.output.SysClientResDTO;
import com.trinasolar.trinax.user.service.SysClientService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/***
 * API接口应用控制类
 */
@RestController
public class SysClientController implements SysClientFeign {

    @Resource
    private SysClientService sysClientService;


    @Override
    public Result<SysClientResDTO> findClientByAppId(String appId) {
        return sysClientService.findClientByAppId(appId);
    }

    @Override
    public Result<SysClientDetailResDTO> findClientDetailByAppId(String appId) {
        return sysClientService.findClientDetail(appId);
    }

    @Override
    public Result<SysClientInfoResDTO> createClientInfo(SysClientInfoReqDTO sysClientInfoReqDTO) {
        return sysClientService.createClientInfo(sysClientInfoReqDTO);
    }
}
