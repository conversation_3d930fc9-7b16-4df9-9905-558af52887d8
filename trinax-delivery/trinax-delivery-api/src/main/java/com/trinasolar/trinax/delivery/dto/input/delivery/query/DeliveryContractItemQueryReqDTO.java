package com.trinasolar.trinax.delivery.dto.input.delivery.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class DeliveryContractItemQueryReqDTO {

    @Schema(description = "发货申请状态列表（真实状态）")
    private List<String> deliverStatuses;

    @Schema(description = "德勤通合同行主键ID列表")
    private List<String> contractBusinessItemIdList;

    @Schema(description = "编辑时，需要排除当前的状态")
    private String  deliverNo;

}
