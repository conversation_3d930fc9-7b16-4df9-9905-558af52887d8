package com.trinasolar.trinax.delivery.dto.output.delivery.list;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "发货申请列表子项")
public class DeliverListContractItemDTO {
    @Schema(description = "发货合同业务主键ID")
    String deliverContractId;

    @Schema(description = "deliverContractItemId 合同号")
    String deliverContractItemId;

    @Schema(description = "德勤通合同行主键ID")
    private String contractBusinessItemId;

    @Schema(description = "sf产品Id")
    String sfProductId;

    @Schema(description = "PBI产品ID")
    String pbiProductId;

    @Schema(description = "产品名称")
    String productName;

    @Schema(description = "产品小图")
    String productIconUrl;

    @Schema(description = "单位功率")
    Double power;

    @Schema(description = "合同数量")
    BigDecimal quantityP;

    @Schema(description = "合同瓦数 (Mw)")
    BigDecimal quantityMw;

    @Schema(description = "合同瓦数")
    BigDecimal quantityW;

    @Schema(description = "申请数量")
    Integer applyQuantityP;

    @Schema(description = "申请瓦数 (Mw)")
    BigDecimal applyQuantityMw;

    @Schema(description = "申请瓦数")
    BigDecimal applyQuantityW;

    @Schema(description = "瓦单价")
    BigDecimal unitPriceW;

    @Schema(description = "行类型：PRODUCT-产品；COMPLIMENTARY - 赠品")
    String itemType;

    String sfContractItemId;

    String sfParentItemId;

    List<DeliverListContractItemDTO> deliverListContractItemDTOList;

}
