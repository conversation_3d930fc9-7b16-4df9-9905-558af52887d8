package com.trinasolar.trinax.delivery.dto.input.delivery.save;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "保存或提交发货申请")
public class OpsSaveDeliveryReqDTO {
    @Schema(description = "deliveryNo 发货号")
    private String deliverNo;

    @Schema(description = "contracts")
    private List<OpsSaveDeliveryContract> contracts;

    @Schema(description = "当前用户id",hidden = true)
    private String currentUserId;

    @Schema(description = "当前用户名称",hidden = true)
    private String currentUserName;

    @Schema(description = "当前用户类型",hidden = true)
    private String currentUserType;

    //已经确认过的发货申请需要传以下参数
    @Schema(description = "发货申请是否已经确认过:true-已确认，false-没有确认")
    private boolean isConfirmed;

    @Schema(description = "contract,已经确认过的发货申请再次修改")
    private OpsSaveDeliveryContract contract;
}




