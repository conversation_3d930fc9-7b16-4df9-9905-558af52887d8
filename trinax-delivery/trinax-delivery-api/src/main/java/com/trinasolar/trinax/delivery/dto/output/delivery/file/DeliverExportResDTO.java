package com.trinasolar.trinax.delivery.dto.output.delivery.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "发货列表查询")
public class DeliverExportResDTO {

    @Schema(description = "发货单号")
    String deliverNo;

    @Schema(description = "发货状态")
    String deliverStatus;

    @Schema(description = "状态文本")
    String deliverStatusText;

    @Schema(description = "发货状态(伪)")
    String deliverStatusFake;

    @Schema(description = "企业Id")
    String enterpriseId;

    @Schema(description = "企业名称")
    String enterpriseName;

    @Schema(description = "签约主体：存在关联方时为关联方，否则为大商")
    String signEntity;

    @Schema(description = "资方企业Id")
    String capitalEnterpriseId;

    @Schema(description = "资方企业名称")
    String capitalEnterpriseName;

    @Schema(description = "签约客户类型")
    private String customerCategory;

    @Schema(description = "签约客户类型")
    private String customerCategoryText;

    @Schema(description = "地址信息")
    String address;

    @Schema(description = "地址Id")
    String addressId;

    @Schema(description = "联系人")
    String contactId;

    @Schema(description = "联系人名称")
    String contactName;

    @Schema(description = "收货人")
    String recipientName;

    @Schema(description = "合同号")
    String sfContractNo;

    @Schema(description = "发货申请的片数(按合同统计)")
    Integer applicationQuantity;

    @Schema(description = "发货申请的容量(按合同统计)")
    BigDecimal applicationVolumeMw;

    @Schema(description = "收货电话")
    String recipientPhone;

    @Schema(description = "发货方式")
    String deliverMethod;

    @Schema(description = "备注")
    String comment;

    @Schema(description = "发货申请创建日期")
    LocalDateTime createdTime;

    @Schema(description = "发货申请提交时间")
    LocalDateTime externalUserSubmitTime;

    @Schema(description = "发货单总功率mw")
    private BigDecimal quantityMw;

    @Schema(description = "发货单合同列表,分割")
    private String sfContractList;

    @Schema(description = "发货单总片数")
    private Integer totalQuantityP;

    @Schema(description = "退回原因")
    String returnReason;

    @Schema(description = "状态")
    String status;

    @Schema(description = "状态文本")
    String statusText;

    @Schema(description = "提交日期")
    LocalDate submitDate;

    @Schema(description = "创建人id")
    String  createdBy;

    @Schema(description = "创建人姓名")
    String  createdName;

    @Schema(description = "业务所属战区编码")
    private String bizOrganizationCode;

    @Schema(description = "业务所属战区")
    private String bizOrganizationName;

    @Schema(description = "是否包含返利")
    Boolean rebate;

    @Schema(description = "开口合同类型")
    private String openContract;

    @Schema(description = "开口合同类型")
    private String openContractText;

    @Schema(description = "销售人员id")
    private String salesInternalUserId;

    @Schema(description = "销售人员姓名")
    private String salesInternalUserName;

}
