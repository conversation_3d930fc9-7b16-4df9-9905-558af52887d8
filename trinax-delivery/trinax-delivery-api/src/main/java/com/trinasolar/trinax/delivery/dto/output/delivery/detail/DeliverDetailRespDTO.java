package com.trinasolar.trinax.delivery.dto.output.delivery.detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@Schema(description = "发货申请详情")
public class DeliverDetailRespDTO {
    @Schema(description = "发货申请单")
    String deliverNo;

    @Schema(description = "发货状态")
    String deliverStatus;

    @Schema(description = "企业号")
    String enterpriseId;

    @Schema(description = "企业名称")
    String enterpriseName;

    @Schema(description = "资方企业Id")
    String capitalEnterpriseId;

    @Schema(description = "资方企业名称")
    String capitalEnterpriseName;

    @Schema(description = "配送方式")
    String deliverMethod;

    @Schema(description = "备注")
    String comment;

    @Schema(description = "退回原因")
    String returnReason;

    @Schema(description = "联系人Id")
    String contactId;

    @Schema(description = "联系人姓名")
    String contactName;

    @Schema(description = "销售人员id")
    String salesInternalUserId;

    @Schema(description = "销售人员名称")
    String salesInternalUserName;

    @Schema(description = "发货地址")
    EnterpriseAddressRespDTO address;

    @Schema(description = "发货申请创建日期")
    LocalDateTime createdTime;

    @Schema(description = "合同列表")
    List<DeliverDetailContractDTO> contractList;

    @Schema(description = "订单类型")
    private Integer orderType;

//    @Schema(description = "是否包含返利,true:包含,false:不包含")
//    Boolean rebate;
}
