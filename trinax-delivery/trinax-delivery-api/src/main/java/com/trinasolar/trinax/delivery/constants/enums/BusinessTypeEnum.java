package com.trinasolar.trinax.delivery.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {

    BPM_APPROVAL("bpmApproval","正常发货"),
    ;

    /**
     * 状态编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    public static String getDescByCode(String code) {
        for (BusinessTypeEnum value : BusinessTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
