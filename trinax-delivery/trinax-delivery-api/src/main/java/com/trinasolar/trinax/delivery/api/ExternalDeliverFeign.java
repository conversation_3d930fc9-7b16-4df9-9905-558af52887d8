package com.trinasolar.trinax.delivery.api;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.delivery.constants.ServiceIds;
import com.trinasolar.trinax.delivery.dto.input.delivery.save.SaveDeliveryReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.submit.SubmitPartnerListReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Tag(name = "外部用户发货申请")
@FeignClient(value = ServiceIds.DELIVERY_SERVICE)
public interface ExternalDeliverFeign {

    @Operation(summary = "外部用户 详情 页提交发货申请detail")
    @PostMapping("/delivery/submit/partner/detail")
    Result<Object> submitPartnerDetailDelivery(@RequestBody SaveDeliveryReqDTO req);

    @Operation(summary = "外部用户 列表 提交发货申请 list")
    @PostMapping("/delivery/submit/partner/list")
    Result<Object> submitPartnerListDelivery(@RequestBody SubmitPartnerListReqDTO req);


}
