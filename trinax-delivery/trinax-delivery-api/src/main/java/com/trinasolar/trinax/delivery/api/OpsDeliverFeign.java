package com.trinasolar.trinax.delivery.api;

import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.delivery.constants.ServiceIds;
import com.trinasolar.trinax.delivery.dto.input.delivery.cancel.CancelDeliverAfterConfirmReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.check.EnterpriseBalanceCheckReq;
import com.trinasolar.trinax.delivery.dto.input.delivery.confirm.ConfirmDeliverOpsReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverOpsDetailReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverOpsListReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverReleaseReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.report.DeliverTrendReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.reverse.ReverseDeliverReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.save.OpsSaveDeliveryReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.submit.ApprovalStatusReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.submit.DeliverRetryApprovalReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.submit.DeliverRetryReqDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.DeliverContractResDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.check.EnterpriseBalanceCheckDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.detail.DeliverDetailOpsRespDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.detail.DeliverInfoOpsRespDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.file.DeliverDetailResDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverOpsListRespDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverReleaseResDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.report.DeliverTrendResDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@Tag(name = "运营发货申请")
@FeignClient(value = ServiceIds.DELIVERY_SERVICE)
public interface OpsDeliverFeign {

    @Operation(summary = "运营 查询发货申请详情 detail")
    @PostMapping("/delivery/ops/detail")
    Result<PageResponse<DeliverDetailOpsRespDTO>> detailDeliverOps(@RequestBody PageRequest<DeliverOpsDetailReqDTO> req);

    @Operation(summary = "运营 通过 deliverNo 查询发货详情")
    @PostMapping("/delivery/ops/deliverDetailByNo")
    Result<List<DeliverInfoOpsRespDTO>> deliverDetailByNo(@RequestBody DeliverOpsDetailReqDTO req);


    @Operation(summary = "运营 查询发货申请列表 list")
    @PostMapping("/delivery/ops/list")
    Result<PageResponse<DeliverOpsListRespDTO>> listDeliverOps(@RequestBody PageRequest<DeliverOpsListReqDTO> req);

    @Operation(summary = "运营 导出 excel 查询发货申请列表 list")
    @PostMapping("/delivery/ops/exportList")
    Result<List<DeliverDetailResDTO>> exportList(@RequestBody DeliverOpsListReqDTO req);

    @Operation(summary = "退回发货申请")
    @PostMapping("/delivery/ops/reverse")
    Result<Object> reverseDeliver(@RequestBody ReverseDeliverReqDTO req);


    @Operation(summary = "确认发货申请")
    @PostMapping("/delivery/ops/confirm")
    Result<Object> confirmDeliver(@RequestBody ConfirmDeliverOpsReqDTO req);


    @Operation(summary = "查询发货合同业务信息")
    @PostMapping("/delivery/ops/qryDeliverInfo")
    Result<List<DeliverContractResDTO>> qryDeliverInfo(@RequestBody DeliverOpsDetailReqDTO req);

    @Operation(summary = "管理端-运营保存发货申请")
    @PostMapping("/delivery/ops/saveDeliver")
    Result<Object> saveDeliver(@RequestBody OpsSaveDeliveryReqDTO req);

    @Operation(summary = "管理端-运营保存发货申请(已确认)")
    @PostMapping("/delivery/ops/saveDeliverConfirmed")
    Result<Object> saveDeliverConfirmed(@RequestBody OpsSaveDeliveryReqDTO req);

    @Operation(summary = "放货申请分页查询")
    @PostMapping("/delivery/ops/qryDeliverRelease")
    Result<PageResponse<DeliverReleaseResDTO>> qryDeliverRelease(@RequestBody PageRequest<DeliverReleaseReqDTO> req);

    @Operation(summary = "放货申请列表查询")
    @PostMapping("/delivery/ops/qryDeliverReleaseList")
    Result<List<DeliverReleaseResDTO>> qryDeliverReleaseList(@RequestBody DeliverReleaseReqDTO req);

    @Operation(summary = "放货同步 sf dr")
    @PostMapping("/delivery/ops/retrySyncDr")
    Result<String> retrySyncDr(@RequestBody DeliverRetryReqDTO req);

    @Operation(summary = "放货重新提交 bpm 审批")
    @PostMapping("/delivery/ops/retrySubmitApproval")
    Result<String> retrySubmitApproval(@RequestBody @Valid DeliverRetryApprovalReqDTO req);

    @Operation(summary = "放货重新提交 bpm 审批")
    @PostMapping("/delivery/ops/approvalStatusAccess")
    Result<String> approvalStatusAccess(@RequestBody ApprovalStatusReqDTO req);

    @Operation(summary = "发货趋势图")
    @PostMapping("/delivery/ops/deliverTrend")
    Result<List<DeliverTrendResDTO>> deliverTrend(@RequestBody DeliverTrendReqDTO req);

    @Operation(summary = "check供应商预付款余额")
    @PostMapping("/delivery/ops/balance/check")
    Result<EnterpriseBalanceCheckDTO> balanceCheck(@RequestBody EnterpriseBalanceCheckReq enterpriseBalanceCheckReq);

    @Operation(summary = "重试提交分销余额")
    @PostMapping("/delivery/ops/retrySubmitAmount")
    Result retrySubmitDistributeAmount(@RequestParam String deliverContractId);

    @Operation(summary = "取消发货申请")
    @PostMapping("/delivery/ops/cancelDeliver")
    Result<Object> cancelDeliver(@RequestBody CancelDeliverAfterConfirmReqDTO reqDTO);
}
