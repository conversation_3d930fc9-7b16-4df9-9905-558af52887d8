package com.trinasolar.trinax.delivery.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发货申请场景类型编码，描述枚举
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum DeliverySituationEnum {

    //发货申请场景
    DR_EXTERNAL_CREATE("DR_EXTERNAL_CREATE", "生态伙伴/一般客户创建发货申请"),
    DR_INTERNAL_CONFIRM("DR_INTERNAL_CONFIRM", "分销销售确认发货申请"),
    DR_INTERNAL_CONFIRM_SELF_CREATE("DR_INTERNAL_CONFIRM_SELF_CREATE", "分销销售确认自建发货申请"),
    DR_EXTERNAL_CANCEL_WAIT_SALE_CONFIRM("DR_EXTERNAL_CANCEL_WAIT_SALE_CONFIRM", "生态伙伴/一般客户取消已提交(待销售确认)的发货申请"),
    DR_EXTERNAL_CANCEL_WAIT_OPERATION_CONFIRM("DR_EXTERNAL_CANCEL_WAIT_OPERATION_CONFIRM", "生态伙伴/一般客户取消已提交(待运营确认)的发货申请"),
    DR_INTERNAL_CANCEL_WAIT_SALE_CONFIRM("DR_INTERNAL_CANCEL_WAIT_SALE_CONFIRM", "分销销售取消已提交(待销售确认)的发货申请"),
    DR_INTERNAL_CANCEL_WAIT_OPERATION_CONFIRM("DR_INTERNAL_CANCEL_WAIT_OPERATION_CONFIRM", "分销销售取消已提交(待运营确认)的发货申请"),
    DR_INTERNAL_OPERATION_CONFIRM("DR_INTERNAL_OPERATION_CONFIRM", "运营人员确认发货申请"),
    DR_INTERNAL_OPERATION_BACK("DR_INTERNAL_OPERATION_BACK", "运营人员退回发货申请"),
    DR_CLEAN_CACHE("DR_CLEAN_CACHE", "清理缓存"),
    ;


    private final String code;
    private final String desc;

    public static String getDescByCode(String code) {
        for (DeliverySituationEnum value : DeliverySituationEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

}
