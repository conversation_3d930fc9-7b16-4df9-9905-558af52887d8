package com.trinasolar.trinax.delivery.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ApprovalTypeEnum {

    NORMAL_SHIPPING("Normal shipping", "正常发货", "Normal"),
    SPECIAL_APPROVAL("Special approval", "特批", "Special"),
    ADVANCE_SHIPPING("Advance shipping", "提前发货", "Advance"),
    ;

    /**
     * 状态编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 描述
     */
    private final String sfCode;

    public static String getDescByCode(String code) {
        for (ApprovalTypeEnum value : ApprovalTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

    public static String getSfCodeByCode(String code) {
        for (ApprovalTypeEnum value : ApprovalTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getSfCode();
            }
        }
        return "";
    }
}
