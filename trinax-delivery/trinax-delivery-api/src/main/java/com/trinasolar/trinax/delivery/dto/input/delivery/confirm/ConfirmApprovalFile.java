package com.trinasolar.trinax.delivery.dto.input.delivery.confirm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ConfirmApprovalFile {

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "文件id")
    private String fileId;

    @Schema(description = "文件大小")
    private String fileSize;

    @Schema(description = "文件类型")
    private String fileType;
}
