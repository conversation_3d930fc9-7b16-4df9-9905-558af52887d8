package com.trinasolar.trinax.delivery.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BpmStatusEnum {
    APPROVED("Approved", "审批通过"),
    REJECT("Reject", "审批拒绝"),
    APPROVING("Approving", "审批中"),
    DRAFT("Draft", "草稿"),
    FAIL("Submission failed", "审批提交失败"),
    NOT_SYNCING("Not syncing", "待同步"),

//    Draft，Pending Approval，Approved，Processing，Completed，Cancelled

    ;

    /**
     * 状态编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    public static String getDescByCode(String code) {
        for (BpmStatusEnum value : BpmStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
