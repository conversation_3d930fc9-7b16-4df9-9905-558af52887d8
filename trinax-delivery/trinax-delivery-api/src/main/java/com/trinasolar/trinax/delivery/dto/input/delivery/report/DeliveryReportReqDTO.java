package com.trinasolar.trinax.delivery.dto.input.delivery.report;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "发货申请报表查询入参")
public class DeliveryReportReqDTO {

    @Schema(description = "接口类型：新增-1；累计-2",hidden = true)
    private String apiType;

    @Schema(description = "开始时间",hidden = true)
    private LocalDateTime startTime;

    @Schema(description = "结束时间",hidden = true)
    private LocalDateTime endTime;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "发货申请状态")
    private String deliverStatus;

    @Schema(description = "客户类别：PARTNER，BUSINESS")
    private String customerCategory;

    @Schema(description = "企业id列表",hidden = true)
    private List<String> enterpriseIdList;
}
