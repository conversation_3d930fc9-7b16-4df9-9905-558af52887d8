package com.trinasolar.trinax.delivery.dto.input.delivery.cancel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "删除发货行")
public class CleanItemReqDTO {

    @Schema(description = "发货行 id")
    @NotNull(message = "发货行 id 不能为空")
    private List<String> itemIds;

    @Schema(description = "发货单号")
    @NotBlank(message = "发货单 id 不能为空")
    private String deliverNo;

    @Schema(hidden = true)
    private String currentUserId;

    @Schema(hidden = true)
    private String currentUserName;

    @Schema(hidden = true)
    private String currentUserType;
}
