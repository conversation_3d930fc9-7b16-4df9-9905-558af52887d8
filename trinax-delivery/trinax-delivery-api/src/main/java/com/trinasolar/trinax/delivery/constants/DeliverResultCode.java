package com.trinasolar.trinax.delivery.constants;

import com.google.common.collect.Maps;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.dto.output.ResultCodeArtist;

import java.util.Map;

public class DeliverResultCode extends ResultCode {
    protected static Map<String, ResultCodeArtist> valueMap = Maps.newHashMap();

    public static ResultCodeArtist REQ_QUANTITY_ERROR = new ResultCodeArtist(false, "REQ_QUANTITY_ERROR",
            "申请发货数量超过剩余发货数量");

    public static ResultCodeArtist NON_COUNTERSIGNED_ERROR = new ResultCodeArtist(false, "NON_COUNTERSIGNED_ERROR",
            "以下合同状态已发生变更，无法提交发货申请: ");

    public static ResultCodeArtist DELIVER_CONFIRMED_ERROR = new ResultCodeArtist(false, "DELIVER_CONFIRMED_ERROR",
            "该发货申请已被确认，不能取消");

    public static ResultCodeArtist DELIVER_CANCEL_ERROR = new ResultCodeArtist(false, "DELIVER_CANCEL_ERROR",
            "该发货申请已被取消");

    public static ResultCodeArtist DELIVER_BUSY_ERROR = new ResultCodeArtist(false, "DELIVER_BUSY_ERROR",
            "已有其他用户在更改此发货申请，请稍后再试");

    public static ResultCodeArtist DELIVER_DETAIL_LOST_ERROR = new ResultCodeArtist(false, "DELIVER_DETAIL_LOST_ERROR",
            "发货申请行缺失");

    public static ResultCodeArtist DELIVER_DETAIL_QUANTITY_ERROR = new ResultCodeArtist(false, "DELIVER_DETAIL_QUANTITY_ERROR",
            "发货申请超发校验");
    public static ResultCodeArtist SALESFORCE_UN_COUNTERSIGNED_ERROR = new ResultCodeArtist(false, "SALESFORCE_UN_COUNTERSIGNED_ERROR",
            "salesforce合同未双签");

    public static ResultCodeArtist ORDER_TYPE_MAP_ERROR = new ResultCodeArtist(false, "ORDER_TYPE_MAP_ERROR",
            "合同类型和发货类型不匹配 ");



    @Override
    protected void setValueMap() {
        valueMap.put(REQ_QUANTITY_ERROR.getCode(), REQ_QUANTITY_ERROR);
        valueMap.put(NON_COUNTERSIGNED_ERROR.getCode(), NON_COUNTERSIGNED_ERROR);
        valueMap.put(DELIVER_CONFIRMED_ERROR.getCode(), DELIVER_CONFIRMED_ERROR);
        valueMap.put(DELIVER_CANCEL_ERROR.getCode(), DELIVER_CANCEL_ERROR);
    }
}
