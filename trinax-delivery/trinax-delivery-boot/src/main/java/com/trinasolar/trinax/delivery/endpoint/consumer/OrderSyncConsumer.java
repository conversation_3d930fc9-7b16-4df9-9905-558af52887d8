package com.trinasolar.trinax.delivery.endpoint.consumer;

import com.trinasolar.trinax.delivery.constants.DeliveryConstant;
import com.trinasolar.trinax.delivery.dto.mq.delivery.OrderSyncMqDTO;
import com.trinasolar.trinax.delivery.service.DeliverService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 11/17/2023
 * @description
 */

@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(topic = DeliveryConstant.ORDER_MESSAGE_TOPIC,
        consumerGroup = "Delivery_" + DeliveryConstant.ORDER_MESSAGE_TOPIC,
        selectorExpression = "*")
public class OrderSyncConsumer implements RocketMQListener<OrderSyncMqDTO> {
    private final DeliverService deliverService;

    @Override
    public void onMessage(OrderSyncMqDTO req) {
        log.info("订单同步消费消息,入参：{}", req);
        deliverService.sendOrderToSf(req);
    }
}
