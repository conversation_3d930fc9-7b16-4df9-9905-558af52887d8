server:
  port: 8882
spring:
#  application:
#    # 应用名称
#    name: trans-service
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************************
    username: root
    password: 123456@2025
#    password: docker123456##
    hikari:
      minimum-idle: 100
      maximum-pool-size: 200
      idle-timeout: 10000
      max-lifetime: 1800000
      connection-timeout: 30000
#  redis:
#    cluster.nods: clustercfg.dev-membership-redis-cluster.q3bos4.cnw1.cache.amazonaws.com.cn:6379
#    ssl: true
#    password: dev123321crm3215433#2021
#    master-connection-minimum-idle-size: 6
#    slave-connection-minimum-idle-size: 6
#    master-connection-pool-size: 10
#    slave-connection-pool-size: 10
#    connect-timeout: 30000
#    timeout: 30000
  redis:
    # redis数据库索引（默认为0），我们使用索引为3的数据库，避免和其他数据库冲突
    database: 0
    # redis服务器地址（默认为localhost）
    host: ${REDIS_HOST:*************}
    # redis端口（默认为6379）
    port: ${REDIS_PORT:6379}
    # redis访问密码（默认为空）
#    password: ${REDIS_PASSWORD:Trina@123}
    # redis连接超时时间（单位为毫秒）
    timeout: 30000

#  kafka:
#    bootstrap-servers: b-1.dev-membership.qiritt.c4.kafka.cn-northwest-1.amazonaws.com.cn:9092,b-2.dev-membership.qiritt.c4.kafka.cn-northwest-1.amazonaws.com.cn:9092,b-3.dev-membership.qiritt.c4.kafka.cn-northwest-1.amazonaws.com.cn:9092
#    producer:
#      key-serializer: org.apache.kafka.common.serialization.StringSerializer
#      value-serializer: org.apache.kafka.common.serialization.StringSerializer
#      retries: 3
#    consumer:
#      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      group-id: delivery-service-default

#  kafka:
#    bootstrap-servers: kafka0:9092;kafka1:9092;kafka2:9092
#    producer:
#      key-serializer: org.apache.kafka.common.serialization.StringSerializer
#      value-serializer: org.apache.kafka.common.serialization.StringSerializer
#      retries: 3
#    consumer:
#      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      group-id: delivery-service-default

xxl:
  job:
    adminAddress: ${XXLJOB_ADMINADDRESS:http://localhost:9997/xxl-job-admin}
    executorName: trinax-delivery-service
    ip:
    port: 12000
    accessToken:
    logPath: /tmp/xxl_log
    logRetentionDays: -1

#日志上报环境地址
logging:
  collector:
    #dev
    address: localhost:8088

rocketmq:
  name-server: ${ROCKETMQ_HOST:*************:9876}
  producer:
    group: delivery
  consumer:
    group:
    topic:

#同步Sf 订单 每个环境不一样
deliver:
  createOrder:
    recordTypeId: 0122I000000s5S6QAI
  createDeliver:
    on-off: false
    name: 压测创建发货申请，不走并发锁
  caffeineCache:
    on-off: true
  openSystemOrgCode: TRINA_CN_SALES_HUANAN,TRINA_CN_SALES_HUABEI
#德勤域名
trinax:
  fileHost: https://trinaxfile-dev.deloitte.com

