server:
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${DATASOURCE_URL}
    username: ${DATASOURCE_USERNAME}
    password: ${DATASOURCE_PASSWORD}
    hikari:
      minimum-idle: 10
      maximum-pool-size: 20
      idle-timeout: 10000
      max-lifetime: 1800000
      connection-timeout: 30000
  redis:
    # redis数据库索引（默认为0），我们使用索引为3的数据库，避免和其他数据库冲突
    database: 0
    # redis服务器地址（默认为localhost）
    host: ${REDIS_HOST}
    # redis端口（默认为6379）
    port: ${REDIS_PORT}
    # redis访问密码（默认为空）
    password: ${REDIS_PASSWORD}
    # redis连接超时时间（单位为毫秒）
    timeout: 30000

xxl:
  job:
    adminAddress: ${XXLJOB_ADMINADDRESS}
    executorName: trinax-delivery-service
    ip:
    port: 12000
    accessToken:
    logPath: /tmp/xxl_log
    logRetentionDays: -1


#日志上报环境地址
logging:
  collector:
    address: ${LOGGING_COLLECTOR_ADDRESS}


rocketmq:
  name-server: ${ROCKETMQ_HOST}
  producer:
    group: delivery
  consumer:
    group:
    topic:

#同步Sf 订单 每个环境不一样
deliver:
  createDeliver:
    on-off: false
    name: 压测创建发货申请，不走并发锁
  caffeineCache:
    on-off: false
#德勤域名
trinax:
  fileHost: https://trinaxfile-dev.deloitte.com