# Tomcat
server:
  port: 80
#日志组件
trinasolar:
  team: dtt-team
  project:
    name: trinax-delivery-service
# Spring
spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  application:
    # 应用名称
    name: trinax-delivery-service
    version: 1.0
  profiles:
    # 环境配置
    active: ${ACTIVE_PROFILE:local}
#    active: dev
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      discovery:
        # 使用德勤分配的 namespace 代替，如没有则注释掉这一样
        namespace: trinax
        # 服务注册地址
        server-addr: ${NACOS:localhost}:8848
#        server-addr: mynacos:8848
      config:
       # 使用德勤分配的 namespace 代替，如没有则注释掉这一样
        namespace: trinax
         # 使用德勤分配的 group 代替，如没有则注释掉这一样
#        group: trinax-public
        # 配置中心地址
        server-addr: ${NACOS:localhost}:8848
#        server-addr: mynacos:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-dataids: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: mysentinel:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: ${NACOS:localhost}:8848
#            server-addr: mynacos:8848
            dataId: sentinel-delivery-service
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: flow
#  mail:
#    host: smtp.163.com
#    username: <EMAIL>
#    password: SPLTIJVCNPCCCP
#    default-encoding: UTF-8
#    protocol: smtp
#    test-connection: true
#    properties:
#      mail:
#        smtp:
#          auth: true
#          starttls:
#            enable: true
#            required: true
#          socketFactory:
#            class: javax.net.ssl.SSLSocketFactory
#            port: 465

mybatis-plus:

  mapper-locations: classpath:/mapper/**/*.xml
  global-config:
    db-config:
      id-type: none
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
management:
  health.defaults.enabled: false

  #SPLTIJVCNPCCCPFU
message:
  template:
    externalCreateTodo: DR_EXTERNAL_CREATE_TODO
    externalCreatePush: DR_EXTERNAL_CREATE_JPUSH
    internalConfirmNotice: DR_INTERNAL_CONFIRM_NOTICE
    internalConfirmTodo: DR_INTERNAL_CONFIRM_TODO
    internalConfirmSelfCreateNotice: DR_INTERNAL_CONFIRM_SELF_CREATE_NOTICE
    externalCancelInternalUnConfirmNotice: DR_EXTERNAL_CANCEL_WAIT_SALE_CONFIRM_NOTICE
    externalCancelOperationUnConfirmNoticeSale: DR_EXTERNAL_CANCEL_WAIT_OPERATION_CONFIRM_INTERNAL_NOTICE
    externalCancelOperationUnConfirmNoticeOperation: DR_EXTERNAL_CANCEL_WAIT_OPERATION_CONFIRM_OPERATION_NOTICE
    internalCancelInternalUnConfirmNotice: DR_INTERNAL_CANCEL_WAIT_SALE_CONFIRM_NOTICE
    internalCancelOperationUnConfirmNoticeExternal: DR_INTERNAL_CANCEL_WAIT_OPERATION_CONFIRM_INTERNAL_NOTICE
    internalCancelOperationUnConfirmNoticeOperation: DR_INTERNAL_CANCEL_WAIT_OPERATION_CONFIRM_OPERATION_NOTICCE
    operationConfirmNoticeExternal: DR_OPERATION_CONFIRM_EXTERNAL_NOTICE
    operationConfirmNoticeInternal: DR_OPERATION_CONFIRM_INTERNAL_NOTICE
    operationCancelNoticePush: DR_OPERATION_BACK_JPUSH
    operationCancelNoticeTodo: DR_OPERATION_BACK_TODO

id-generator.segments:
  deliveryContractItem:
    step: 1
    limitId: 999999
    preloadNext: false
  deliveryContract:
    step: 1
    limitId: 999999
    preloadNext: false
  delivery:
    step: 1
    limitId: 999999
    preloadNext: false
  deliverFileId:
    step: 1
    limitId: 999999
    preloadNext: false