<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.trinasolar</groupId>
		<artifactId>trinax-delivery-root</artifactId>
		<version>0.0.4-SNAPSHOT</version>
	</parent>

	<packaging>jar</packaging>
	<artifactId>trinax-delivery-boot</artifactId>
	<name>trinax-delivery-boot</name>
	<description>Demo project for Spring Boot</description>

	<properties>
		<maven.deploy.skip>true</maven.deploy.skip>
		<java.version>8</java.version>
		<dtt-xxljob.version>0.0.2.RELEASE</dtt-xxljob.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.trinasolar</groupId>
			<artifactId>trinax-delivery-domain</artifactId>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>com.trinasolar.lapetus.log</groupId>-->
<!--			<artifactId>log2-spring-boot-starter</artifactId>-->
<!--		</dependency>-->

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-loadbalancer</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-bootstrap</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
		<dependency>
			<groupId>dtt.asset</groupId>
			<artifactId>dtt-framework-xxljob</artifactId>
			<version>${dtt-xxljob.version}</version>
		</dependency>
	</dependencies>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-maven-plugin</artifactId>
					<version>${spring-boot-dependencies.version}</version>
					<executions>
						<execution>
							<goals>
								<goal>repackage</goal>
							</goals>
						</execution>
					</executions>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>${plugin.compile.version}</version>

				</plugin>
			</plugins>
		</pluginManagement>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<forceJavacCompilerUse>true</forceJavacCompilerUse>
				</configuration>
				<!--configuration>
                <release>11</release>
                </configuration-->
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring-boot-dependencies.version}</version>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<!--<executions>-->
				<!--<execution>-->
				<!--<id>package</id>-->
				<!--<phase>package</phase>-->
				<!--<configuration>-->
				<!--<target>-->
				<!--<echo message="*******************install*******************" />-->
				<!--<mkdir dir="${basedir}/target/classes" />-->
				<!--<copy todir="../compose/${project.artifactId}" overwrite="true">-->
				<!--<fileset dir="${project.build.directory}"-->
				<!--erroronmissingdir="false">-->
				<!--<include name="*.jar" />-->
				<!--</fileset>-->
				<!--</copy>-->

				<!--</target>-->
				<!--</configuration>-->
				<!--<goals>-->
				<!--<goal>run</goal>-->
				<!--</goals>-->
				<!--</execution>-->
				<!--<execution>-->
				<!--<id>clean</id>-->
				<!--<phase>clean</phase>-->
				<!--<configuration>-->
				<!--<target>-->
				<!--<echo message="*******************clean*******************" />-->
				<!--<delete dir="target" />-->
				<!--<mkdir dir="${basedir}/target/classes" />-->
				<!--</target>-->
				<!--</configuration>-->
				<!--<goals>-->
				<!--<goal>run</goal>-->
				<!--</goals>-->
				<!--</execution>-->
				<!--</executions>-->
			</plugin>
		</plugins>
		<finalName>${project.artifactId}</finalName>
	</build>

</project>
