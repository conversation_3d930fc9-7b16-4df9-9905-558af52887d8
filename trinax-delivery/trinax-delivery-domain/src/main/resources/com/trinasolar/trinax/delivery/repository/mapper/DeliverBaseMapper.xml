<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trinasolar.trinax.delivery.repository.mapper.DeliverBaseMapper">


    <insert id="saveDeliverInfo">
        <include refid="saveDeliver"/>
        <include refid="saveDeliverContract"/>
        <include refid="saveDeliverItem"/>
    </insert>

    <sql id="saveDeliver">
        <if test="deliver != null ">
            insert into deliver (deliver_no, enterprise_id, enterprise_name, capital_enterprise_id,
            capital_enterprise_name, incoterm, bill_to_address_id, quantity_mw, total_quantity_p, comment,
            deliver_status, deliver_status_false, sf_contract_list,
            external_user_id, external_user_submit_time, sales_internal_user_id, sales_confirm_time,
            operation_internal_user_id, operation_confirm_time, operation_return_reason, operation_return_user_id,
            operation_return_time, is_deleted, created_by, created_name, created_time, updated_by, updated_name,
            updated_time, biz_organization_code,sales_internal_user_name,operation_internal_user_name,
            origin_external_user_id,origin_sales_internal_user_id,origin_operation_internal_user_id,customer_category,order_type)
            values (
            #{deliver.deliverNo},
            #{deliver.enterpriseId},
            #{deliver.enterpriseName},
            #{deliver.capitalEnterpriseId},
            #{deliver.capitalEnterpriseName},
            #{deliver.incoterm},
            #{deliver.billToAddressId},
            #{deliver.quantityMw},
            #{deliver.totalQuantityP},
            #{deliver.comment},
            #{deliver.deliverStatus},
            #{deliver.deliverStatusFalse},
            #{deliver.sfContractList},
            #{deliver.externalUserId},
            #{deliver.externalUserSubmitTime},
            #{deliver.salesInternalUserId},
            #{deliver.salesConfirmTime},
            #{deliver.operationInternalUserId},
            #{deliver.operationConfirmTime},
            #{deliver.operationReturnReason},
            #{deliver.operationReturnUserId},
            #{deliver.operationReturnTime},
            #{deliver.isDeleted},
            #{deliver.createdBy},
            #{deliver.createdName},
            #{deliver.createdTime},
            #{deliver.updatedBy},
            #{deliver.updatedName},
            #{deliver.updatedTime},
            #{deliver.bizOrganizationCode},
            #{deliver.salesInternalUserName},
            #{deliver.operationInternalUserName},
            #{deliver.originExternalUserId},
            #{deliver.originSalesInternalUserId},
            #{deliver.originOperationInternalUserId},
            #{deliver.customerCategory},
            #{deliver.orderType})
            ;
        </if>
    </sql>

    <sql id="saveDeliverContract">
        <if test="contractList != null ">
            insert into deliver_contract(deliver_contract_id, deliver_no, contract_id, sf_contract_no, sf_contract_id,
            status, order_type_name, order_type_id, currency, sub_region_code, arrival_city_code, arrival_country_code,
            arrival_province_code, marketing_account_id, price_book_id, opportunity_id, incoterm,
            channel, order_start_date, scheduled_delivery_date, counter_signed_date, application, beneficiary_bank_id,
            country_of_installation, destination_port_id, loading_port_id, bill_to_address_id, ship_to_address_id,
            tax_classification_id, odm, total_volume_mw, is_deleted, created_by, created_name, created_time, updated_by,
            updated_name,
            updated_time,sf_record_id,application_quantity,application_volume_mw,rebate,is_open_contract,open_contract
            )
            values
            <foreach collection="contractList" separator="," item="contract">
                (
                #{contract.deliverContractId},#{contract.deliverNo},#{contract.contractId},
                #{contract.sfContractNo},#{contract.sfContractId},#{contract.status},
                #{contract.orderTypeName},#{contract.orderTypeId},#{contract.currency},
                #{contract.subRegionCode},#{contract.arrivalCityCode},#{contract.arrivalCountryCode},
                #{contract.arrivalProvinceCode},#{contract.marketingAccountId},#{contract.priceBookId},
                #{contract.opportunityId},#{contract.incoterm},
                #{contract.channel},#{contract.orderStartDate},#{contract.scheduledDeliveryDate},
                #{contract.counterSignedDate},#{contract.application},#{contract.beneficiaryBankId},
                #{contract.countryOfInstallation},#{contract.destinationPortId},#{contract.loadingPortId},
                #{contract.billToAddressId},#{contract.shipToAddressId},#{contract.taxClassificationId},
                #{contract.odm},#{contract.totalVolumeMW},#{contract.isDeleted},#{contract.createdBy},
                #{contract.createdName},#{contract.createdTime},#{contract.updatedBy},#{contract.updatedName},
                #{contract.updatedTime},
                #{contract.sfRecordId},#{contract.applicationQuantity},#{contract.applicationVolumeMw},#{contract.rebate},
                #{contract.isOpenContract},#{contract.openContract}
                )
            </foreach>
            ;
        </if>
    </sql>

    <sql id="saveDeliverItem">
        <if test="itemList != null ">
            insert into deliver_contract_item(
            deliver_contract_item_id, deliver_contract_id, deliver_no, sf_contract_id, contract_business_item_id,
            sf_product_id, sf_product_name, pbi_product_id, price_book_id, plug_connector, installation, order_type_id,
            cable_length, cable_length_positive, cable_Length_cathode, power, expected_deliver_date, unit_price_w,
            notes, from_product_editor, quantity_mw, quantity_w, quantity_p, item_type, is_deleted, created_by,
            created_name, created_time, updated_by, updated_name, updated_time, erp_order_type_id,sf_contract_item_id,
            currency
            )
            values
            <foreach collection="itemList" separator="," item="item">
                (
                #{item.deliverContractItemId},#{item.deliverContractId},#{item.deliverNo},
                #{item.sfContractId},#{item.contractBusinessItemId},#{item.sfProductId},
                #{item.sfProductName},#{item.pbiProductId},#{item.priceBookId},#{item.plugConnector},
                #{item.installation},#{item.orderTypeId},#{item.cableLength},#{item.cableLengthPositive},
                #{item.cableLengthCathode},#{item.power},#{item.expectedDeliverDate},#{item.unitPriceW},
                #{item.notes},#{item.fromProductEditor},#{item.quantityMw},#{item.quantityW},#{item.quantityP},
                #{item.itemType},#{item.isDeleted},#{item.createdBy},#{item.createdName},#{item.createdTime},
                #{item.updatedBy},#{item.updatedName},#{item.updatedTime},#{item.erpOrderTypeId},#{item.sfContractItemId},
                #{item.currency}
                )
            </foreach>
            ;
        </if>
    </sql>

</mapper>
