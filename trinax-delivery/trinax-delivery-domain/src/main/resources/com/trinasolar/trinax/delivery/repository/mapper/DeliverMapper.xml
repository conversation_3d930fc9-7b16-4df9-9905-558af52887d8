<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trinasolar.trinax.delivery.repository.mapper.DeliverMapper">
    <resultMap id="BaseResultMap" type="com.trinasolar.trinax.delivery.repository.domain.DeliverPO">
        <!--@mbg.generated-->
        <!--@Table deliver-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="deliver_no" jdbcType="VARCHAR" property="deliverNo"/>
        <result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId"/>
        <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName"/>
        <result column="capital_enterprise_id" jdbcType="VARCHAR" property="capitalEnterpriseId"/>
        <result column="capital_enterprise_name" jdbcType="VARCHAR" property="capitalEnterpriseName"/>
        <result column="incoterm" jdbcType="VARCHAR" property="incoterm"/>
        <result column="bill_to_address_id" jdbcType="VARCHAR" property="billToAddressId"/>
        <result column="comment" jdbcType="VARCHAR" property="comment"/>
        <result column="deliver_status" jdbcType="VARCHAR" property="deliverStatus"/>
        <result column="deliver_status_false" jdbcType="VARCHAR" property="deliverStatusFalse"/>
        <result column="external_user_id" jdbcType="VARCHAR" property="externalUserId"/>
        <result column="external_user_submit_time" jdbcType="TIMESTAMP" property="externalUserSubmitTime"/>
        <result column="sales_internal_user_id" jdbcType="VARCHAR" property="salesInternalUserId"/>
        <result column="sales_confirm_time" jdbcType="TIMESTAMP" property="salesConfirmTime"/>
        <result column="operation_internal_user_id" jdbcType="VARCHAR" property="operationInternalUserId"/>
        <result column="operation_confirm_time" jdbcType="TIMESTAMP" property="operationConfirmTime"/>
        <result column="operation_return_reason" jdbcType="VARCHAR" property="operationReturnReason"/>
        <result column="operation_return_user_id" jdbcType="VARCHAR" property="operationReturnUserId"/>
        <result column="operation_return_time" jdbcType="TIMESTAMP" property="operationReturnTime"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_name" jdbcType="VARCHAR" property="createdName"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="biz_organization_code" jdbcType="VARCHAR" property="bizOrganizationCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        deliver_no,
        enterprise_id,
        enterprise_name,
        capital_enterprise_id,
        capital_enterprise_name,
        incoterm,
        bill_to_address_id,
        `comment`,
        deliver_status,
        deliver_status_false,
        external_user_id,
        external_user_submit_time,
        sales_internal_user_id,
        sales_confirm_time,
        operation_internal_user_id,
        operation_confirm_time,
        operation_return_reason,
        operation_return_user_id,
        operation_return_time,
        is_deleted,
        created_by,
        created_name,
        created_time,
        updated_by,
        updated_name,
        updated_time,
        biz_organization_code
    </sql>

    <sql id="authPara">
        <choose>
            <!--        SALES 销售-->
            <when test="para != null and para.sysUserTypeEnum == 'SALES'and para.salesUserList.size() != 0">
                AND ((d.sales_internal_user_id, d.biz_organization_code) in
                <foreach collection="para.salesUserList" separator="," item="item" open="(" close=")">
                    (#{item.userId}, #{item.organizationCode})
                </foreach>
                OR d.created_by = #{req.userId})
            </when>

            <!--        OPERATION 运营-->
            <when test="para != null and para.sysUserTypeEnum == 'OPERATION'and para.operationUserIdList.size() != 0">
                AND (d.operation_internal_user_id in
                <foreach collection="para.operationUserIdList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
                OR d.operation_internal_user_id = #{req.userId})
            </when>
            <!--        COMMON 常规-->
            <when test="para != null and para.sysUserTypeEnum == 'COMMON' and para.commonOrganizationCodes.size() != 0">
                AND d.biz_organization_code in
                <foreach collection="para.commonOrganizationCodes" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </when>

            <when test="req.commonUserIdList != null and req.commonUserIdList.size() > 0">
                and (d.sales_internal_user_id in
                <foreach item="item" collection="req.commonUserIdList" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or
                d.operation_internal_user_id in
                <foreach item="item" collection="req.commonUserIdList" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </when>
            <when test="para != null and para.sysUserTypeEnum == 'SERVICE'">
                AND
                d
                .
                biz_organization_code
                =
                'NOT_EXIST'
            </when>
        </choose>
    </sql>

    <sql id="searchCondition">
        <if test="req.searchString != null  and req.searchString != ''">
            AND (d.enterprise_name like CONCAT('%', #{req.searchString}, '%')
            OR d.deliver_no like CONCAT('%', #{req.searchString}, '%')
            OR d.created_time like CONCAT('%', #{req.searchString}, '%')
            OR d.deliver_no like CONCAT('%', #{req.searchString}, '%')
            OR d.capital_enterprise_name like CONCAT('%', #{req.searchString}, '%')
            OR EXISTS (select 1
            from deliver_contract dc
            where d.deliver_no = dc.deliver_no
            AND (dc.contract_id like CONCAT('%', #{req.searchString}, '%')
            OR dc.sf_contract_id like CONCAT('%', #{req.searchString}, '%')
            OR dc.sf_contract_no like CONCAT('%', #{req.searchString}, '%')
            OR dc.order_start_date like CONCAT('%', #{req.searchString}, '%')
            ))
            OR EXISTS (select 1
            from deliver_contract_item dci
            where d.deliver_no = dci.deliver_no
            AND (dci.sf_product_name like CONCAT('%', #{req.searchString}, '%'))))
        </if>
    </sql>
    <!--deliver contract 表上的查询条件-->
    <sql id="dcQueryCondition">
        <if test="req.signDateStart != null or req.signDateEnd != null or req.powerStart != null or req.powerEnd != null">
            AND EXISTS (select 1
            from deliver_contract dc
            where d.deliver_no = dc.deliver_no
            <if test="req.signDateStart != null">
                AND dc.counter_signed_date >= #{req.signDateStart}
            </if>
            <if test="req.signDateEnd != null">
                AND dc.counter_signed_date &lt;= #{req.signDateEnd}
            </if>
            <if test="req.powerStart != null">
                AND dc.total_volume_mw &gt;= #{req.powerStart}
            </if>
            <if test="req.powerEnd != null">
                AND dc.total_volume_mw &lt;= #{req.powerEnd}
            </if>
            )
        </if>
    </sql>

    <select id="getExternalDeliverPage" resultType="com.trinasolar.trinax.delivery.repository.domain.list.DeliveryListDTO">
        <include refid="externalDeliverList"/>
    </select>

    <select id="getExternalDeliverList" resultType="com.trinasolar.trinax.delivery.repository.domain.list.DeliveryListDTO">
        <include refid="externalDeliverList"/>
    </select>

    <sql id="externalDeliverList">
        select d.deliver_no as deliverNo,
        d.deliver_status as deliverStatus,
        d.deliver_status_false as deliverStatusFake,
        d.enterprise_id as enterpriseId,
        d.enterprise_name as enterpriseName,
        d.capital_enterprise_id as capitalEnterpriseId,
        d.capital_enterprise_name as capitalEnterpriseName,
        d.external_User_Id as externalUserId,
        d.sales_internal_user_id as salesInternalUserId,
        d.created_time as createdTime,
        d.sf_contract_list as sfContractList,
        d.bill_to_address_id as billToAddressId,
        d.comment as comment,
        d.incoterm,
        d.sales_internal_user_name as salesInternalUserName,
        d.order_type as orderType
        from deliver as d
        where (d.is_deleted = 0)
        <include refid="dcQueryCondition"/>

        <if test="req.enterpriseName != null  and req.enterpriseName != ''">
            AND d.enterprise_name LIKE CONCAT('%', #{req.enterpriseName}, '%')
        </if>
        <if test="req.deliverNo != null  and req.deliverNo != ''">
            AND d.deliver_no LIKE CONCAT('%', #{req.deliverNo}, '%')
        </if>
        <if test="req.contractId != null  and req.contractId != ''">
            AND d.sf_contract_list LIKE CONCAT('%', #{req.contractId}, '%')

<!--            AND EXISTS (select 1-->
<!--            from deliver_contract dc-->
<!--            where d.deliver_no = dc.deliver_no-->
<!--            AND (dc.contract_id like CONCAT('%', #{req.contractId}, '%')))-->
        </if>
        <if test="req.productName != null  and req.productName != ''">
            AND EXISTS (select 1
            from deliver_contract_item dci
            where d.deliver_no = dci.deliver_no
            AND (dci.sf_product_name like CONCAT('%', #{req.productName}, '%')))
        </if>

        <choose>
            <when test="relation != null and relation.size() != 0">
                AND ((d.external_user_id, d.enterprise_id) in
                <foreach collection="relation" separator="," item="item" open="(" close=")">
                    (#{item.dealerUserId,jdbcType=VARCHAR}, #{item.enterpriseId,jdbcType=VARCHAR})
                </foreach>
                OR d.external_user_id = #{req.userId})
            </when>
            <otherwise>
                AND d.external_user_id = #{req.userId}
            </otherwise>
        </choose>

        <if test="req.deliverStatusList != null and req.deliverStatusList.size() != 0">
            AND d.deliver_status in
            <foreach collection="req.deliverStatusList" separator="," item="status" open="(" close=")">
                #{status}
            </foreach>
        </if>

        <if test="req.createdDateStart != null">
            AND d.created_time &gt;= #{req.createdDateStart}
        </if>

        <if test="req.createdDateEnd != null">
            AND d.created_time &lt;= #{req.createdDateEnd}
        </if>
        <if test="req.signEntity != null and req.signEntity != ''">
            AND (d.capital_enterprise_name like concat('%', #{req.signEntity},'%')
            or d.enterprise_name like concat('%', #{req.signEntity},'%')  and d.capital_enterprise_name is null )
        </if>
        and not exists(select 1 from deliver tmp where tmp.created_by !=#{req.userId} and tmp.deliver_status_false =
        'UN_SUBMIT' and d.deliver_no = tmp.deliver_no)
        order by d.created_time desc
    </sql>

<!--    <select id="getExternalDeliverList"-->
<!--            resultType="com.trinasolar.trinax.delivery.repository.domain.list.DeliveryListDTO">-->
<!--        select d.deliver_no as deliverNo,-->
<!--        d.deliver_status as deliverStatus,-->
<!--        d.deliver_status_false as deliverStatusFake,-->
<!--        d.enterprise_id as enterpriseId,-->
<!--        d.enterprise_name as enterpriseName,-->
<!--        d.capital_enterprise_id as capitalEnterpriseId,-->
<!--        d.capital_enterprise_name as capitalEnterpriseName,-->
<!--        d.external_User_Id as externalUserId,-->
<!--        d.sales_internal_user_id as salesInternalUserId,-->
<!--        d.created_time as createdTime-->
<!--        from deliver as d-->
<!--        where (d.is_deleted = 0)-->
<!--        <include refid="dcQueryCondition"/>-->

<!--        <if test="req.enterpriseName != null  and req.enterpriseName != ''">-->
<!--            AND d.enterprise_name LIKE CONCAT('%', #{req.enterpriseName}, '%')-->
<!--        </if>-->
<!--        <if test="req.deliverNo != null  and req.deliverNo != ''">-->
<!--            AND d.deliver_no LIKE CONCAT('%', #{req.deliverNo}, '%')-->
<!--        </if>-->
<!--        <if test="req.contractId != null  and req.contractId != ''">-->
<!--            AND EXISTS (select 1-->
<!--            from deliver_contract dc-->
<!--            where d.deliver_no = dc.deliver_no-->
<!--            AND (dc.contract_id like CONCAT('%', #{req.contractId}, '%')))-->
<!--        </if>-->
<!--        <if test="req.productName != null  and req.productName != ''">-->
<!--            AND EXISTS (select 1-->
<!--            from deliver_contract_item dci-->
<!--            where d.deliver_no = dci.deliver_no-->
<!--            AND (dci.sf_product_name like CONCAT('%', #{req.productName}, '%'))))-->
<!--        </if>-->

<!--        <choose>-->
<!--            <when test="relation != null and relation.size() != 0">-->
<!--                AND ((d.external_user_id, d.enterprise_id) in-->
<!--                <foreach collection="relation" separator="," item="item" open="(" close=")">-->
<!--                    (#{item.dealerUserId,jdbcType=VARCHAR}, #{item.enterpriseId,jdbcType=VARCHAR})-->
<!--                </foreach>-->
<!--                OR d.external_user_id = #{req.userId})-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                AND d.external_user_id = #{req.userId}-->
<!--            </otherwise>-->
<!--        </choose>-->

<!--        <if test="req.deliverStatusList != null and req.deliverStatusList.size() != 0">-->
<!--            AND d.deliver_status in-->
<!--            <foreach collection="req.deliverStatusList" separator="," item="status" open="(" close=")">-->
<!--                #{status}-->
<!--            </foreach>-->
<!--        </if>-->

<!--        <if test="req.createdDateStart != null">-->
<!--            AND d.created_time &gt;= #{req.createdDateStart}-->
<!--        </if>-->

<!--        <if test="req.createdDateEnd != null">-->
<!--            AND d.created_time &lt;= #{req.createdDateEnd}-->
<!--        </if>-->
<!--        and not exists(select 1 from deliver tmp where tmp.created_by !=#{req.userId} and tmp.deliver_status_false =-->
<!--        'UN_SUBMIT' and d.deliver_no = tmp.deliver_no)-->
<!--        order by d.created_time desc-->
<!--    </select>-->


    <select id="getInternalDeliverList"
            resultType="com.trinasolar.trinax.delivery.repository.domain.list.DeliveryListDTO">
        with deliver_tmp as (
        select d.deliver_no as deliverNo,
        d.deliver_status as deliverStatus,
        d.deliver_status_false as deliverStatusFake,
        d.enterprise_id as enterpriseId,
        d.enterprise_name as enterpriseName,
        d.capital_enterprise_id as capitalEnterpriseId,
        d.capital_enterprise_name as capitalEnterpriseName,
        d.created_time as createdTime,
        d.external_User_Id as externalUserId,
        d.sales_internal_user_id as salesInternalUserId,
        d.created_by,
        d.deliver_status_false,
        d.order_type as orderType
        from deliver as d
        where (d.is_deleted = 0)
        <include refid="authPara"/>
        <include refid="dcQueryCondition"/>
        <if test="req.deliverStatusList != null and req.deliverStatusList.size() != 0">
            AND d.deliver_status in
            <foreach collection="req.deliverStatusList" separator="," item="status" open="(" close=")">
                #{status}
            </foreach>
        </if>
        <if test="req.createdDateStart != null">
            AND d.created_time &gt;= #{req.createdDateStart}
        </if>

        <if test="req.createdDateEnd != null">
            AND d.created_time &lt;= #{req.createdDateEnd}
        </if>
        <if test="req.signEntity != null and req.signEntity != ''">
            AND (d.capital_enterprise_name like concat('%', #{req.signEntity},'%')
            or d.enterprise_name like concat('%', #{req.signEntity},'%')  and d.capital_enterprise_name is null )
        </if>
        ) select deliverNo,
        deliverStatus,
        deliverStatusFake,
        enterpriseId,
        enterpriseName,
        capitalEnterpriseId,
        capitalEnterpriseName,
        externalUserId,
        salesInternalUserId,
        createdTime,
        orderType
        from deliver_tmp where deliver_status_false!= 'UN_SUBMIT' or created_by=#{req.userId}
        order by createdTime desc
    </select>

    <select id="searchInternalDeliverList"
            resultType="com.trinasolar.trinax.delivery.repository.domain.list.DeliveryListDTO">
        select d.deliver_no as deliverNo,
        d.deliver_status as deliverStatus,
        d.deliver_status_false as deliverStatusFake,
        d.enterprise_id as enterpriseId,
        d.enterprise_name as enterpriseName,
        d.capital_enterprise_id as capitalEnterpriseId,
        d.capital_enterprise_name as capitalEnterpriseName,
        d.external_User_Id as externalUserId,
        d.sales_internal_user_id as salesInternalUserId,
        d.created_time as createdTime,
        d.order_type as orderType
        from deliver as d
        where (d.is_deleted = 0)
        <include refid="authPara"/>
        <include refid="searchCondition"/>
        <include refid="dcQueryCondition"/>

        <if test="req.deliverStatusList != null and req.deliverStatusList.size() != 0">
            AND d.deliver_status in
            <foreach collection="req.deliverStatusList" separator="," item="status" open="(" close=")">
                #{status}
            </foreach>
        </if>
        <if test="req.signEntity != null and req.signEntity != ''">
            AND (d.capital_enterprise_name like concat('%', #{req.signEntity},'%')
            or d.enterprise_name like concat('%', #{req.signEntity},'%')  and d.capital_enterprise_name is null )
        </if>

        <if test="req.createdDateStart != null">
            AND d.created_time &gt;= #{req.createdDateStart}
        </if>
        <if test="req.createdDateEnd != null">
            AND d.created_time &lt;= #{req.createdDateEnd}
        </if>
        order by d.created_time desc
    </select>


    <select id="searchExternalDeliverList"
            resultType="com.trinasolar.trinax.delivery.repository.domain.list.DeliveryListDTO">
        select d.deliver_no as deliverNo,
        d.deliver_status as deliverStatus,
        d.deliver_status_false as deliverStatusFake,
        d.enterprise_id as enterpriseId,
        d.enterprise_name as enterpriseName,
        d.capital_enterprise_id as capitalEnterpriseId,
        d.capital_enterprise_name as capitalEnterpriseName,
        d.external_User_Id as externalUserId,
        d.sales_internal_user_id as salesInternalUserId,
        d.created_time as createdTime,
        d.order_type as orderType
        from deliver as d
        where (d.is_deleted = 0)
        <include refid="searchCondition"/>
        <include refid="dcQueryCondition"/>

        <choose>
            <when test="relation != null and relation.size() != 0">
                AND ((d.external_user_id, d.enterprise_id) in
                <foreach collection="relation" separator="," item="item" open="(" close=")">
                    (#{item.dealerUserId,jdbcType=VARCHAR}, #{item.enterpriseId,jdbcType=VARCHAR})
                </foreach>
                OR d.external_user_id = #{req.userId})
            </when>
            <otherwise>
                AND d.external_user_id = #{req.userId}
            </otherwise>
        </choose>

        <if test="req.deliverStatusList != null and req.deliverStatusList.size() != 0">
            AND d.deliver_status in
            <foreach collection="req.deliverStatusList" separator="," item="status" open="(" close=")">
                #{status}
            </foreach>
        </if>

        <if test="req.createdDateStart != null">
            AND d.created_time &gt;= #{req.createdDateStart}
        </if>

        <if test="req.createdDateEnd != null">
            AND d.created_time &lt;= #{req.createdDateEnd}
        </if>
        <if test="req.signEntity != null and req.signEntity != ''">
            AND (d.capital_enterprise_name like concat('%', #{req.signEntity},'%')
            or d.enterprise_name like concat('%', #{req.signEntity},'%')  and d.capital_enterprise_name is null )
        </if>

        order by d.created_time desc
    </select>

    <select id="getOpsDeliverList"
            resultType="com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverOpsListRespDTO">
        select d.enterprise_name as enterpriseName,
        d.deliver_no as deliverNo,
        d.incoterm as deliverMethod,
        d.bill_to_address_id as addressId,
        d.comment as comment,
        d.external_user_id as contactId,
        d.operation_return_reason as returnReason,
        d.deliver_status as status,
        d.quantity_mw as quantityMw,
        d.created_by as createdBy,
        d.created_name as createdName,
        d.sf_contract_list as sfContractList,
        d.total_quantity_p as totalQuantityP,
        d.capital_enterprise_name as capitalEnterpriseName,
        d.capital_enterprise_id as capitalEnterpriseId,
        d.biz_organization_code,
        d.sales_internal_user_id as salesInternalUserId,
        d.sales_internal_user_name as salesInternalUserName,
        d.enterprise_id,
        d.customer_category as customerCategory,
        case
        when d.external_user_submit_time IS NULL then
        d.sales_confirm_time
        else d.external_user_submit_time
        end
        as submitDate,
        d.created_time as createdTime,
        d.operation_internal_user_id as operationInternalUserId,
        d.operation_internal_user_name as operationInternalUserName,
        d.order_type as orderType
        from deliver d
        where (d.is_deleted = 0)
        <choose>
            <!--        SALES 销售-->
            <when test="para != null and para.sysUserTypeEnum == 'SALES'and para.salesUserList.size() != 0">
                AND ((d.sales_internal_user_id, d.biz_organization_code) in
                <foreach collection="para.salesUserList" separator="," item="item" open="(" close=")">
                    (#{item.userId}, #{item.organizationCode})
                </foreach>
                OR d.sales_internal_user_id = #{req.userId})
            </when>

            <!--        OPERATION 运营-->
            <when test="para != null and para.sysUserTypeEnum == 'OPERATION'and para.operationUserIdList.size() != 0">
                AND (d.operation_internal_user_id in
                <foreach collection="para.operationUserIdList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
                OR d.operation_internal_user_id = #{req.userId})
            </when>
            <!--        COMMON 常规-->
            <when test="para != null and para.sysUserTypeEnum == 'COMMON' and para.commonOrganizationCodes.size() != 0">
                AND d.biz_organization_code in
                <foreach collection="para.commonOrganizationCodes" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </when>

            <when test="para != null and para.sysUserTypeEnum == 'SERVICE'">
                AND d.biz_organization_code = 'NOT_EXIST'
            </when>
        </choose>

        <if test="req.customerCategory != null  and req.customerCategory != ''">
            AND d.customer_category = #{req.customerCategory}
        </if>

        <if test="req.enterpriseName != null  and req.enterpriseName != ''">
            AND d.enterprise_name LIKE CONCAT('%', #{req.enterpriseName}, '%')
        </if>

        <if test="req.deliverNo != null  and req.deliverNo != ''">
            AND d.deliver_no LIKE CONCAT('%', #{req.deliverNo}, '%')
        </if>

        <if test="req.deliveryStatus != null  and req.deliveryStatus != ''">
            AND d.deliver_status = #{req.deliveryStatus}
        </if>

        <if test="req.submitDateStart != null">
            AND IF(d.external_user_submit_time is null,
            d.sales_confirm_time &gt;= #{req.submitDateStart},
            d.external_user_submit_time &gt;= #{req.submitDateStart})
        </if>

        <if test="req.submitDateEnd != null">
            AND IF(d.external_user_submit_time is null,
            d.sales_confirm_time &lt;= #{req.submitDateEnd},
            d.external_user_submit_time &lt;= #{req.submitDateEnd})
        </if>

        <if test="req.contractId != null  and req.productName != null">
            AND EXISTS(select 1
            from deliver_contract dc
            where d.deliver_no = dc.deliver_no
            AND dc.sf_contract_no LIKE CONCAT('%', #{req.contractId}, '%'))
        </if>

        <if test="req.productName != null  and req.productName != ''">
            AND EXISTS(select 1
            from deliver_contract_item dci
            where d.deliver_no = dci.deliver_no
            AND dci.sf_product_name LIKE CONCAT('%', #{req.productName}, '%'))
        </if>
        <if test="req.enterpriseIdList!=null">
            and (d.enterprise_id = #{req.enterpriseId} or d.capital_enterprise_id = #{req.enterpriseId})
        </if>

        <if test="req.enterpriseId!=null  and req.enterpriseId != ''">
            and (d.enterprise_id = #{req.enterpriseId} or d.capital_enterprise_id = #{req.enterpriseId})
        </if>
        <if test="req.capitalEnterpriseName != null  and req.capitalEnterpriseName != ''">
            AND d.capital_enterprise_name LIKE CONCAT('%', #{req.capitalEnterpriseName}, '%')
        </if>
        <if test="req.signEntity != null and req.signEntity != ''">
            AND (d.capital_enterprise_name like concat('%', #{req.signEntity},'%')
            or d.enterprise_name like concat('%', #{req.signEntity},'%')  and d.capital_enterprise_name is null )
        </if>
        <if test="req.bizOrganizationCode != null  and req.bizOrganizationCode != ''">
            AND d.biz_organization_code = #{req.bizOrganizationCode}
        </if>
        order by d.created_time desc
    </select>

    <sql id="pcQryDeliverCondition">
        select d.deliver_no as deliverNo,
        d.deliver_status as deliverStatus,
        d.deliver_status_false as deliverStatusFake,
        d.enterprise_id as enterpriseId,
        d.enterprise_name as enterpriseName,
        d.capital_enterprise_id as capitalEnterpriseId,
        d.capital_enterprise_name as capitalEnterpriseName,
        d.created_time as createdTime,
        d.created_by as createdBy,
        d.incoterm as deliverMethod,
        d.bill_to_address_id as addressId,
        d.comment as comment,
        d.external_user_id as contactId,
        d.operation_return_reason as returnReason,
        d.external_user_submit_time as externalUserSubmitTime
        from deliver as d
        where d.is_deleted = 0
        <if test="req.deliverStatus != null  and req.deliverStatus != ''">
            and d.deliver_status = #{req.deliverStatus}
        </if>
        <if test="req.dataUserType != null and req.dataUserType == 'INTERNAL' ">
            and d.sales_internal_user_id = #{req.dataUserId}
        </if>
        <if test="req.dataUserType != null and req.dataUserType == 'EXTERNAL' ">
            and d.external_user_id = #{req.dataUserId}
        </if>
        <include refid="authPara"/>
        <if test="req.deliverNoLike != null  and req.deliverNoLike != null">
            and d.deliver_no like CONCAT('%', #{req.deliverNoLike}, '%')
        </if>
        <if test="req.submitTimeBegin != null  and req.submitTimeEnd != null">
            and d.external_user_submit_time between #{req.submitTimeBegin} and #{req.submitTimeEnd}
        </if>
        <if test="req.createdTimeBegin != null  and req.createdTimeEnd != null">
            and d.created_time between #{req.createdTimeBegin} and #{req.createdTimeEnd}
        </if>
        <if test="req.enterpriseNameLike != null and req.enterpriseNameLike!=''">
            and d.enterprise_name like CONCAT('%', #{req.enterpriseNameLike}, '%')
        </if>
        <if test="req.sfContractNoLike != null and req.sfContractNoLike!=''">
            and exists(select 1 from deliver_contract dc
            where d.deliver_no = dc.deliver_no
            and dc.sf_contract_no like CONCAT('%', #{req.sfContractNoLike}, '%'))
        </if>
        <if test="req.sfProductNameLike != null and req.sfProductNameLike != ''">
            and exists(select 1 from deliver_contract_item dci
            where d.deliver_no = dci.deliver_no
            and dci.sf_product_name like CONCAT('%', #{req.sfProductNameLike}, '%'))
        </if>
        order by external_user_submit_time desc
    </sql>

    <select id="exportPcQryDeliverList"
            resultType="com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverInfoResDTO">
        <include refid="pcQryDeliverCondition"></include>
    </select>

    <select id="pcQryDeliverList"
            resultType="com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverInfoResDTO">
        <include refid="pcQryDeliverCondition"></include>
    </select>

    <select id="deliveryAmountReport" resultType="com.trinasolar.trinax.delivery.dto.output.delivery.report.DeliveryReportResDTO">
        select
            t3.biz_organization_code,
            t3.full_month,
            t3.single_year,
            t3.single_month,
            count(1) as amount
        from
            (select
                t1.deliver_no,
                t1.biz_organization_code,
                DATE_FORMAT(t1.created_time , '%Y%m') as full_month,
                DATE_FORMAT(t1.created_time , '%Y') as single_year,
                DATE_FORMAT(t1.created_time , '%m') as single_month
            from
                trinax_delivery.deliver t1
            where
                t1.deliver_status in('SUBMIT','SALES_CONFIRMED','OPERATION_CONFIRMED','CANCELED')
            <if test="req.deliverStatus != null and req.deliverStatus != ''">
                AND t1.deliver_status = #{req.deliverStatus}
            </if>
            <if test="req.startTime != null ">
                AND t1.created_time &gt;= #{req.startTime}
            </if>
            <if test="req.endTime != null ">
                AND t1.created_time &lt;= #{req.endTime}
            </if>
            <if test="req.enterpriseIdList != null and req.enterpriseIdList.size() > 0">
                and t1.enterprise_id in
                <foreach item="item" collection="req.enterpriseIdList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        ) t3
        group by
            t3.biz_organization_code,
            t3.full_month ,
            t3.single_year,
            t3.single_month
        order by
            t3.full_month asc;
    </select>



    <select id="opsExportDeliverList"
            resultType="com.trinasolar.trinax.delivery.dto.output.delivery.file.DeliverExportResDTO">
        select d.enterprise_name as enterpriseName,
        d.deliver_no as deliverNo,
        d.incoterm as deliverMethod,
        d.bill_to_address_id as addressId,
        d.comment as comment,
        d.external_user_id as contactId,
        d.operation_return_reason as returnReason,
        d.deliver_status as status,
        d.quantity_mw as quantityMw,
        d.created_by as createdBy,
        d.created_name as createdName,
        d.sf_contract_list as sfContractList,
        d.customer_category as customerCategory,
        dc.sf_contract_no as sfContractNo,
        dc.application_quantity as applicationQuantity,
        dc.application_volume_mw as applicationVolumeMw,
        dc.rebate,
        dc.open_contract,
        d.total_quantity_p as totalQuantityP,
        d.capital_enterprise_name as capitalEnterpriseName,
        d.biz_organization_code,
        case
        when d.external_user_submit_time IS NULL then
        d.sales_confirm_time
        else d.external_user_submit_time
        end
        as submitDate,
        d.created_time as createdTime,
        d.sales_internal_user_id as salesInternalUserId,
        d.sales_internal_user_name as salesInternalUserName
        from deliver d left join deliver_contract dc
        on d.deliver_no = dc.deliver_no
        where (d.is_deleted = 0)
        <choose>
            <!--        SALES 销售-->
            <when test="para != null and para.sysUserTypeEnum == 'SALES'and para.salesUserList.size() != 0">
                AND ((d.sales_internal_user_id, d.biz_organization_code) in
                <foreach collection="para.salesUserList" separator="," item="item" open="(" close=")">
                    (#{item.userId}, #{item.organizationCode})
                </foreach>
                OR d.sales_internal_user_id = #{req.userId})
            </when>

            <!--        OPERATION 运营-->
            <when test="para != null and para.sysUserTypeEnum == 'OPERATION'and para.operationUserIdList.size() != 0">
                AND (d.operation_internal_user_id in
                <foreach collection="para.operationUserIdList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
                OR d.operation_internal_user_id = #{req.userId})
            </when>
            <!--        COMMON 常规-->
            <when test="para != null and para.sysUserTypeEnum == 'COMMON' and para.commonOrganizationCodes.size() != 0">
                AND d.biz_organization_code in
                <foreach collection="para.commonOrganizationCodes" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </when>

            <when test="para != null and para.sysUserTypeEnum == 'SERVICE'">
                AND d.biz_organization_code = 'NOT_EXIST'
            </when>
        </choose>


        <if test="req.enterpriseName != null  and req.enterpriseName != ''">
            AND d.enterprise_name LIKE CONCAT('%', #{req.enterpriseName}, '%')
        </if>

        <if test="req.deliverNo != null  and req.deliverNo != ''">
            AND d.deliver_no LIKE CONCAT('%', #{req.deliverNo}, '%')
        </if>

        <if test="req.deliveryStatus != null  and req.deliveryStatus != ''">
            AND d.deliver_status = #{req.deliveryStatus}
        </if>

        <if test="req.submitDateStart != null">
            AND IF(d.external_user_submit_time is null,
            d.sales_confirm_time &gt;= #{req.submitDateStart},
            d.external_user_submit_time &gt;= #{req.submitDateStart})
        </if>

        <if test="req.submitDateEnd != null">
            AND IF(d.external_user_submit_time is null,
            d.sales_confirm_time &lt;= #{req.submitDateEnd},
            d.external_user_submit_time &lt;= #{req.submitDateEnd})
        </if>

        <if test="req.contractId != null  and req.productName != null">
            AND EXISTS(select 1
            from deliver_contract dc
            where d.deliver_no = dc.deliver_no
            AND dc.sf_contract_no LIKE CONCAT('%', #{req.contractId}, '%'))
        </if>

        <if test="req.productName != null  and req.productName != ''">
            AND EXISTS(select 1
            from deliver_contract_item dci
            where d.deliver_no = dci.deliver_no
            AND dci.sf_product_name LIKE CONCAT('%', #{req.productName}, '%'))
        </if>
        <if test="req.enterpriseIdList!=null">
            and (d.enterprise_id = #{req.enterpriseId} or d.capital_enterprise_id = #{req.enterpriseId})
        </if>

        <if test="req.enterpriseId!=null  and req.enterpriseId != ''">
            and d.enterprise_id = #{req.enterpriseId}
        </if>
        <if test="req.signEntity != null and req.signEntity != ''">
            AND (d.capital_enterprise_name like concat('%', #{req.signEntity},'%')
            or (d.enterprise_name like concat('%', #{req.signEntity},'%')
                and (d.capital_enterprise_name is null or d.capital_enterprise_name = '')))
        </if>
        <if test="req.bizOrganizationCode != null  and req.bizOrganizationCode != ''">
            AND d.biz_organization_code = #{req.bizOrganizationCode}
        </if>

        order by d.created_time desc
    </select>

    <select id="selectChangeOwnerRecord" resultType="com.trinasolar.trinax.delivery.repository.domain.DeliverPO">
        select t1.*
        from
        trinax_delivery.deliver t1
        where t1.deliver_status not in('UN_SUBMIT','CANCELED')
        <if test="req.changeType=='SALES_USER_CHANGE' and req.conditionList != null and req.conditionList.size() > 0">
            and t1.sales_internal_user_id = #{req.originUserId}
            and  (t1.enterprise_id,t1.external_user_id) in
            <foreach collection="req.conditionList" item="item" index="index" open="(" close=")" separator=",">
                (#{item.enterpriseId}, #{item.userId})
            </foreach>
        </if>
        <if test="req.changeType=='OPERATION_USER_CHANGE' and req.conditionList != null and req.conditionList.size() > 0">
            and t1.operation_internal_user_id = #{req.originUserId}
            and   (t1.biz_organization_code,t1.sales_internal_user_id) in
            <foreach collection="req.conditionList" item="item" index="index" open="(" close=")" separator=",">
                (#{item.organizationCode}, #{item.userId})
            </foreach>
        </if>
        <if test="req.changeType=='EXTERNAL_USER_CHANGE' and req.conditionList != null and req.conditionList.size() > 0">
            and t1.external_user_id = #{req.originUserId}
            and (t1.enterprise_id,t1.sales_internal_user_id) in
            <foreach collection="req.conditionList" item="item" index="index" open="(" close=")" separator=",">
                (#{item.enterpriseId}, #{item.userId})
            </foreach>
        </if>
    </select>


</mapper>
