package com.trinasolar.trinax.delivery.service.biz;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.contract.api.ContractFeign;
import com.trinasolar.trinax.delivery.constants.DeliveryConstant;
import com.trinasolar.trinax.delivery.constants.enums.DeliverStatus;
import com.trinasolar.trinax.delivery.constants.enums.DeliverySituationEnum;
import com.trinasolar.trinax.delivery.constants.enums.DeliveryStatusFake;
import com.trinasolar.trinax.delivery.dto.input.delivery.submit.SubmitPartnerListReqDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.check.DetailCheckResDTO;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractItemMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverMapperService;
import com.trinasolar.trinax.delivery.repository.domain.DeliverContractItemPO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverPO;
import com.trinasolar.trinax.delivery.service.biz.lock.business.DeliverBusinessLock;
import com.trinasolar.trinax.delivery.service.manager.DeliverBizManager;
import com.trinasolar.trinax.delivery.service.manager.DeliverCheckManager;
import com.trinasolar.trinax.delivery.service.manager.DeliverManager;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.trinasolar.trinax.delivery.constants.enums.DeliverOperationTypeEnum.EXTERNAL_COMMIT_LIST;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class DeliverSubmitListBiz implements DeliverBusinessLock {
    final DeliverMapperService deliverMapperService;
    final DeliverContractMapperService deliverContractMapperService;
    final DeliverContractItemMapperService deliverContractItemMapperService;
    final SysUserFeign sysUserFeign;
    final ContractFeign contractFeign;
    final TransactionTemplate transactionTemplate;
    final DeliverManager deliverManager;
    final DeliverBizManager deliverBizManager;
    final EnterpriseFeign enterpriseFeign;
    final MqManager mqManager;
    final RedissonClient redissonClient;
    final DeliverCheckManager deliverCheckManager;

    public Result<Object> submitPartnerListDelivery(SubmitPartnerListReqDTO req) {

        List<String> lockList = new ArrayList<>();
        lockList.add(req.getDeliveryNo());

        return this.lockBusiness(JacksonUtil.bean2Json(req), lockList,
                SubmitPartnerListReqDTO.class, redissonClient);


    }


    private void sendMsg(String deliveryNo, SysUserRespDTO sysUser) {
        // 查询基本数据
        DeliverPO deliverPO = deliverMapperService.lambdaQuery().eq(DeliverPO::getDeliverNo, deliveryNo).one();
        List<DeliverContractItemPO> deliverContractItemPOList = deliverContractItemMapperService.lambdaQuery()
                .eq(DeliverContractItemPO::getDeliverNo, deliveryNo).list();

        // 发送消息
        mqManager.sendTopic(DeliveryConstant.DELIVERY_REQUEST_MESSAGE_TOPIC,
                JacksonUtil.bean2Json(deliverBizManager.composeDeliverMessage(
                        DeliverySituationEnum.DR_EXTERNAL_CREATE, sysUser, deliverPO, deliverContractItemPOList)));
    }

    @Override
    public Result<Object> doBusiness(Object reqBody) {
        SubmitPartnerListReqDTO req = (SubmitPartnerListReqDTO) reqBody;

        String deliverNo = req.getDeliveryNo();

        DeliverPO deliver = deliverMapperService.lambdaQuery().eq(DeliverPO::getDeliverNo, deliverNo).one();

        String deliverStatus = deliver.getDeliverStatus();
        String deliverStatusFalse = deliver.getDeliverStatusFalse();

        deliverBizManager.checkDeliverInfo(deliverNo, deliver, null, null, EXTERNAL_COMMIT_LIST.getCode());

        deliver.setDeliverStatus(DeliverStatus.SUBMIT.getCode());
        deliver.setDeliverStatusFalse(DeliveryStatusFake.SUBMIT.getCode());
        deliverMapperService.lambdaUpdate()
                .eq(DeliverPO::getDeliverNo, deliverNo)
                .set(DeliverPO::getDeliverStatus, DeliverStatus.SUBMIT.getCode())
                .set(DeliverPO::getDeliverStatusFalse, DeliveryStatusFake.SUBMIT.getCode())
                .update();

        //校验需要事务控制
        Result<DetailCheckResDTO> checkResult = deliverCheckManager.deliverDetailCheck(deliverNo, deliver);
        if (!checkResult.getSuccess()) {
            deliverStatusRollback(deliverNo, deliverStatus, deliverStatusFalse);
            return Result.result(checkResult.getCode(), checkResult.getCode(), checkResult.getData());
        }

        Result<String> check = deliverBizManager.complexDeliverCheck(deliverNo, null, false, EXTERNAL_COMMIT_LIST.getCode());
        if (!check.getSuccess()) {
            deliverStatusRollback(deliverNo, deliverStatus, deliverStatusFalse);
            return Result.fail(check.getCode(), check.getMessage());
        }

        String userId = req.getUserId();
        SysUserRespDTO sysUser = sysUserFeign.getUserByUserId(userId).getData();
        String userName = sysUser.getUserName();
        LocalDateTime now = LocalDateTime.now();

        transactionTemplate.execute(tt -> {
            deliverMapperService.lambdaUpdate()
                    .eq(DeliverPO::getDeliverNo, deliverNo)
                    .set(DeliverPO::getDeliverStatus, DeliverStatus.SUBMIT.getCode())
                    .set(DeliverPO::getDeliverStatusFalse, DeliveryStatusFake.SUBMIT.getCode())
                    .set(DeliverPO::getExternalUserSubmitTime, now)
                    .set(DeliverPO::getUpdatedBy, userId)
                    .set(DeliverPO::getUpdatedName, userName)
                    .set(DeliverPO::getUpdatedTime, now)
                    .update();
            return Result.ok(deliverNo);
        });
        sendMsg(deliverNo, sysUser);
        return Result.ok(req.getDeliveryNo());
    }

    private void deliverStatusRollback(String deliverNo, String deliverStatus, String deliverStatusFalse) {
        deliverMapperService.lambdaUpdate()
                .eq(DeliverPO::getDeliverNo, deliverNo)
                .set(DeliverPO::getDeliverStatus, deliverStatus)
                .set(DeliverPO::getDeliverStatusFalse, deliverStatusFalse)
                .update();
    }


    @Override
    public Result<Object> lockBusiness(String req, List lockKeyListStr, Class beanType, RedissonClient redissonClient) {
        return DeliverBusinessLock.super.lockBusiness(req, lockKeyListStr, beanType, redissonClient);
    }
}
