package com.trinasolar.trinax.delivery.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverAuthListUserReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverListReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverOpsListReqDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.detail.DeliverDetailOpsRespDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverListRespDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverOpsListRespDTO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DeliverMapperService extends IService<DeliverPO> {
    Result<PageResponse<DeliverListRespDTO>> getInternalDeliveryList(PageRequest<DeliverListReqDTO> req,
                                                                     DeliverAuthListUserReqDTO para);

    Result<PageResponse<DeliverListRespDTO>> getExternalDeliveryPage(PageRequest<DeliverListReqDTO> req);

    List<DeliverListRespDTO> getExternalDeliveryList(DeliverListReqDTO req);

    Result<PageResponse<DeliverListRespDTO>> searchInternalDeliveryList(PageRequest<DeliverListReqDTO> req,
                                                                        DeliverAuthListUserReqDTO para);

    Result<PageResponse<DeliverListRespDTO>> searchExternalDeliveryList(PageRequest<DeliverListReqDTO> req);

    IPage<DeliverOpsListRespDTO> listDeliverOps(Page<DeliverOpsListRespDTO> page,
                                                @Param("req") DeliverOpsListReqDTO req,
                                                @Param("para") DeliverAuthListUserReqDTO para);

    IPage<DeliverDetailOpsRespDTO> detailOpsDeliver(Page<DeliverDetailOpsRespDTO> page, String deliverNo);
}
