package com.trinasolar.trinax.delivery.service.manager;

import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
@RefreshScope
public class CacheManager {

    @Value("${deliver.caffeineCache.on-off}")
    private boolean caffeineCache;

    @Autowired
    @Qualifier("deliverCaffeineCache")
    Cache<String, Object> deliverCaffeineCache;


    /**
     * 缓存结构
     */
    public PageResponse getDeliverCache(String cacheKey, String userKey) {
        if (caffeineCacheOff()) {
            return null;
        }
        Map<String, PageResponse> map = (Map<String, PageResponse>) deliverCaffeineCache.getIfPresent(userKey);
        if (ObjectUtils.isEmpty(map)) {
            return null;
        }

        return map.get(cacheKey);
    }

    /**
     * 设置缓存
     */
    public void setDeliverCache(String cacheKey, String userKey, PageResponse value) {
        if (caffeineCacheOff()) {
            return;
        }
        Map<String, PageResponse> map = (Map<String, PageResponse>) deliverCaffeineCache.getIfPresent(userKey);
        if (ObjectUtils.isEmpty(map)) {
            Map<String, PageResponse> cacheHashMap = new HashMap<>();
            cacheHashMap.put(cacheKey, value);
            deliverCaffeineCache.put(userKey, cacheHashMap);
        } else {
            map.put(cacheKey, value);
            deliverCaffeineCache.put(userKey, map);
        }
    }

    public void cleanCache(String userId) {
        deliverCaffeineCache.invalidate(userId);
    }

    private boolean caffeineCacheOff(){
        return !caffeineCache;
    }
}
