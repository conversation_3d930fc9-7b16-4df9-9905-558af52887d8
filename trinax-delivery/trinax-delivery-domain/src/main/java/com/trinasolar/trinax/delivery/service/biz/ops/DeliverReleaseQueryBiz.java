package com.trinasolar.trinax.delivery.service.biz.ops;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.contract.api.ContractFeign;
import com.trinasolar.trinax.contract.constants.enums.OpenContractEnum;
import com.trinasolar.trinax.contract.dto.output.contract.sales.CoBusinessResDTO;
import com.trinasolar.trinax.delivery.constants.enums.ApprovalTypeEnum;
import com.trinasolar.trinax.delivery.constants.enums.BpmStatusEnum;
import com.trinasolar.trinax.delivery.constants.enums.ItemTypeEnum;
import com.trinasolar.trinax.delivery.constants.enums.SyncStatusSfEnum;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.*;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverFileRelationResDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverReleaseResDTO;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractItemMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverFileRelationMapperService;
import com.trinasolar.trinax.delivery.repository.domain.DeliverContractItemPO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverFileRelationPO;
import com.trinasolar.trinax.delivery.repository.mapper.DeliverContractMapper;
import com.trinasolar.trinax.delivery.service.manager.DeliverAuthManager;
import com.trinasolar.trinax.intentorder.api.IntentOrderFeign;
import com.trinasolar.trinax.intentorder.dto.output.IntentOrderPCQueryResDTO;
import com.trinasolar.trinax.partner.api.MarketingAccountFeign;
import com.trinasolar.trinax.partner.dto.input.ErpOrderTypeQueryReqDTO;
import com.trinasolar.trinax.partner.dto.output.ErpOrderTypeResDTO;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class DeliverReleaseQueryBiz {
    final DeliverContractMapperService deliverContractMapperService;
    final DeliverContractMapper deliverContractMapper;
    final DeliverContractItemMapperService deliverContractItemMapperService;
    final DeliverFileRelationMapperService deliverFileRelationMapperService;
    final MarketingAccountFeign marketingAccountFeign;
    final DeliverAuthManager deliverAuthManager;

    @Value("${deliver.openSystemOrgCode}")
    String openSystemOrgCode;
    private final ContractFeign contractFeign;
    private final IntentOrderFeign intentOrderFeign;


    /**
     * 分页查询
     * @param req
     * @return
     */
    public Result<PageResponse<DeliverReleaseResDTO>> qryDeliverReleasePage(PageRequest<DeliverReleaseReqDTO> req) {
        Page<DeliverReleaseResDTO> page = new Page<>(req.getIndex(), req.getSize());
        DeliverAuthListUserReqDTO para = deliverAuthManager.getQueryAuthPara(req.getQuery().getCurrentUserId(), SysUserTypeEnum.INTERNAL.getType());
        IPage<DeliverReleaseResDTO> dataPage = deliverContractMapper.qryDeliverReleasePage(page, req.getQuery(),para);
        List<DeliverReleaseResDTO> dataList = dataPage.getRecords();
        //数据为空直接返回
        if(CollUtil.isEmpty(dataList)){
            return Result.ok(PageResponse.empty(req));
        }
        //存在数据，需要进行数据填充
        fillData(dataList,true);

        PageResponse<DeliverReleaseResDTO> result = PageResponse.toResult(
                req.getIndex(),
                req.getSize(),
                (int) dataPage.getTotal(),
                dataList);
        return Result.ok((result));
    }

    /**
     * 列表查询
     * @param req
     * @return
     */
    public Result<List<DeliverReleaseResDTO>> qryDeliverReleaseList(DeliverReleaseReqDTO req) {
        DeliverAuthListUserReqDTO para = deliverAuthManager.getQueryAuthPara(req.getCurrentUserId(), SysUserTypeEnum.INTERNAL.getType());
        List<DeliverReleaseResDTO> dataList = deliverContractMapper.qryDeliverReleaseList(req,para);
        //数据为空直接返回
        if(CollUtil.isEmpty(dataList)){
            return Result.ok(Collections.emptyList());
        }
        //存在数据，需要进行数据填充
        dataList = fillData(dataList,false);

        return Result.ok((dataList));
    }

    /**
     * 填充发货申请合同产品行 & 发货申请合同文件
     * @param dataList
     */
    private List<DeliverReleaseResDTO> fillData(List<DeliverReleaseResDTO> dataList,boolean isPageQuery ){
        List<String> deliveryContractIdList = dataList.stream().map(DeliverReleaseResDTO::getDeliverContractId).collect(Collectors.toList());
        List<String> contractIdList = dataList.stream().map(DeliverReleaseResDTO::getContractId).collect(Collectors.toList());

        //查询发货申请合同产品行
        List<DeliverContractItemPO> itemList = deliverContractItemMapperService.lambdaQuery()
                .in(DeliverContractItemPO::getDeliverContractId,deliveryContractIdList)
                .list();

        //获取orderErpType信息，
        List<String> typeIdList = itemList.stream().map(DeliverContractItemPO::getErpOrderTypeId).collect(Collectors.toList());
        Result<List<ErpOrderTypeResDTO>> erpOrderResult = marketingAccountFeign.getErpOrderType( ErpOrderTypeQueryReqDTO.builder().setTypeIdList(typeIdList));
        Map<String, List<ErpOrderTypeResDTO>> erpDataMap = erpOrderResult.getData().stream()
                .collect(Collectors.groupingBy(ErpOrderTypeResDTO::getErpOrderTypeId));

        List<DeliverReleaseResDTO> itemDataList = BeanUtil.copyToList(itemList,DeliverReleaseResDTO.class);
        //计算发货申请合同产品行的金额
        itemDataList.forEach(e->{
            BigDecimal amount = new BigDecimal(0);
            boolean isZero = ObjectUtils.isEmpty(e.getQuantityP()) || ObjectUtils.isEmpty(e.getPower()) || ObjectUtils.isEmpty(e.getUnitPriceW());
            if (!isZero) {
                amount = e.getQuantityP().multiply(e.getPower()).multiply(e.getUnitPriceW());
            }
            e.setAmount(amount);
            //erpTypeName
            if(CollUtil.isNotEmpty(erpDataMap) && CollUtil.isNotEmpty(erpDataMap.get(e.getErpOrderTypeId()))){
                e.setErpOrderTypeName(erpDataMap.get(e.getErpOrderTypeId()).get(0).getErpOrderTypeName());
            }
            e.setDeliverNo(null);
            e.setItemTypeText(ItemTypeEnum.getDescByCode(e.getItemType()));

        });
        //将发货申请合同产品行分组
        Map<String, List<DeliverReleaseResDTO>> itemDataMap = itemDataList.stream()
                .collect(Collectors.groupingBy(DeliverReleaseResDTO::getDeliverContractId));

        //查询发货申请合同文件
        List<DeliverFileRelationPO> fileRelationPOList = deliverFileRelationMapperService.lambdaQuery()
                .in(DeliverFileRelationPO::getDeliverContractId,deliveryContractIdList)
                .list();
        //将发货申请合同文件分组
        List<DeliverFileRelationResDTO> fileDataList = BeanUtil.copyToList(fileRelationPOList,DeliverFileRelationResDTO.class);
        Map<String, List<DeliverFileRelationResDTO>> fileDataMap = fileDataList.stream()
                .collect(Collectors.groupingBy(DeliverFileRelationResDTO::getDeliverContractId));

        Result<List<CoBusinessResDTO>> busContractResult=contractFeign.qryContractBusiness(contractIdList);
        Assert.isTrue(busContractResult.getSuccess(),"合同信息请求出错！");
        List<String> intentOrderNoList=busContractResult.getData().stream().map(CoBusinessResDTO::getIntentOrderNo).collect(Collectors.toList());
        Result<List<IntentOrderPCQueryResDTO>> intentOrderList = intentOrderFeign.findIntentOrderByOrderNo(intentOrderNoList);
        Assert.isTrue(busContractResult.getSuccess(),"订单信息请求出错！");
        List<IntentOrderPCQueryResDTO> intentOrderListData = intentOrderList.getData();
        Map<String, Integer> orderNoTypeMap = intentOrderListData.stream()
                .collect(Collectors.toMap(IntentOrderPCQueryResDTO::getIntentOrderNo, IntentOrderPCQueryResDTO::getOrderType));

        Map<String, Integer> contractTypeMap = busContractResult.getData().stream()
                .collect(Collectors.toMap(
                        CoBusinessResDTO::getContractId,
                        item -> orderNoTypeMap.getOrDefault(item.getIntentOrderNo(), 0)
                ));
        //分页查询处理
        if(isPageQuery){
            //设置发货申请合同产品行，发货申请合同文件
            dataList.forEach(e->{
                if(!openSystemOrgCode.contains(e.getBizOrganizationCode())){
                    //非开放区域，暂时不用处理
                    e.setDistributeAmountSfStatus(SyncStatusSfEnum.NO_NEED_SYNC.getCode());
                }
                e.setDistributeAmountSfStatusText(SyncStatusSfEnum.getDescByCode(e.getDistributeAmountSfStatus()));
                if(StringUtils.isNotBlank(e.getCapitalEnterpriseName())){
                    e.setSignEntity(e.getCapitalEnterpriseName());
                }else{
                    e.setSignEntity(e.getEnterpriseName());
                }
                //分页查询时，将产品行信息放到子列表中;文件信息也放入子列表
                if(CollUtil.isNotEmpty(itemDataMap.get(e.getDeliverContractId()))){
                    e.setProductList(itemDataMap.get(e.getDeliverContractId()));
                }
                if(CollUtil.isNotEmpty(fileDataMap) && CollUtil.isNotEmpty(fileDataMap.get(e.getDeliverContractId()))){
                    List<DeliverFileRelationResDTO> fileData = fileDataMap.get(e.getDeliverContractId());
                    e.setApprovalFileList(fileData);
                }
                e.setItemTypeText(ItemTypeEnum.getDescByCode(e.getItemType()));
                e.setBpmStatusText(BpmStatusEnum.getDescByCode(e.getBpmStatus()));
                e.setApprovalTypeText(ApprovalTypeEnum.getDescByCode(e.getApprovalType()));
                e.setSyncStatusSfText(SyncStatusSfEnum.getDescByCode(e.getSyncStatusSf()));
                if (StringUtils.isNotBlank(e.getOpenContract())) {
                    String[] openContract = e.getOpenContract().split(";");
                    StringJoiner joiner = new StringJoiner(";");
                    for (int i = 0; i < openContract.length; i++) {
                        joiner.add(OpenContractEnum.getDescByCode(openContract[i]));
                    }
                    e.setOpenContractText(joiner.toString());
                }
                e.setOrderType(contractTypeMap.get(e.getContractId()));
            });
            return dataList;
        }else{
            //列表查询处理
            List<DeliverReleaseResDTO> dataResultList = new LinkedList<>();
            dataList.forEach(e->{
                if(!openSystemOrgCode.contains(e.getBizOrganizationCode())){
                    //非开放区域，暂时不用处理
                    e.setDistributeAmountSfStatus(SyncStatusSfEnum.NO_NEED_SYNC.getCode());
                }
                e.setDistributeAmountSfStatusText(SyncStatusSfEnum.getDescByCode(e.getDistributeAmountSfStatus()));
                if(StringUtils.isNotBlank(e.getCapitalEnterpriseName())){
                    e.setSignEntity(e.getCapitalEnterpriseName());
                }else{
                    e.setSignEntity(e.getEnterpriseName());
                }
                dataResultList.add(e);
                if(CollUtil.isNotEmpty(itemDataMap.get(e.getDeliverContractId()))){
                    dataResultList.addAll(itemDataMap.get(e.getDeliverContractId()));
                }
                e.setItemTypeText(ItemTypeEnum.getDescByCode(e.getItemType()));
                e.setBpmStatusText(BpmStatusEnum.getDescByCode(e.getBpmStatus()));
                e.setApprovalTypeText(ApprovalTypeEnum.getDescByCode(e.getApprovalType()));
                e.setSyncStatusSfText(SyncStatusSfEnum.getDescByCode(e.getSyncStatusSf()));
                if (StringUtils.isNotBlank(e.getOpenContract())) {
                    String[] openContract = e.getOpenContract().split(";");
                    StringJoiner joiner = new StringJoiner(";");
                    for (int i = 0; i < openContract.length; i++) {
                        joiner.add(OpenContractEnum.getDescByCode(openContract[i]));
                    }
                    e.setOpenContractText(joiner.toString());
                }
                e.setOrderType(contractTypeMap.get(e.getContractId()));
            });
            return dataResultList;
        }
    }
}
