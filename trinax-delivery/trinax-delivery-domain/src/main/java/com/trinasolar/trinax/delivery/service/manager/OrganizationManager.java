package com.trinasolar.trinax.delivery.service.manager;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.user.api.SysOrganizationFeign;
import com.trinasolar.trinax.user.constants.UserConstant;
import com.trinasolar.trinax.user.dto.output.SysOrganizationRespDTO;
import dtt.cache.redisclient.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;


@Service
@Slf4j
public class OrganizationManager {

    @Autowired
    private SysOrganizationFeign sysOrganizationFeign;

    @Autowired
    RedisUtil redisUtil;


    /**
     * 通过组织类型编码，查询组织信息
     *
     * @return
     */
    public List<String> getByOrganizationType(String orgTypeCode) {
        String redisKey = UserConstant.DATA_PERMISSION_ORGANIZATION_PREFIX + orgTypeCode;
        String organizationInfo = redisUtil.get(redisKey);
        if (StringUtils.isNotBlank(organizationInfo)) {
            List<SysOrganizationRespDTO> result = JacksonUtil.json2List(organizationInfo, SysOrganizationRespDTO.class);
            return result.stream().map(SysOrganizationRespDTO::getOrganizationCode).distinct().collect(Collectors.toList());
        } else {
            Result<List<SysOrganizationRespDTO>> orgResult = sysOrganizationFeign.getOrgListByType(orgTypeCode);
            if (!Boolean.TRUE.equals(orgResult.getSuccess()) || CollectionUtils.isEmpty(orgResult.getData())) {
                throw new BizException(ResultCode.FAIL.getCode(), "通过组织类型获取组织信息失败");
            }
            return orgResult.getData().stream().map(SysOrganizationRespDTO::getOrganizationCode).distinct().collect(Collectors.toList());
        }
    }

    /**
     * 通过userId查询组织类型
     *
     * @param userId
     * @return
     */
    public String getOrgTypeByUserId(String userId) {
        String redisKey = UserConstant.DATA_PERMISSION_USER_ORGANIZATION_PREFIX + userId;
        String organizationType = redisUtil.get(redisKey);
        if (StringUtils.isNotBlank(organizationType)) {
            return organizationType;
        } else {
            Result<String> orgTypeResult = sysOrganizationFeign.getOrganizationType(userId);
            if (!Boolean.TRUE.equals(orgTypeResult.getSuccess()) || StringUtils.isBlank(orgTypeResult.getData())) {
                throw new BizException(ResultCode.FAIL.getCode(), "没有获取到当前用户组织类型信息");
            }
            return orgTypeResult.getData();
        }
    }


}
