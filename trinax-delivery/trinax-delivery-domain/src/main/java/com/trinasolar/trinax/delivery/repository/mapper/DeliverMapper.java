package com.trinasolar.trinax.delivery.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.billing.dto.input.changeowner.InvoiceChangeOwnerReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.changeowner.DeliveryChangeOwnerReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverAuthListUserReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverListReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverOpsListReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverQueryReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.report.DeliveryReportReqDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.file.DeliverExportResDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverInfoResDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverOpsListRespDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.report.DeliveryReportResDTO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverPO;
import com.trinasolar.trinax.delivery.repository.domain.list.DeliveryListDTO;
import com.trinasolar.trinax.user.dto.output.SysSubDealerRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DeliverMapper extends BaseMapper<DeliverPO> {

    IPage<DeliveryListDTO> getExternalDeliverPage(Page<DeliveryListDTO> page,
                                                  @Param("req") DeliverListReqDTO req,
                                                  @Param("relation") List<SysSubDealerRespDTO> subDealerRespList);

    List<DeliveryListDTO> getExternalDeliverList(@Param("req") DeliverListReqDTO req,
                                                  @Param("relation") List<SysSubDealerRespDTO> subDealerRespList);

    IPage<DeliveryListDTO> getInternalDeliverList(Page<DeliveryListDTO> page,
                                                  @Param("req") DeliverListReqDTO query,
                                                  @Param("para") DeliverAuthListUserReqDTO para
    );

    Page<DeliveryListDTO> searchInternalDeliverList(Page<DeliveryListDTO> page,
                                                    @Param("req") DeliverListReqDTO query,
                                                    @Param("para") DeliverAuthListUserReqDTO para
    );

    Page<DeliveryListDTO> searchExternalDeliverList(Page<DeliveryListDTO> page,
                                                    @Param("req") DeliverListReqDTO query,
                                                    @Param("relation") List<SysSubDealerRespDTO> subDealerRespList
    );

    IPage<DeliverOpsListRespDTO> getOpsDeliverList(Page<DeliverOpsListRespDTO> page,
                                                   @Param("req") DeliverOpsListReqDTO query,
                                                   @Param("para") DeliverAuthListUserReqDTO para);

    List<DeliverExportResDTO> opsExportDeliverList(@Param("req") DeliverOpsListReqDTO query, @Param("para") DeliverAuthListUserReqDTO para);


    IPage<DeliverInfoResDTO> pcQryDeliverList(Page<DeliverInfoResDTO> page,
                                              @Param("req") DeliverQueryReqDTO query,
                                              @Param("para") DeliverAuthListUserReqDTO para);

    IPage<DeliverInfoResDTO> exportPcQryDeliverList(@Param("req") DeliverQueryReqDTO query,
                                              @Param("para") DeliverAuthListUserReqDTO para);

    List<DeliveryReportResDTO> deliveryAmountReport(@Param("req") DeliveryReportReqDTO reqDTO);

    List<DeliverPO> selectChangeOwnerRecord(@Param("req") DeliveryChangeOwnerReqDTO queryDTO);
}
