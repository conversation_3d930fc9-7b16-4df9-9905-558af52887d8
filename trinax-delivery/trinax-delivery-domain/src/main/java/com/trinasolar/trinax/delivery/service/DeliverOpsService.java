package com.trinasolar.trinax.delivery.service;

import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.delivery.dto.input.delivery.cancel.CancelDeliverAfterConfirmReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.check.EnterpriseBalanceCheckReq;
import com.trinasolar.trinax.delivery.dto.input.delivery.confirm.ConfirmDeliverOpsReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverOpsDetailReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverOpsListReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverReleaseReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.report.DeliverTrendReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.reverse.ReverseDeliverReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.save.OpsSaveDeliveryReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.submit.ApprovalStatusReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.submit.DeliverRetryApprovalReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.submit.DeliverRetryReqDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.DeliverContractResDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.check.EnterpriseBalanceCheckDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.detail.DeliverDetailOpsRespDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.detail.DeliverInfoOpsRespDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.file.DeliverDetailResDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverOpsListRespDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverReleaseResDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.report.DeliverTrendResDTO;

import java.util.List;


public interface DeliverOpsService {
    Result<PageResponse<DeliverOpsListRespDTO>> listDeliverOps(PageRequest<DeliverOpsListReqDTO> req);

    Result<PageResponse<DeliverDetailOpsRespDTO>> detailOpsDeliver(PageRequest<DeliverOpsDetailReqDTO> req);

    Result<Object> reverseDeliver(ReverseDeliverReqDTO req);

    Result<Object> confirmDeliver(ConfirmDeliverOpsReqDTO req);

    Result<List<DeliverContractResDTO>> qryDeliverInfo(DeliverOpsDetailReqDTO req);

    Result<List<DeliverDetailResDTO>> exportList(DeliverOpsListReqDTO req);

    Result<List<DeliverInfoOpsRespDTO>> deliverDetailByNo(DeliverOpsDetailReqDTO req);

    Result<Object> saveDeliver(OpsSaveDeliveryReqDTO req);

    Result<Object> saveDeliverConfirmed(OpsSaveDeliveryReqDTO req);

    Result<PageResponse<DeliverReleaseResDTO>> qryDeliverRelease(PageRequest<DeliverReleaseReqDTO> req);

    Result<List<DeliverReleaseResDTO>> qryDeliverReleaseList(DeliverReleaseReqDTO req);

    Result<String> retrySyncDr(DeliverRetryReqDTO req);

    Result<String> retrySubmitApproval(DeliverRetryApprovalReqDTO req);

    Result<String> approvalStatusAccess(ApprovalStatusReqDTO req);

    Result<List<DeliverTrendResDTO>> deliverTrend(DeliverTrendReqDTO req);

    Result<EnterpriseBalanceCheckDTO> balanceCheck(EnterpriseBalanceCheckReq enterpriseBalanceCheckReq);

    Result retrySubmitDistributeAmount(String deliverContractId);

    Result<Object> cancelDeliver(CancelDeliverAfterConfirmReqDTO req);
}
