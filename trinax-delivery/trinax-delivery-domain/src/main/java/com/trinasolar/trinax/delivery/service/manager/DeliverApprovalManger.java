package com.trinasolar.trinax.delivery.service.manager;

import com.trinasolar.trinax.cart.dto.input.ContractQueryReqIdListDTO;
import com.trinasolar.trinax.cart.dto.input.ContractQueryReqItemIdListDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.contract.api.ContractFeign;
import com.trinasolar.trinax.contract.dto.output.ContractDeliveryItemResDTO;
import com.trinasolar.trinax.contract.dto.output.ContractDeliveryResDTO;
import com.trinasolar.trinax.delivery.constants.enums.BpmStatusEnum;
import com.trinasolar.trinax.delivery.constants.enums.DistributeOperationTypeEnum;
import com.trinasolar.trinax.delivery.constants.enums.SyncStatusSfEnum;
import com.trinasolar.trinax.delivery.repository.atomicservice.*;
import com.trinasolar.trinax.delivery.repository.domain.*;
import com.trinasolar.trinax.delivery.service.biz.ops.DistributeAmountBiz;
import com.trinasolar.trinax.integration.api.IntegrationDeliveryFeign;
import com.trinasolar.trinax.integration.api.oa.IntegrationOAFeign;
import com.trinasolar.trinax.integration.dto.input.SfAccountBalanceEditReq;
import com.trinasolar.trinax.integration.dto.input.delivery.DrLine;
import com.trinasolar.trinax.integration.dto.input.delivery.NoReleaseQryReqDTO;
import com.trinasolar.trinax.integration.dto.input.oa.Attachment;
import com.trinasolar.trinax.integration.dto.input.oa.DeliverApprovalItem;
import com.trinasolar.trinax.integration.dto.input.oa.DeliverApprovalReqDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.NoReleaseResDTO;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.api.MarketingAccountFeign;
import com.trinasolar.trinax.partner.dto.output.EnterpriseDTO;
import com.trinasolar.trinax.partner.dto.output.MarketingAccountRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
@RefreshScope
public class DeliverApprovalManger {

    private final DeliverMapperService deliverMapperService;
    private final EnterpriseFeign enterpriseFeign;
    private final DeliverContractMapperService deliverContractMapperService;
    private final DeliverContractItemMapperService deliverContractItemMapperService;
    private final IntegrationDeliveryFeign integrationDeliveryFeign;
    private final IntegrationOAFeign integrationOAFeign;
    private final ContractFeign contractFeign;
    private final MarketingAccountFeign marketingAccountFeign;
    private final DeliverFileRelationMapperService deliverFileRelationMapperService;

    @Value("${trinax.fileHost}")
    private String fileHost;
    private final DistributeAccountBillMapperService distributeAccountBillMapperService;

    @Value("${deliver.openSystemOrgCode}")
    String openSystemOrgCode;

    private final DistributeAmountBiz distributeAmountBiz;


    public void bpmApproval(String deliverNo, String userEmail, List<DeliverContractPO> contractInfos) {

        DeliverPO deliver = deliverMapperService.lambdaQuery().eq(DeliverPO::getDeliverNo, deliverNo).one();
        String enterpriseId = StringUtils.isNotBlank(deliver.getCapitalEnterpriseId()) ?
                deliver.getCapitalEnterpriseId() : deliver.getEnterpriseId();

        List<DeliverContractItemPO> items = deliverContractItemMapperService.lambdaQuery().eq(DeliverContractItemPO::getDeliverNo, deliverNo).list();

        Map<String, List<DeliverContractItemPO>> itemMap = items.stream().collect(Collectors.groupingBy(DeliverContractItemPO::getDeliverContractId));

        final EnterpriseDTO enterpriseInfo = enterpriseFeign.getEnterpriseByEnterpriseId(enterpriseId);

        //查询 noRelease
        long noReleaseAmount = qryNoRelease(enterpriseInfo, items, deliverNo);

        List<ContractDeliveryResDTO> contractList = contractFeign.deliveryListByContractIds(new ContractQueryReqIdListDTO(
                contractInfos.stream().map(DeliverContractPO::getContractId)
                        .collect(Collectors.toList()))).getData();
        Map<String, ContractDeliveryResDTO> contractPayMap = contractList.stream().collect(Collectors.toMap(ContractDeliveryResDTO::getSfContractNo, Function.identity()));

        List<String> contractItemIds = items.stream().map(DeliverContractItemPO::getContractBusinessItemId).collect(Collectors.toList());
        // 远程调用获取所有的合同行信息
        List<ContractDeliveryItemResDTO> comtractItemList = contractFeign.deliveryListItemByContractItemIds(
                new ContractQueryReqItemIdListDTO(contractItemIds)).getData();
        Map<String, ContractDeliveryItemResDTO> contractItemMap = comtractItemList.stream().collect(Collectors.toMap(ContractDeliveryItemResDTO::getContractBusinessItemId, Function.identity()));


        List<String> sfContractNos = contractInfos.stream().map(DeliverContractPO::getSfContractNo).collect(Collectors.toList());
        List<DeliverFileRelationPO> deliverFileRelS = deliverFileRelationMapperService.lambdaQuery()
                .eq(DeliverFileRelationPO::getDeliverNo, deliverNo)
                .in(DeliverFileRelationPO::getSfContractNo, sfContractNos)
                .list();
        //加载账套信息
        Map<String, MarketingAccountRespDTO> marketingAccountMap = marketingAccountMapInit();

        contractInfos.stream().forEach(a -> {

            ContractDeliveryResDTO contractDeliveryRes = findValueByStr(a.getSfContractNo(), contractPayMap, ContractDeliveryResDTO.class);

            List<DeliverContractItemPO> itemPOList = findValuesByStr(a.getDeliverContractId(), itemMap);


            List<DeliverApprovalItem> deliverApprovalItems = approvalItemInit(itemPOList, a, contractItemMap);

            List<Attachment> fileList = fileListInit(deliverFileRelS, a);

            MarketingAccountRespDTO marketingAccountInfo = findValueByStr(a.getMarketingAccountId(), marketingAccountMap, MarketingAccountRespDTO.class);

            DeliverApprovalReqDTO deliverApprovalReq = deliverApprovalReqInit(a, items, enterpriseInfo,
                    contractDeliveryRes, deliverApprovalItems, fileList, marketingAccountInfo, userEmail, noReleaseAmount);

            //循环提交 bpm 审批
            Result<String> submitResult = integrationOAFeign.submitApplication(deliverApprovalReq);
            String bpmTaskId = "";
            String bpmErr = "";
            String bpmStatus = BpmStatusEnum.APPROVING.getCode();
            if (submitResult.getSuccess()) {
                bpmTaskId = submitResult.getData();
            } else {
                bpmErr = submitResult.getMessage();
                bpmStatus = BpmStatusEnum.FAIL.getCode();
            }
            a.setBpmTaskId(bpmTaskId);

            //更新合同信息
            updateDeliverContract(bpmTaskId, bpmErr, bpmStatus, noReleaseAmount, deliverNo, a.getSfContractNo());

            if (submitResult.getSuccess()){
                //同步分销余额到sf
                distributeAmountBiz.deductDistributeAmount(deliver,enterpriseInfo,a,itemPOList);
            }
        });
    }


    private List<Attachment> fileListInit(List<DeliverFileRelationPO> deliverFileRelS, DeliverContractPO deliverContract) {

        List<Attachment> fileList = new ArrayList<>();
        deliverFileRelS.stream().forEach(a -> {
            if (StringUtils.equals(a.getSfContractNo(), deliverContract.getSfContractNo())) {
                Attachment attachment = new Attachment();
                if (StringUtils.isNotBlank(a.getFileType()) && a.getFileType().contains("pdf")) {
                    attachment.setFileUrl(fileHost + "/filebff/file/download/browse/" + a.getFileId());
                } else {
                    attachment.setFileUrl(fileHost + "/filebff/file/download/pub/" + a.getFileId());
                }

                fileList.add(attachment);
            }
        });
        return fileList;
    }

    private long qryNoRelease(EnterpriseDTO enterpriseInfo, List<DeliverContractItemPO> items, String deliverNo) {

        List<DrLine> drLines = new ArrayList<>();
        items.forEach(c -> {
            DrLine line = new DrLine();
            line.setDrLineNo(c.getDeliverContractItemId());
            line.setDeliveryQty(String.valueOf(c.getQuantityP()));
            line.setContractItemId(c.getSfContractItemId());
            line.setCurrency(c.getCurrency());
            drLines.add(line);
        });

        NoReleaseQryReqDTO noReleaseQryReq = NoReleaseQryReqDTO.builder()
                .setAccountId(enterpriseInfo.getSfId())
                .setDrLines(drLines)
                .setDrNo(deliverNo);
        Result<NoReleaseResDTO> result = integrationDeliveryFeign.qryNoRelease(noReleaseQryReq);
        NoReleaseResDTO noReleaseResDTO = result.getData();
        if (result.getSuccess()) {
            return noReleaseResDTO.getAmount();
        }
        throw new BizException(ResultCode.FAIL.getCode(), result.getMessage());
    }

    private void updateDeliverContract(String bpmTaskId, String bpmErr, String bpmStatus, long noReleaseAmount, String deliverNo, String sfContractNo) {
        deliverContractMapperService.lambdaUpdate()
                .set(DeliverContractPO::getBpmTaskId, bpmTaskId)
                .set(DeliverContractPO::getBpmSubmitDesc, bpmErr)
                .set(DeliverContractPO::getBpmApprovalOpinions, "")
                .set(DeliverContractPO::getBpmStatus, bpmStatus)
                .set(DeliverContractPO::getNoReleaseAmount, noReleaseAmount)
                .eq(DeliverContractPO::getDeliverNo, deliverNo)
                .eq(DeliverContractPO::getSfContractNo, sfContractNo)
                .update();
    }

    private DeliverApprovalReqDTO deliverApprovalReqInit(DeliverContractPO a, List<DeliverContractItemPO> items,
                                                         EnterpriseDTO enterpriseInfo, ContractDeliveryResDTO contractDeliveryRes,
                                                         List<DeliverApprovalItem> deliverApprovalItems, List<Attachment> fileList,
                                                         MarketingAccountRespDTO marketingAccountInfo, String userEmail, long noReleaseAmount) {

        return DeliverApprovalReqDTO.builder()
                .setDeliveryItemList(deliverApprovalItems)
                .setFileList(fileList)
                .setCustomerName(enterpriseInfo.getName())
                .setCurrency(items.get(0).getCurrency())
                .setZzbId(a.getDeliverContractId())
                .setRegion("CHN")
                .setProposer(userEmail)
                .setOrderType("")
                .setCustomerMDMId(enterpriseInfo.getMdmId())
                .setTradeTerms(contractDeliveryRes.getIncoterm())
                .setPi("100% prepayment")
                .setDestinationPort(contractDeliveryRes.getDestinationPortId())
                .setInvoiceNumber("")
                .setSalesOrder(a.getDeliverNo())
                .setSubRegion(a.getArrivalProvinceCode())
                .setAttribute17("")
                .setAttribute3("")
                .setOuName(marketingAccountInfo.getAccountOuEn())
                .setOuCode(marketingAccountInfo.getOaAccountCode())
                .setSalesPersonRegion("CHN")
                .setApplication(contractDeliveryRes.getApplication())
                .setNoRelease(noReleaseAmount)
                .setIfHaveTP("");
    }

    private List<DeliverApprovalItem> approvalItemInit(List<DeliverContractItemPO> itemPOList, DeliverContractPO a, Map<String, ContractDeliveryItemResDTO> contractItemMap) {
        List<DeliverApprovalItem> deliverApprovalItems = new ArrayList<>();

        itemPOList.stream().forEach(b -> {
            ContractDeliveryItemResDTO itemResDTO = findValueByStr(b.getContractBusinessItemId(), contractItemMap, ContractDeliveryItemResDTO.class);

            DeliverApprovalItem deliverApprovalItem = DeliverApprovalItem.builder()
                    .setAmount(b.getQuantityP().toString())
                    //单片的价格
                    .setPrice(itemResDTO.getUnitPriceP().toString())
                    .setContractNO(a.getSfContractNo())
                    .setProdName(b.getSfProductName())
                    .setProdType(b.getPower().toString())
                    .setProdUnit(b.getUnitPriceW().toString())
                    .setTaxValue("");
            deliverApprovalItems.add(deliverApprovalItem);
        });

        return deliverApprovalItems;
    }

    private <T> List<T> findValuesByStr(String key, Map<String, List<T>> map) {
        List<T> obj = map.get(key);
        if (ObjectUtils.isEmpty(obj)) {
            obj = new ArrayList<>();
        }
        return obj;
    }

    private <T> T findValueByStr(String key, Map<String, T> map, Class<T> t) {
        T obj = map.get(key);
        if (ObjectUtils.isEmpty(obj)) {
            try {
                obj = t.newInstance();
            } catch (InstantiationException e) {
                throw new RuntimeException(e);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
        return obj;
    }

    private Map<String, MarketingAccountRespDTO> marketingAccountMapInit() {
        List<MarketingAccountRespDTO> marketingAccounts = marketingAccountFeign.listMarketAccount().getData();
        if (ObjectUtils.isEmpty(marketingAccounts)) {
            return new HashMap<>();
        }
        return marketingAccounts.stream().collect(Collectors.toMap(MarketingAccountRespDTO::getAccountId, Function.identity()));
    }

    public void bpmApproval(String deliverNo, String userEmail) {
    }
}
