package com.trinasolar.trinax.delivery.service.biz;

import com.trinasolar.trinax.basic.api.TodoListFeign;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoBizCodeEnum;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.delivery.constants.enums.DeliverStatus;
import com.trinasolar.trinax.delivery.constants.enums.DeliverySituationEnum;
import com.trinasolar.trinax.delivery.constants.enums.DeliveryStatusFake;
import com.trinasolar.trinax.delivery.dto.input.delivery.cancel.CancelDeliverReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.cancel.CleanItemReqDTO;
import com.trinasolar.trinax.delivery.manager.DeliveryTodoListManger;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractItemMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverMapperService;
import com.trinasolar.trinax.delivery.repository.domain.DeliverContractItemPO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverPO;
import com.trinasolar.trinax.delivery.service.biz.lock.business.DeliverBusinessLock;
import com.trinasolar.trinax.delivery.service.manager.DeliverBizManager;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.trinasolar.trinax.delivery.constants.DeliverResultCode.DELIVER_CONFIRMED_ERROR;
import static com.trinasolar.trinax.delivery.constants.DeliveryConstant.DELIVERY_REQUEST_MESSAGE_TOPIC;

@Service
@Slf4j
@RequiredArgsConstructor
public class DeliverCancelBiz implements DeliverBusinessLock {
    final SysUserFeign sysUserFeign;
    final DeliverMapperService deliverMapperService;
    final DeliverContractItemMapperService deliverContractItemMapperService;
    final DeliverBizManager deliverBizManager;
    final MqManager mqManager;
    final TodoListFeign todoListFeign;
    final RedissonClient redissonClient;
    final DeliveryTodoListManger deliveryTodoListManger;


    public Result<Object> cancelDelivery(CancelDeliverReqDTO req) {
//        Result<Object> result = this.lockBusiness(req.toString(), req.getDeliverNo(), CancelDeliverReqDTO.class, redissonClient);
        List<String> lockKeyList = new ArrayList<>();
        lockKeyList.add(req.getDeliverNo());
        return this.lockBusiness(JacksonUtil.bean2Json(req), lockKeyList, CancelDeliverReqDTO.class, redissonClient);
    }

    @Override
    public Result<Object> doBusiness(Object reqBody) {
        CancelDeliverReqDTO req = (CancelDeliverReqDTO) reqBody;
        String userId = req.getUserId();
        String deliverNo = req.getDeliverNo();
        SysUserRespDTO sysUser = sysUserFeign.getUserByUserId(userId).getData();
        String userName = sysUser.getUserName();
        LocalDateTime now = LocalDateTime.now();

        DeliverPO deliverPO = deliverMapperService.lambdaQuery().eq(DeliverPO::getDeliverNo, deliverNo).one();
        if (deliverPO == null) return Result.fail("当前发货单不存在");
        if (DeliverStatus.OPERATION_CONFIRMED.getCode().equals(deliverPO.getDeliverStatus()))
            return Result.fail(DELIVER_CONFIRMED_ERROR.getCode(), DELIVER_CONFIRMED_ERROR.getMessage());

        deliverMapperService.lambdaUpdate()
                .eq(DeliverPO::getDeliverNo, deliverNo)
                .set(DeliverPO::getDeliverStatus, DeliverStatus.CANCELED.getCode())
                .set(DeliverPO::getDeliverStatusFalse, DeliveryStatusFake.CANCELED.getCode())
                .set(DeliverPO::getUpdatedBy, userId)
                .set(DeliverPO::getUpdatedName, userName)
                .set(DeliverPO::getUpdatedTime, now)
                .set(DeliverPO::getComment, req.getComment())
                .update();

        List<DeliverContractItemPO> deliverContractItemPOList = deliverContractItemMapperService.lambdaQuery()
                .eq(DeliverContractItemPO::getDeliverNo, deliverNo).list();

        List<String> bizCodeList = new ArrayList<>();
        //  生态伙伴/一般客户取消待销售发货申请
        if (sysUser.externalUser() && deliverPO.getDeliverStatus().equals(DeliverStatus.SUBMIT.getCode())) {
            mqManager.sendTopic(DELIVERY_REQUEST_MESSAGE_TOPIC,
                    JacksonUtil.bean2Json(deliverBizManager.composeDeliverMessage(
                            DeliverySituationEnum.DR_EXTERNAL_CANCEL_WAIT_SALE_CONFIRM,
                            sysUser, deliverPO, deliverContractItemPOList)));
            bizCodeList.clear();
            //完成销售的代办（外部用户创建发货申请指给销售的代办）
            bizCodeList.add(TodoBizCodeEnum.DR_EXTERNAL_CREATE.getCode());
            //完成 运营退回发货申请，指给销售的代办
            bizCodeList.add(TodoBizCodeEnum.DR_INTERNAL_OPERATION_BACK.getCode());
        }

        //   生态伙伴/一般客户取消待运营确认的发货申请
        if (sysUser.externalUser() && deliverPO.getDeliverStatus().equals(DeliverStatus.SALES_CONFIRMED.getCode())) {
            mqManager.sendTopic(DELIVERY_REQUEST_MESSAGE_TOPIC,
                    JacksonUtil.bean2Json(deliverBizManager.composeDeliverMessage(
                            DeliverySituationEnum.DR_EXTERNAL_CANCEL_WAIT_OPERATION_CONFIRM,
                            sysUser, deliverPO, deliverContractItemPOList)));
            bizCodeList.clear();
            //完成运营的代办（销售确认后，给运营发的代办）
            bizCodeList.add(TodoBizCodeEnum.DR_INTERNAL_CONFIRM.getCode());
        }

        //   分销销售取消待销售确认的发货申请
        if (sysUser.internalUser() && deliverPO.getDeliverStatus().equals(DeliverStatus.SUBMIT.getCode())) {
            mqManager.sendTopic(DELIVERY_REQUEST_MESSAGE_TOPIC,
                    JacksonUtil.bean2Json(deliverBizManager.composeDeliverMessage(
                            DeliverySituationEnum.DR_INTERNAL_CANCEL_WAIT_SALE_CONFIRM,
                            sysUser, deliverPO, deliverContractItemPOList)));
            bizCodeList.clear();
            //完成 外部用户创建发货申请，指给销售的代办
            bizCodeList.add(TodoBizCodeEnum.DR_EXTERNAL_CREATE.getCode());
            //完成 运营退回发货申请，指给销售的代办
            bizCodeList.add(TodoBizCodeEnum.DR_INTERNAL_OPERATION_BACK.getCode());
        }

        //   分销销售取消待运营确认的发货申请
        if (sysUser.internalUser() && deliverPO.getDeliverStatus().equals(DeliverStatus.SALES_CONFIRMED.getCode())) {
            mqManager.sendTopic(DELIVERY_REQUEST_MESSAGE_TOPIC,
                    JacksonUtil.bean2Json(deliverBizManager.composeDeliverMessage(
                            DeliverySituationEnum.DR_INTERNAL_CANCEL_WAIT_OPERATION_CONFIRM,
                            sysUser, deliverPO, deliverContractItemPOList)));
            bizCodeList.clear();
            //完成销售确认时，指给运营的代办
            bizCodeList.add(TodoBizCodeEnum.DR_INTERNAL_CONFIRM.getCode());
        }
        //完成代办消息
        deliveryTodoListManger.updateTodoList(deliverPO, now, bizCodeList, "");
        return Result.ok(req.getDeliverNo());
    }

    @Override
    public Result<Object> lockBusiness(String req, List lockKeyListStr, Class beanType, RedissonClient redissonClient) {
        return DeliverBusinessLock.super.lockBusiness(req, lockKeyListStr, beanType, redissonClient);
    }

    public Result<String> cleanDeliverItem(CleanItemReqDTO req) {
        DeliverPO deliver = deliverMapperService.lambdaQuery().eq(DeliverPO::getDeliverNo, req.getDeliverNo()).one();

        if (ObjectUtils.isEmpty(deliver)) {
            throw new BizException(ResultCode.FAIL.getCode(), "发货申请单不存在");
        }
        if (DeliverStatus.OPERATION_CONFIRMED.getCode().equals(deliver.getDeliverStatus())) {
            throw new BizException(ResultCode.FAIL.getCode(), "运营已确认，禁止删除发货行");
        }
        if (ObjectUtils.isEmpty(req.getItemIds())) {
            return Result.ok();
        }
        deliverContractItemMapperService.lambdaUpdate()
                .eq(DeliverContractItemPO::getDeliverNo, req.getDeliverNo())
                .in(DeliverContractItemPO::getDeliverContractItemId, req.getItemIds())
                .remove();

        return Result.ok();

    }
}
