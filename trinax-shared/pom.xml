<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<modules>
		<module>trinax-common</module>
	</modules>
	<parent>
		<groupId>dtt.asset</groupId>
		<artifactId>dtt-base</artifactId>
		<version>0.0.2.RELEASE</version>
		<relativePath>../dtt-component/dtt-base/pom.xml</relativePath>
	</parent>
	<packaging>pom</packaging>
	<groupId>com.trinasolar</groupId>
	<artifactId>trinax-shared</artifactId>
	<version>0.0.2.RELEASE</version>
	<name>trinax-shared</name>
	<description>trinax-shared project for Spring Boot</description>
	<properties>
		<version.suffix>-SNAPSHOT</version.suffix>
		<java.version>8</java.version>
		<common.version>0.0.4${version.suffix}</common.version>
		<common-utils.version>0.0.2.RELEASE</common-utils.version>
		<dtt-rocketmq.version>0.0.2.RELEASE</dtt-rocketmq.version>
		<dtt-queue.version>0.0.2.RELEASE</dtt-queue.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
			<!--<version>2.2.9.RELEASE</version>-->
		</dependency>
		<!--<dependency>-->
			<!--<groupId>org.springframework.cloud</groupId>-->
			<!--<artifactId>spring-cloud-starter-netflix-ribbon</artifactId>-->
		<!--</dependency>-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>io.swagger.core.v3</groupId>
			<artifactId>swagger-annotations</artifactId>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>dtt.asset</groupId>
				<artifactId>dtt-framework-common-utils</artifactId>
				<version>${common-utils.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-user-api</artifactId>
				<version>0.0.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-user-domain</artifactId>
				<version>${common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-contract-api</artifactId>
				<version>${common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-contract-domain</artifactId>
				<version>${common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-integration-api</artifactId>
				<version>${common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-integration-domain</artifactId>
				<version>${common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-partner-api</artifactId>
				<version>0.0.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-partner-domain</artifactId>
				<version>${common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-delivery-api</artifactId>
				<version>${common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-delivery-domain</artifactId>
				<version>${common.version}</version>
			</dependency>

			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-auth-center-api</artifactId>
				<version>${common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-auth-center-domain</artifactId>
				<version>${common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-auth-center-core</artifactId>
				<version>${common.version}</version>
			</dependency>

			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-masterdata-api</artifactId>
				<version>0.0.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-masterdata-domain</artifactId>
				<version>${common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-basic-api</artifactId>
				<version>${common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-basic-domain</artifactId>
				<version>${common.version}</version>
			</dependency>

			<dependency>
				<groupId>dtt.asset</groupId>
				<artifactId>dtt-framework-rocketmq</artifactId>
				<version>${dtt-rocketmq.version}</version>
			</dependency>

			<dependency>
				<groupId>dtt.asset</groupId>
				<artifactId>dtt-framework-queue</artifactId>
				<version>${dtt-queue.version}</version>
				<scope>compile</scope>
			</dependency>
<!--			<dependency>-->
<!--				<groupId>com.trinasolar.lapetus.log</groupId>-->
<!--				<artifactId>log2-spring-boot-starter</artifactId>-->
<!--				<version>2.7.0-SNAPSHOT</version>-->
<!--			</dependency>-->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>easyexcel</artifactId>
				<version>3.3.2</version>
			</dependency>
			<dependency>
				<groupId>com.squareup.okhttp3</groupId>
				<artifactId>okhttp</artifactId>
				<version>4.11.0</version>
			</dependency>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-all</artifactId>
				<version>5.8.22</version>
			</dependency>
			<dependency>
				<groupId>com.github.xiaoymin</groupId>
				<artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
				<version>4.3.0</version>
			</dependency>
			<dependency>
				<groupId>io.swagger.core.v3</groupId>
				<artifactId>swagger-annotations</artifactId>
				<version>2.2.9</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
</project>
