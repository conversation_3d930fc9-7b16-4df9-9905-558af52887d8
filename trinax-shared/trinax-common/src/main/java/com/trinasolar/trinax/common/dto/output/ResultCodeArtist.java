package com.trinasolar.trinax.common.dto.output;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description:
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ResultCodeArtist implements Serializable {

    private Boolean success;

    private String code;

    private String message;

}
