package com.trinasolar.trinax.common.utils.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 下载文件设置样式
 * @date 2022/07/24 17:06
 */
public class DownloadCommon extends AbstractColumnWidthStyleStrategy {


    public static HorizontalCellStyleStrategy getHorizontalCellStyleStrategy() {
        // 表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        headWriteCellStyle.setBottomBorderColor(IndexedColors.WHITE.index);
        headWriteCellStyle.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.index);
        headWriteCellStyle.setRightBorderColor(IndexedColors.GREY_40_PERCENT.index);
        WriteFont writeFont = new WriteFont();
        writeFont.setFontHeightInPoints(Short.parseShort("11"));
        writeFont.setFontName("Calibri");
        writeFont.setColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(writeFont);
        //垂直-居中
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //水平-左
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);

        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

//        DataFormatData format = new DataFormatData();
//        format.setFormat("?");
//        format.setIndex((short)49);
//        contentWriteCellStyle.setDataFormatData(format);

        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        return horizontalCellStyleStrategy;
    }

    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        if (isHead) {
            int width = (cell.getStringCellValue().getBytes().length + 2) * 256;
            writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), width);
        }
    }
}
