package com.trinasolar.trinax.sharedapi.controller.product;

import com.trinasolar.trinax.auth.core.details.AuthUserDetails;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.masterdata.api.ProductFavouriteFeign;
import com.trinasolar.trinax.masterdata.dto.input.favourite.AppProductFavouriteQueryDTO;
import com.trinasolar.trinax.masterdata.dto.input.favourite.ProductFavouriteAddReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.favourite.AppProductFavouriteRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> Yang
 */
@Slf4j
@RequestMapping("/product-favourite")
@RestController
@RequiredArgsConstructor
@Tag(name = "收藏接口")
public class ProductFavouriteController {

    private final ProductFavouriteFeign productFavouriteFeign;

    @Operation(summary = "收藏产品")
    @PostMapping("/add")
    Result<String> add(@RequestBody ProductFavouriteAddReqDTO reqDTO) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        reqDTO.setLoginUserId(authUser.getUserIdStr());
        reqDTO.setLoginUserName(authUser.getName());
        return productFavouriteFeign.add(reqDTO);
    }

    @Operation(summary = "取消收藏")
    @PostMapping("/delete")
    Result<Void> delete(@RequestParam String productFavouriteId) {
        return productFavouriteFeign.delete(productFavouriteId);
    }

    @PostMapping("/pageContractResPc")
    @Operation(summary = "收藏分页查询-app")
    Result<PageResponse<AppProductFavouriteRespDTO>> appPageProductFavourite(@RequestBody PageRequest<AppProductFavouriteQueryDTO> pageReqDTO) {
        pageReqDTO.getQuery().setLoginUserId(AuthUserHelper.getAuthUser().getUserIdStr());
        return productFavouriteFeign.appPageProductFavourite(pageReqDTO);
    }


}
