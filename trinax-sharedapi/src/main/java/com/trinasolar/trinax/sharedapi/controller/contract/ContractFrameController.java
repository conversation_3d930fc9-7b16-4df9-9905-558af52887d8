package com.trinasolar.trinax.sharedapi.controller.contract;


import com.trinasolar.trinax.auth.core.details.AuthUserDetails;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.contract.api.ContractFrameFeign;
import com.trinasolar.trinax.contract.dto.input.contract.frame.ContactFrameAppReqDTO;
import com.trinasolar.trinax.contract.dto.output.contract.frame.ContractFrameAppResDTO;
import com.trinasolar.trinax.contract.dto.output.contract.frame.ContractFrameDetailAppResDTO;
import com.trinasolar.trinax.contract.dto.output.contract.frame.ContractFrameResDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


@Slf4j
@Tag(name = "合同框架API")
@RestController
@RequiredArgsConstructor
@RequestMapping("/contract-frame")
public class ContractFrameController {

    private final ContractFrameFeign contractFrameFeign;

    @PostMapping("/pageContractFrameApp")
    @Operation(summary = "框架合同-分页查询-app端显示 ")
    Result<PageResponse<ContractFrameAppResDTO>> pageContractFrameApp(@RequestBody PageRequest<ContactFrameAppReqDTO> pageReqDTO) {
        AuthUserDetails userDetails = AuthUserHelper.getAuthUser();
        pageReqDTO.getQuery().setLoginUserId(userDetails.getUserIdStr());
        pageReqDTO.getQuery().setLoginUserType(userDetails.getUserType());
        return contractFrameFeign.pageContractFrameApp(pageReqDTO);
    }


    @GetMapping("/detailContractFrameApp")
    @Operation(summary = "框架合同-详情查询-app端显示 ")
    Result<ContractFrameDetailAppResDTO> detailContractFrameApp(@RequestParam String contractNo) {
        return contractFrameFeign.detailContractFrameApp(contractNo);
    }

    @GetMapping("/quickOrder/queryByEnterpriseId")
    @Operation(summary = "渠道订单基于企业id查询框架合同")
    Result<ContractFrameResDTO> quickOrderOne(@RequestParam String enterpriseId) {
        return contractFrameFeign.quickOrderOne(enterpriseId);
    }

}
