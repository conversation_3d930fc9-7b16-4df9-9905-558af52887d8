package com.trinasolar.trinax.sharedapi.controller.file;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.basic.api.UploadFileFeign;
import com.trinasolar.trinax.basic.constants.enums.FileReadModeEnum;
import com.trinasolar.trinax.basic.dto.input.SaveUploadFileReqDTO;
import com.trinasolar.trinax.basic.dto.output.GetUploadFileResDTO;
import com.trinasolar.trinax.basic.dto.output.SaveUploadFileResDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.integration.api.ContractManagerFeign;
import com.trinasolar.trinax.integration.api.IntegrationFileFeign;
import com.trinasolar.trinax.integration.api.TosFeign;
import com.trinasolar.trinax.integration.constants.enums.EncodeTypeEnum;
import com.trinasolar.trinax.integration.dto.output.tos.TosFileDTO;
import com.trinasolar.trinax.sharedapi.utils.FeignUtil;
import com.trinasolar.trinax.sharedapi.utils.FileUtil;
import feign.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.InputStream;

@Slf4j
@RestController
@RefreshScope
@RequiredArgsConstructor
@Tag(name = "文件管理")
public class FileController {

    @Value("${upload.file.extension:png,jpg,jpeg,pdf,bmp,ms_bmp,zip,rar,msg}")
    private String fileExtensions;
    @Value("${upload.file.content-type:image/bmp,image/x-ms-bmp,image/png,image/jpeg,application/pdf,image/x-ms-bmp,application/zip,application/x-rar-compressed,application/vnd.ms-outlook,application/octet-stream}")
    private String contentTypes;
    private final TosFeign tosFeign;
    private final UploadFileFeign uploadFileFeign;
    private final IntegrationFileFeign integrationFileFeign;
    private final ContractManagerFeign contractManagerFeign;


    @Operation(summary = "上传文件-公共阅读模式")
    @PostMapping(value = "/file/upload/pub", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<SaveUploadFileResDTO> upload(@RequestPart("file") MultipartFile file) {
        log.info("上传文件-公共阅读模式，originalFilename={}", file.getOriginalFilename());
        log.info("上传文件-公共阅读模式，contentType={}", file.getContentType());
        if (!FileUtil.checkFile(file, fileExtensions, contentTypes)) {
            log.warn("上传文件-公共阅读模式，文件不合法，fileName={}", file.getOriginalFilename());
            return Result.fail("文件不合法");
        }
        Result<TosFileDTO> tosRst = tosFeign.upload(file);
        if (Boolean.FALSE.equals(tosRst.getSuccess())) {
            return Result.fail(tosRst.getMessage());
        }

        String userId = AuthUserHelper.getAuthUser().getUserIdStr();
        String userName = AuthUserHelper.getAuthUser().getUsername();
        SaveUploadFileReqDTO saveUploadFileReq = BeanUtil.copyProperties(tosRst.getData(), SaveUploadFileReqDTO.class);
        saveUploadFileReq.setReadMode(FileReadModeEnum.PUB.getCode());
        saveUploadFileReq.setUserId(userId);
        saveUploadFileReq.setUserName(userName);
        return uploadFileFeign.save(saveUploadFileReq);
    }

    @Operation(summary = "下载文件")
    @GetMapping("/file/download")
    public ResponseEntity download(@RequestParam("url") String url) {
        if (StrUtil.isEmpty(url)) {
            log.warn("下载文件，url=为空");
            return new ResponseEntity(HttpStatus.NOT_FOUND);
        }

        return downloadFile(url, true);
    }


    @Operation(summary = "下载文件-需要 base64编码")
    @GetMapping("/file/download/integration")
    public ResponseEntity downloadDoc(@RequestParam("url") String url) {
        if (StrUtil.isEmpty(url)) {
            log.warn("下载文件，url=为空");
            return new ResponseEntity(HttpStatus.NOT_FOUND);
        }
        return downloadFile(url, false);
    }

    @Operation(summary = "小程序下载合同文件")
    @GetMapping("/file/download/contractFileDownForMP")
    public ResponseEntity contractFileDownForMP(@RequestParam("url") String url,
                                                @RequestParam("documentId") String documentId,
                                                @RequestParam("fileName") String fileName,
                                                @RequestParam("fileName") String fileType,
                                                HttpServletRequest request) {
        log.info("小程序下载合同文件url:{}  documentId: {} fileName: {} content-type:{}", url, documentId, fileName, request.getHeader("User-Agent").toLowerCase());
        if (StrUtil.isNotBlank(url)) {
            return downloadFileForMoreWeb(url, request, fileType);
        } else if (StrUtil.isNotBlank(documentId)) {
            return qysDownloadFile(documentId, fileName, fileType, request);
        } else {
            log.warn("小程序合同下载，下载信息为空");
            return new ResponseEntity(HttpStatus.NOT_FOUND);
        }
    }

    @Operation(summary = "下载文件-私有阅读模式")
    @GetMapping("/file/download/pri/{fileId}")
    public ResponseEntity downloadWithPri(@PathVariable("fileId") String fileId) {
        if (StrUtil.isEmpty(fileId)) {
            log.warn("下载文件-私有阅读模式，fileId为空");
            return new ResponseEntity(HttpStatus.NOT_FOUND);
        }

        Result<GetUploadFileResDTO> result = uploadFileFeign.getPriByFileId(fileId);
        if (Boolean.FALSE.equals(result.getSuccess()) || result.getData() == null) {
            log.warn("下载文件-私有阅读模式，文件信息不存在，result={}", JacksonUtil.bean2Json(result));
            return new ResponseEntity(HttpStatus.NOT_FOUND);
        }
        return downloadFile(result.getData().getUrl(), true);
    }

    private ResponseEntity downloadFile(String url, boolean isTos) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getResponse();
        if (response == null) {
            return null;
        }

        InputStream in = null;
        BufferedInputStream bufferedIn = null;
        BufferedOutputStream bufferedOutput = null;
        Response docRes;
        if (isTos) {
            docRes = tosFeign.download(url);
        } else {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
            docRes = integrationFileFeign.downloadDoc(url, request.getHeader("User-Agent"), "pdf",EncodeTypeEnum.BASE64.getCode());
        }
        if (docRes.status() == HttpStatus.NOT_FOUND.value()) {
            log.warn("下载文件-私有阅读模式，文件不存在，url={}", url);
            return new ResponseEntity(HttpStatus.NOT_FOUND);
        }

        try {
            response.setContentType(FeignUtil.getHeader(docRes, "Content-Type"));
            response.setHeader("Content-Disposition", FeignUtil.getHeader(docRes, "Content-Disposition"));
            response.setHeader("Pragma", "no-cache");

            Response.Body body = docRes.body();
            in = body.asInputStream();
            bufferedIn = new BufferedInputStream(in);
            bufferedOutput = new BufferedOutputStream(response.getOutputStream());

            IOUtils.copy(bufferedIn, bufferedOutput);
            bufferedOutput.flush();
            return null;
        } catch (Exception e) {
            log.error("下载文件异常", e);
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(bufferedOutput);
            IOUtils.closeQuietly(bufferedIn);
            IOUtils.closeQuietly(in);
        }
    }

    private ResponseEntity commonDownFile(Response docRes) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getResponse();
        if (response == null) {
            return null;
        }

        InputStream in = null;
        BufferedInputStream bufferedIn = null;
        BufferedOutputStream bufferedOutput = null;
        try {
            response.setContentType(FeignUtil.getHeader(docRes, "Content-Type"));
            response.setHeader("Content-Disposition", FeignUtil.getHeader(docRes, "Content-Disposition"));
            response.setHeader("Pragma", "no-cache");

            Response.Body body = docRes.body();
            in = body.asInputStream();
            bufferedIn = new BufferedInputStream(in);
            bufferedOutput = new BufferedOutputStream(response.getOutputStream());

            IOUtils.copy(bufferedIn, bufferedOutput);
            bufferedOutput.flush();
            return null;
        } catch (Exception e) {
            log.error("下载文件异常", e);
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(bufferedOutput);
            IOUtils.closeQuietly(bufferedIn);
            IOUtils.closeQuietly(in);
        }
    }


    private ResponseEntity qysDownloadFile(String documentId, String fileName, String fileType, HttpServletRequest request) {
        Response response = contractManagerFeign.signFileDown(documentId, fileName, request.getHeader("User-Agent").toLowerCase(), fileType);
        commonDownFile(response);
        return null;
    }

    /**
     * 三方文件下载 兼容 safari 浏览器
     */
    private ResponseEntity downloadFileForMoreWeb(String url, HttpServletRequest request, String fileType) {
        Response response = integrationFileFeign.downloadDoc(url, request.getHeader("User-Agent").toLowerCase(), fileType, EncodeTypeEnum.URL_ENCODE.getCode());
        commonDownFile(response);
        return null;
    }

}
