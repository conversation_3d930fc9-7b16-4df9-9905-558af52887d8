package com.trinasolar.trinax.sharedapi.controller.user;

//import com.trinasolar.cloud.lapetus.logback.appender.aspect.IgnoreLogging;
import com.trinasolar.trinax.auth.core.details.AuthUserDetails;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.user.api.SysDealerSalesRelationFeign;
import com.trinasolar.trinax.user.dto.input.*;
import com.trinasolar.trinax.user.dto.output.RequirementEnterpriseRespDTO;
import com.trinasolar.trinax.user.dto.output.SysDealerRespDTO;
import com.trinasolar.trinax.user.dto.output.SysDealerSalesRespDTO;
import com.trinasolar.trinax.user.dto.output.authentication.AuthenticationResDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> Yang
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/dealer-sales-relation")
@Tag(name = "用户-企业-销售关系")
public class DealerSalesRelationController {

    private final SysDealerSalesRelationFeign sysDealerSalesRelationFeign;

    @Operation(summary = "分配专属销售")
    @PostMapping({"/allocateExclusiveSales"})
    public Result<Void> allocateExclusiveSales(@RequestBody @Valid AllocateExclusiveSalesDTO allocateExclusiveSalesDTO) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        allocateExclusiveSalesDTO.setUpdatedBy(authUser.getUserIdStr());
        allocateExclusiveSalesDTO.setUpdatedName(authUser.getName());
        return sysDealerSalesRelationFeign.allocateExclusiveSales(allocateExclusiveSalesDTO);

    }

    @Operation(summary = "认证失败")
    @PostMapping("/authenticationFail")
    public Result<Void> authenticationFail(@RequestBody @Valid AuthenticationFailedDTO reqDTO) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        reqDTO.setUpdateBy(authUser.getUserIdStr());
        reqDTO.setUpdateName(authUser.getName());
        return sysDealerSalesRelationFeign.authenticationFail(reqDTO);
    }

    @Operation(summary = "认证成功")
    @PostMapping("/authenticationSuccess")
    public Result<AuthenticationResDTO> authenticationSuccess(@RequestBody @Valid AuthenticationSuccessDTO reqDTO) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        reqDTO.setUpdateBy(authUser.getUserIdStr());
        reqDTO.setUpdateName(authUser.getName());
        return sysDealerSalesRelationFeign.authenticationSuccess(reqDTO);
    }


//    @IgnoreLogging
    @Operation(summary = "我的客户联系人-分页（内部人员）")
    @PostMapping("/pageMyDealers")
    public Result<PageResponse<SysDealerRespDTO>> pageMyDealers(@RequestBody PageRequest<MyDealerQueryDTO> pageReqDTO) {
        pageReqDTO.getQuery().setSalesUserId(AuthUserHelper.getAuthUser().getUserIdStr());
        return sysDealerSalesRelationFeign.pageMyDealers(pageReqDTO);
    }

    @Operation(summary = "根据企业加外部用户查询专属销售列表接口-意向单&发货申请")
    @GetMapping("/salesByEnterpriseIdAndDealerUserId")
    public Result<List<SysDealerSalesRespDTO>> salesByEnterpriseIdAndDealerUserId(@RequestParam String enterpriseId) {
        return sysDealerSalesRelationFeign.salesByEnterpriseIdAndDealerUserId(enterpriseId, AuthUserHelper.getAuthUser().getUserIdStr());
    }

    @Operation(summary = "需求单选择的企业-列表 ")
    @PostMapping("/requirementEnterprises")
    public Result<List<RequirementEnterpriseRespDTO>> requirementEnterprises(@RequestBody @Valid RequirementEnterpriseQueryDTO reqDTO) {
        return sysDealerSalesRelationFeign.requirementEnterprises(reqDTO);
    }
}
