server:
  port: 78
spring:
  redis:
    # redis数据库索引（默认为0），我们使用索引为3的数据库，避免和其他数据库冲突
    database: 0
    # redis服务器地址（默认为localhost）
    host: ${REDIS_HOST:*************}
    # redis端口（默认为6379）
    port: ${REDIS_PORT:6379}
    # redis访问密码（默认为空）
#    password: ${REDIS_PASSWORD:Trina@123}
    # redis连接超时时间（单位为毫秒）
    timeout: 30000

rocketmq:
  name-server: ${ROCKETMQ_HOST:*************:9876}
  producer:
    # 发送同一类消息的设置为同一个group，保证唯一
    group: sharedapi

#日志上报环境地址
logging:
  collector:
    #dev
    address: localhost:8088

tianyancha:
  mock-on: false

# 热线电话缓存电话
config:
  hotline: 400
  helpDocUrl: https://trinaxfile-uat.deloitte.com/filebff/file/download/browse/eb1083d42e8842598a5647a4b118e739