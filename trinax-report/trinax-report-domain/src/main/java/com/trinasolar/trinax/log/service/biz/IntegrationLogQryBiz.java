package com.trinasolar.trinax.log.service.biz;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.constants.enums.BizOperationEnum;
import com.trinasolar.trinax.integration.constants.enums.BizTypeEnum;
import com.trinasolar.trinax.integration.constants.enums.IntegrationSystemEnum;
import com.trinasolar.trinax.log.dto.input.integration.IntegrationQryReqDTO;
import com.trinasolar.trinax.log.dto.output.integration.IntegrationLogResDTO;
import com.trinasolar.trinax.log.repository.mapper.IntegrationDataRecordMapper;
import com.trinasolar.trinax.log.repository.po.IntegrationDataRecordPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@RefreshScope
public class IntegrationLogQryBiz {
    private final IntegrationDataRecordMapper integrationDataRecordMapper;

    public Result<List<IntegrationLogResDTO>> integrationLogList(IntegrationQryReqDTO req) {
        LambdaQueryWrapper<IntegrationDataRecordPO> condition = conditionInit(req);

        List<IntegrationDataRecordPO> records = integrationDataRecordMapper.selectList(condition);
        List<IntegrationLogResDTO> resultList = BeanUtil.copyToList(records, IntegrationLogResDTO.class);
        resultList.forEach(a -> {
            a.setBizOperationName(BizOperationEnum.getDescByCode(a.getBizOperation()));
            a.setBizTypeName(BizTypeEnum.getDescByCode(a.getBizType()));
            a.setIntegrationSystemName(IntegrationSystemEnum.getDescByCode(a.getIntegrationSystem()));
        });

        return Result.ok(resultList);
    }

    private LambdaQueryWrapper<IntegrationDataRecordPO> conditionInit(IntegrationQryReqDTO req) {

        LambdaQueryWrapper<IntegrationDataRecordPO> condition = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(req.getBizNo())) {
            condition.eq(IntegrationDataRecordPO::getBizNo, req.getBizNo());
        }
        if (StringUtils.isNotBlank(req.getBizOperation())) {
            condition.eq(IntegrationDataRecordPO::getBizOperation, req.getBizOperation());
        }
        if (StringUtils.isNotBlank(req.getBizType())) {
            condition.eq(IntegrationDataRecordPO::getBizType, req.getBizType());
        }
        if (StringUtils.isNotBlank(req.getIntegrationSystem())) {
            condition.eq(IntegrationDataRecordPO::getIntegrationSystem, req.getIntegrationSystem());
        }
        if (StringUtils.isNotBlank(req.getIntegrationForward())) {
            condition.eq(IntegrationDataRecordPO::getIntegrationForward, req.getIntegrationForward());
        }
        if (StringUtils.isNotBlank(req.getUserId())) {
            condition.eq(IntegrationDataRecordPO::getUserId, req.getUserId());
        }
        if (StringUtils.isNotBlank(req.getResponseStatus())) {
            condition.eq(IntegrationDataRecordPO::getResponseStatus, req.getResponseStatus());
        }
        if (req.getCreatedTimeStart() != null) {
            condition.ge(IntegrationDataRecordPO::getRequestTime, req.getCreatedTimeStart());
        }
        if (req.getCreatedTimeEnd() != null) {
            condition.le(IntegrationDataRecordPO::getRequestTime, req.getCreatedTimeEnd());
        }

        condition.orderByDesc(IntegrationDataRecordPO::getRequestTime);
        return condition;
    }

    public Result<PageResponse<IntegrationLogResDTO>> integrationLogPage(PageRequest<IntegrationQryReqDTO> req) {
        LambdaQueryWrapper<IntegrationDataRecordPO> condition = conditionInit(req.getQuery());
        Page<IntegrationDataRecordPO> page = new Page<>(req.getIndex(), req.getSize());
        IPage<IntegrationDataRecordPO> resultPage = integrationDataRecordMapper.selectPage(page, condition);
        List<IntegrationLogResDTO> resultList = BeanUtil.copyToList(resultPage.getRecords(), IntegrationLogResDTO.class);
        resultList.forEach(a -> {

            a.setBizOperationName(BizOperationEnum.getDescByCode(a.getBizOperation()));
            a.setBizTypeName(BizTypeEnum.getDescByCode(a.getBizType()));
            a.setIntegrationSystemName(IntegrationSystemEnum.getDescByCode(a.getIntegrationSystem()));
        });

        return Result.ok(PageResponse.toResult(
                req.getIndex(),
                req.getSize(),
                (int) resultPage.getTotal(),
                resultList));
    }
}
