package com.trinasolar.trinax.log.repository.atomicservice.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.PageSumResponse;
import com.trinasolar.trinax.report.dto.input.OutBoundSoReqDTO;
import com.trinasolar.trinax.report.dto.output.OutBoundSoDTO;
import com.trinasolar.trinax.log.repository.atomicservice.OutBoundSoMapperService;
import com.trinasolar.trinax.log.repository.mapper.OutBoundSoMapper;
import com.trinasolar.trinax.log.repository.po.OutBoundSoPO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created on 2024/7/16, 16:14
 * author: zhangyang
 */
@Service
public class OutBoundSoMapperServiceImpl extends ServiceImpl<OutBoundSoMapper, OutBoundSoPO>  implements OutBoundSoMapperService {
    @Override
    public PageSumResponse<OutBoundSoDTO> pageQuery(PageRequest<OutBoundSoReqDTO> pageReqDTO) {
        LambdaQueryWrapper<OutBoundSoPO> queryWrapper = new LambdaQueryWrapper<>();
        OutBoundSoReqDTO outBoundSoReqDTO=pageReqDTO.getQuery();
        queryWrapper.like(StringUtils.isNotBlank(outBoundSoReqDTO.getPi()),OutBoundSoPO::getPi,outBoundSoReqDTO.getPi())
                .eq(StringUtils.isNotBlank(outBoundSoReqDTO.getOperationsPerson()),OutBoundSoPO::getOperationsNumber,outBoundSoReqDTO.getOperationsPerson())
                .like(StringUtils.isNotBlank(outBoundSoReqDTO.getCustomerName()),OutBoundSoPO::getCustomerName,outBoundSoReqDTO.getCustomerName())
                .eq(StringUtils.isNotBlank(outBoundSoReqDTO.getDepartment()),OutBoundSoPO::getDepartment,outBoundSoReqDTO.getDepartment());

        if(StringUtils.isNotBlank(outBoundSoReqDTO.getBeginTime())){
            queryWrapper.ge(OutBoundSoPO::getOutboundTime,outBoundSoReqDTO.getBeginTime().substring(0,10));
        }
        if(StringUtils.isNotBlank(outBoundSoReqDTO.getEndTime())){
            queryWrapper.le(OutBoundSoPO::getOutboundTime,outBoundSoReqDTO.getEndTime().substring(0,10));
        }
        queryWrapper.orderByDesc(OutBoundSoPO::getOutboundTime)
                .orderByDesc(OutBoundSoPO::getDoubleSignDate);

        Page<OutBoundSoPO> page = new Page<>(pageReqDTO.getIndex(), pageReqDTO.getSize());
        IPage<OutBoundSoPO> outBoundPage  =this.baseMapper.selectPage(page,queryWrapper);
        List<OutBoundSoDTO> resultList = BeanUtil.copyToList(outBoundPage.getRecords(),OutBoundSoDTO.class);

        PageSumResponse<OutBoundSoDTO>  pageSumResponse= PageSumResponse.toResult(
                pageReqDTO.getIndex(),
                pageReqDTO.getSize(),
                (int) page.getTotal(),
                resultList);

        //
        QueryWrapper<OutBoundSoPO> sumQueryWrapper=new QueryWrapper();
        sumQueryWrapper.select("SUM(outbound_power) AS outboundPower");
        sumQueryWrapper.like(StringUtils.isNotBlank(outBoundSoReqDTO.getPi()),"pi",outBoundSoReqDTO.getPi())
                .eq(StringUtils.isNotBlank(outBoundSoReqDTO.getOperationsPerson()),"operations_number",outBoundSoReqDTO.getOperationsPerson())
                .like(StringUtils.isNotBlank(outBoundSoReqDTO.getCustomerName()),"customer_name",outBoundSoReqDTO.getCustomerName())
                .eq(StringUtils.isNotBlank(outBoundSoReqDTO.getDepartment()),"department",outBoundSoReqDTO.getDepartment());

        if(StringUtils.isNotBlank(outBoundSoReqDTO.getBeginTime())){
            sumQueryWrapper.ge("outbound_time",outBoundSoReqDTO.getBeginTime().substring(0,10));
        }
        if(StringUtils.isNotBlank(outBoundSoReqDTO.getEndTime())){
            sumQueryWrapper.le("outbound_time",outBoundSoReqDTO.getEndTime().substring(0,10));
        }
        OutBoundSoPO outBoundSoPO=this.baseMapper.selectOne(sumQueryWrapper);
        Map<String, BigDecimal> map=new HashMap<>();
        map.put("sumPower",outBoundSoPO==null?BigDecimal.ZERO:new BigDecimal(outBoundSoPO.getOutboundPower()).setScale(0));
        pageSumResponse.setSumInfoMap(map);

        return pageSumResponse;
    }

}
