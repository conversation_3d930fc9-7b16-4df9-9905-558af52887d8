server:
  tomcat:
    max-http-form-post-size: -1
spring:
  cloud:
    inetutils:
      preferred-networks:
        - 10\.180\..+
        - 10\.171\..+
#    encoding:
#      charset: UTF-8
#      force: true
#      enabled: true

  main:
#    web-application-type: reactive
    allow-bean-definition-overriding: true
    allow-circular-references: true
#  profiles:
#    active: ${ACTIVE_PROFILE:local}
  mvc:
    hiddenmethod:
      filter:
        enabled: true

  servlet:
    multipart:
      enabled: true #是否启用http上传处理
      max-request-size: 100MB #最大请求文件的大小
      max-file-size: 20MB #设置单个文件最大长度
      file-size-threshold: 20MB #当文件达到多少时进行磁盘写入