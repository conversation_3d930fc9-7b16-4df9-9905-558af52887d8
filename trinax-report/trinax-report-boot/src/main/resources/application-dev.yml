spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${DATASOURCE_URL}
    username: ${DATASOURCE_USERNAME}
    password: ${DATASOURCE_PASSWORD}
    hikari:
      minimum-idle: 10
      maximum-pool-size: 20
      idle-timeout: 10000
      max-lifetime: 1800000
      connection-timeout: 30000
  redis:
    # redis数据库索引（默认为0），我们使用索引为3的数据库，避免和其他数据库冲突
    database: 0
    # redis服务器地址（默认为localhost）
    host: ${REDIS_HOST}
    # redis端口（默认为6379）
    port: ${REDIS_PORT}
    # redis访问密码（默认为空）
    password: ${REDIS_PASSWORD}
    # redis连接超时时间（单位为毫秒）
    timeout: 30000

#日志上报环境地址
logging:
  collector:
    address: ${LOGGING_COLLECTOR_ADDRESS}

integration:
  log:
    on-off: true

xxl:
  job:
    adminAddress: ${XXLJOB_ADMINADDRESS}
    executorName: trinax-report-service
    ip:
    port: 12000
    accessToken:
    logPath: /apps/logs/${spring.application.name}/xxl-job
    logRetentionDays: -1

rocketmq:
  name-server: ${ROCKETMQ_HOST}
  producer:
    group: report
  consumer:
    group:
    topic:

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志