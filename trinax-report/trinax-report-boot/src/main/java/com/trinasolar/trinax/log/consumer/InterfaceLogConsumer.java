package com.trinasolar.trinax.log.consumer;

import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.log.constants.LogTopic;
import com.trinasolar.trinax.log.dto.mq.InterfaceLogMqDTO;
import com.trinasolar.trinax.log.service.InterfaceLogRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(topic = LogTopic.INTERFACE_LOG_TOPIC, consumerGroup = "Report_" + LogTopic.INTERFACE_LOG_TOPIC)
public class InterfaceLogConsumer implements RocketMQListener<InterfaceLogMqDTO> {

    private final InterfaceLogRecordService interfaceLogRecordService;

    @Override
    public void onMessage(InterfaceLogMqDTO interfaceLogMqDTO) {

        log.info("interfaceLogMqDTO request: {}", JacksonUtil.bean2Json(interfaceLogMqDTO));
        interfaceLogRecordService.saveLog(interfaceLogMqDTO);
    }

}
