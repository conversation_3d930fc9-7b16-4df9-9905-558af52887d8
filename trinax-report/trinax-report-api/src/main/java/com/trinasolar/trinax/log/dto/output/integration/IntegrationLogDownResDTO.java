package com.trinasolar.trinax.log.dto.output.integration;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class IntegrationLogDownResDTO {

    @ExcelProperty("业务类型")
    private String bizType;

    @ExcelProperty("业务操作")
    private String bizOperation;

    @ExcelProperty("业务单号")
    private String bizNo;

    @ExcelProperty("集成系统")
    private String integrationSystem;

    @ExcelProperty("集成方向")
    private String integrationForward;

    @ExcelProperty("响应状态")
    private String responseStatus;

    @ExcelProperty("请求时间")
    private LocalDateTime requestTime;

    @ExcelProperty("响应时间")
    private LocalDateTime responseTime;

    @ExcelProperty("请求数据")
    private String requestData;

    @ExcelProperty("响应数据")
    private String responseData;

}
