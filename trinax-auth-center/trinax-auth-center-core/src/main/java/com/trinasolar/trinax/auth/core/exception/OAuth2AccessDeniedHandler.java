package com.trinasolar.trinax.auth.core.exception;

import com.trinasolar.trinax.auth.core.utils.ResultBody;
import com.trinasolar.trinax.auth.core.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * custom definition AccessDenied handler
 *
 * <AUTHOR>
 */
@Slf4j
public class OAuth2AccessDeniedHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException exception) {
        ResultBody resultBody = AuthExceptionResolver.resolveException(exception, request.getRequestURI());
        if(!ObjectUtils.isEmpty(resultBody.getCode())){
            response.setStatus(Integer.parseInt(resultBody.getCode()));
        }

        WebUtils.writeJson(response, resultBody);
    }
}
