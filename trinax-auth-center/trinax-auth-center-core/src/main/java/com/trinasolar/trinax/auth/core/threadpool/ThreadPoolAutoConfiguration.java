package com.trinasolar.trinax.auth.core.threadpool;

import com.alibaba.ttl.threadpool.TtlExecutors;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Description thread pool monitor, extends from ThreadPoolExecutor
 * <AUTHOR>
 * @Date 12/10/2023 16:50
 */
@Configuration
@EnableConfigurationProperties(ThreadPoolProperties.class)
public class ThreadPoolAutoConfiguration {

    /**
     * the executor service is used to execute business task
     *
     * @return ExecutorService
     */
    @Bean
    public ExecutorService executorService(ThreadPoolProperties threadPoolProperties) {
        UserThreadPoolExecutor threadPoolMonitorExecutor = new UserThreadPoolExecutor.Builder()
                .corePoolSize(threadPoolProperties.getCorePoolSize())
                .maximumPoolSize(threadPoolProperties.getMaximumPoolSize())
                .keepAliveTime(threadPoolProperties.getKeepAliveTime())
                .unit(TimeUnit.SECONDS)
                .taskQueueCapacity(threadPoolProperties.getBlockingQueueCapacity())
                .handler(new ThreadPoolExecutor.CallerRunsPolicy())
                .poolName("trina-auth-center-task")
                .isMonitor(true)
                .build();

        return TtlExecutors.getTtlExecutorService(threadPoolMonitorExecutor);
    }
}
