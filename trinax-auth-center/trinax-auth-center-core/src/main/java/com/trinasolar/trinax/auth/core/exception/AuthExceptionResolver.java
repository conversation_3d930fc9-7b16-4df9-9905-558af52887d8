package com.trinasolar.trinax.auth.core.exception;

import com.trinasolar.trinax.auth.core.utils.ResultBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

import java.util.Optional;

/**
 * <pre>
 * Title: auth异常解析类
 *
 * Description:
 * <pre>
 * <AUTHOR>
 **/
@Slf4j
public class AuthExceptionResolver {


    /**
     * 静态解析异常。可以直接调用
     *
     * @param ex
     * @return
     */
    public static ResultBody resolveException(Exception ex, String path) {

        log.error("登录统一认证异常转换:{}",ex);

        Integer code = HttpStatus.INTERNAL_SERVER_ERROR.value();
        String message = ex.getMessage();

        String className = ex.getClass().getName();

        log.error("Exception ClassName: [ {} ], message: [ {} ], requestPath: [ {} ]",className,message,path);

        if (className.contains("UsernameNotFoundException")) {

            code = HttpStatus.UNAUTHORIZED.value();

        } else if (className.contains("UnauthorizedUserException")) {

            code = HttpStatus.UNAUTHORIZED.value();
            message = "用户未被授权";

        } else if (className.contains("ClientRegistrationException")) {

            code = HttpStatus.UNAUTHORIZED.value();

        } else if (className.contains("BadCredentialsException")) {

            code = HttpStatus.UNAUTHORIZED.value();
            message = "错误的客户端凭据";

        } else if (className.contains("AccountExpiredException")) {

            code = HttpStatus.UNAUTHORIZED.value();
            message = "账号已过期";

        } else if (className.contains("LockedException")) {

            code = HttpStatus.UNAUTHORIZED.value();
            message = "账号已被冻结";

        } else if (className.contains("DisabledException")) {

            code = HttpStatus.UNAUTHORIZED.value();
            message = "账号被禁用";

        } else if (className.contains("CredentialsExpiredException")) {

            code = HttpStatus.UNAUTHORIZED.value();
            message = "用户登录凭据已过期";

        } else if (className.contains("InvalidClientException")) {

            code = HttpStatus.UNAUTHORIZED.value();
            message = "不合法的客户端";

        } else if (className.contains("UnauthorizedClientException")) {

            code = HttpStatus.UNAUTHORIZED.value();
            message = "未授权的客户端";

        } else if (className.contains("InsufficientAuthenticationException") || className.contains("AuthenticationCredentialsNotFoundException")) {

            code = HttpStatus.UNAUTHORIZED.value();
            message = "无效的认证信息";

        } else if (className.contains("InvalidGrantException")) {
            String error = Optional.ofNullable(ex.getMessage()).orElse("");

            if (error.contains("User is disabled")) {

                code = HttpStatus.UNAUTHORIZED.value();
                message = "用户被禁用";
            }else if (error.contains("Bad credentials")){

                code = HttpStatus.UNAUTHORIZED.value();
                message = "用户名或密码错误";
            }else if (error.contains("User account is locked")){

                code = HttpStatus.UNAUTHORIZED.value();
                message = "用户被冻结";
            }

        } else if (className.contains("InvalidScopeException")) {

            code = HttpStatus.UNAUTHORIZED.value();
            message = "无效的作用域";

        } else if (className.contains("InvalidTokenException")) {

            code = HttpStatus.UNAUTHORIZED.value();
            message = "无效的token";

        } else if (className.contains("InvalidRequestException")) {

            code = HttpStatus.BAD_REQUEST.value();
            message = "无效的请求";

        } else if (className.contains("UnsupportedGrantTypeException")) {

            code = HttpStatus.UNAUTHORIZED.value();
            message = "授权类型不支持";

        } else if (className.contains("UserDeniedAuthorizationException")) {

            code = HttpStatus.UNAUTHORIZED.value();
            message = "用户拒绝授权";

        } else if (className.contains("AccessDeniedException")) {

            code = HttpStatus.FORBIDDEN.value();
            message = "访问被拒绝";

        } else if (className.contains("HttpMessageNotReadableException")
                || className.contains("TypeMismatchException")
                || className.contains("MissingServletRequestParameterException")) {

            code = HttpStatus.BAD_REQUEST.value();
            message = "客户端错误";

        } else if (className.contains("NoHandlerFoundException")) {

            code = HttpStatus.NOT_FOUND.value();
            message = "未找到资源";

        } else if (className.contains("HttpRequestMethodNotSupportedException")) {

            code = HttpStatus.METHOD_NOT_ALLOWED.value();
            message = "请求方式不支持";

        } else if (className.contains("HttpMediaTypeNotAcceptableException")) {

            code = HttpStatus.BAD_REQUEST.value();
            message = "不支持该媒体类型";

        } else if (className.contains("IllegalArgumentException")) {
            if (message.contains("org.springframework.http.HttpStatus.403")){

                code = HttpStatus.FORBIDDEN.value();
                message = "无权限访问";
            }else {

                code = HttpStatus.BAD_REQUEST.value();
                message = "参数不合法";
            }

        }else if (className.contains("HttpClientErrorException$Unauthorized")) {

            code = HttpStatus.UNAUTHORIZED.value();
            message = "用户认证失败";

        }
        else if (className.contains("SmsCodeExpiredException")) {

            code = HttpStatus.UNAUTHORIZED.value();
        }
        else if (className.contains("OAuth2Exception")) {

            if (message.contains("invalid_client")){

                code = HttpStatus.UNAUTHORIZED.value();
                message = "不合法的的授权客户端";
            }else{

                code = HttpStatus.UNAUTHORIZED.value();
                message = "用户认证失败";
            }
        }else {

            if(className.contains("ResponseStatusException")){
                ResponseStatusException se = (ResponseStatusException) ex;
                code = se.getStatus().value();
                message = se.getMessage();
            }
        }
        return buildBody(message, path, code);
    }

    /**
     * 构建返回结果对象
     *
     * @param message
     * @return
     */
    private static ResultBody buildBody(String message, String path, int code) {

        ResultBody resultBody = new ResultBody(false, message, path, String.valueOf(code));

        return resultBody;
    }

}
