package com.trinasolar.trinax.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <pre>
 * Title: 资源DTO
 *
 * Description:
 * <pre>
 * <AUTHOR>
 **/
@Data
public class ResourcesResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 资源前台展示名称
     */
    private String resourceDisplayName;

    /**
     * 资源名称（不可重复）
     */
    private String resourceName;

    /**
     * 资源类型 PAGE,BUTTON
     */
    private String resourceType;

    /**
     * 资源标识
     */
    private String resourceIdentifier;

    /**
     * 链接地址
     */
    private String url;

    /**
     * 上级资源编号,顶层为0
     */
    private String parentResourceId;

    /**
     * 图标
     * */
    private String icon;

    /** 访问api列表 */
    private List<String> apis;

    /** 请求方式: GET POST PUT DELETE PATCH */
    @Schema(description = "请求方式: GET POST PUT DELETE PATCH")
    private String method;

}
