# Tomcat
server:
  port: 7020
#日志组件
trinasolar:
  team: dtt-team
  project:
    name: trinax-auth-service

springdoc:
  api-docs:
    enabled: true
    path: v3/api-docs
knife4j:
  enable: true


# Spring
spring:
  application:
    # 应用名称
    name: trinax-auth-service
    version: 1.0
  profiles:
    # 环境配置
    active: ${ACTIVE_PROFILE:local}
  main:
    allow-bean-definition-overriding: true
  cloud:
    inetutils:
      preferred-networks:
        - 10\.180\..+
        - 10\.171\..+
    nacos:
#      username: trinasolarx
#      password: ${NACOS_PASSWORD}
      discovery:
        # 使用德勤分配的 namespace 代替，如没有则注释掉这一样
        namespace: trinax
        # 服务注册地址
        server-addr: localhost:8848
#        server-addr: mynacos:8848
      config:
       # 使用德勤分配的 namespace 代替，如没有则注释掉这一样
        namespace: trinax
         # 使用德勤分配的 group 代替，如没有则注释掉这一样
#        group: trinax-public
        # 配置中心地址
        server-addr: localhost:8848
#        server-addr: mynacos:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-dataids: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: mysentinel:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: localhost:8848
#            server-addr: mynacos:8848
            dataId: sentinel-basic-service
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: flow
    jackson:
      date-format: yyyy-MM-dd HH:mm:ss
      time-zone: GMT+8

management:
  health.defaults.enabled: false


  #SPLTIJVCNPCCCPFU
