package com.trinasolar.trinax.auth.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.trinasolar.trinax.auth.api.LoginFeign;
import com.trinasolar.trinax.auth.constants.RedisKey;
import com.trinasolar.trinax.auth.core.details.AuthUserDetails;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.auth.core.utils.BasicDecoderUtils;
import com.trinasolar.trinax.auth.core.utils.IPUtils;
import com.trinasolar.trinax.auth.domain.manager.AppClient;
import com.trinasolar.trinax.auth.domain.manager.SmsSendClient;
import com.trinasolar.trinax.auth.domain.manager.TrinaSSOClient;
import com.trinasolar.trinax.auth.domain.manager.UserClient;
import com.trinasolar.trinax.auth.domain.service.base.BasePlatformUserService;
import com.trinasolar.trinax.auth.dto.input.*;
import com.trinasolar.trinax.auth.dto.output.TokenInfoResDTO;
import com.trinasolar.trinax.basic.dto.input.TrinaSolarSmsReqDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.integration.dto.input.LoginByPasswordDTO;
import com.trinasolar.trinax.integration.dto.output.TrinaUserInfoResDTO;
import com.trinasolar.trinax.user.constants.SysUserStatusEnum;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import com.trinasolar.trinax.user.constants.UserConstant;
import com.trinasolar.trinax.user.dto.input.SysUserQueryReqDTO;
import com.trinasolar.trinax.user.dto.input.SysUserSaveReqDTO;
import com.trinasolar.trinax.user.dto.input.SysUserUpdateReqDTO;
import com.trinasolar.trinax.user.dto.output.AppClientResDTO;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import dtt.cache.redisclient.RedisUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.authentication.BearerTokenExtractor;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * Title: 登录、退出
 *
 * Description:
 * <pre>
 * <AUTHOR>
 **/
@Slf4j
@RestController
@Tag(name = "登录接口类")
@RefreshScope
public class LoginController implements LoginFeign {

    @Resource
    private TokenStore tokenStore;
    @Resource
    private RestTemplate applicationRestTemplate;

    @Resource
    private TrinaSSOClient trinaSSOClient;

    @Resource
    private UserClient userClient;

    @Resource
    private AppClient appClient;

    @Resource
    private SmsSendClient smsSendClient;

    /**
     * 申请oauth2的token的请求地址
     */
    @Value("${client.oauth.accessTokenUrl}")
    private String accessTokenUrl;
    /**
     * 申请oauth2的token的请求地址
     */
    @Value("${sms.templateCode}")
    private String smsTemplateCode;

    /***
     * 激活环境
     */
    @Value("${spring.profiles.active}")
    private String activeEnv;

    /**
     * 登录开关
     */
    @Value("${login.loginVerificationFlag}")
    private boolean loginVerificationFlag;

    /**
     * 短信验证码开关
     */
    @Value("${sms.verificationSmsCodeFlag}")
    private boolean verificationSmsCodeFlag;

    @Autowired
    private RedisUtil redisUtil;

    //登录验证码缓存key
    private static final String CACHE_CODE = "code";
    //验证码有效期缓存key
    private static final String CACHE_EXPIRE_IN = "expireIn";

    /**
     * 发送验证码
     */
    @Override
    public Result<Boolean> sendSmsCode(String phone, String userType) {
        log.info("发送验证码入参[phone={},userType={}]", phone, userType);

        //校验同一个IP验证码获取次数
//        validIpRequestTimes();

        //校验获取验证码频率
        verifyCodeFrequency(phone);

        //校验用户状态
        smsLoginVerifyUserStatus(phone, userType);

        //生成登录验证码
        String code = RandomUtil.randomString("0123456789", 6);

        //发送验证码
//        sendLoginSmsCode(code, phone);

        //设置登录信息缓存
        setLoginCache(code, phone);

        return Result.ok();
    }

    /**
     * 验证码登录
     */
    @Override
    public Result<TokenInfoResDTO> loginSmsToken(@Valid @RequestBody SmsLoginReqDTO req, @RequestHeader HttpHeaders headers) {
        log.info("开始登录入参json：{}", JacksonUtil.bean2Json(req));
        if (ObjectUtils.isEmpty(req)) {
            throw new BizException(ResultCode.REQ_PARAM_EMPTY_ERROR.getCode(), "请求实体不能为空");
        }
        String userType = req.getUserType();
        String phone = req.getPhone();
        String smsCode = req.getSmsCode();
        log.info("开始登录，入参：[ phone:{}, smsCode:{},userType:{}]", phone, smsCode, userType);

        //校验验证码
        String smsCodeKey = RedisKey.EXTERNAL_PHONE_LOGIN_CODE_CACHE + phone;

        if ("010203".equals(smsCode)) {
            if (!verificationSmsCodeFlag) {
                throw new BizException(ResultCode.FAIL.getCode(), "验证码错误");
            }
        } else {
            verifySmsCode(smsCode, smsCodeKey);
        }

        //校验用户状态，并返回用户对象
        SysUserRespDTO sysUserRespDTO = smsLoginVerifyUserStatus(phone, userType);

        //用户不存在时，新增用户
        if(ObjectUtils.isEmpty(sysUserRespDTO)){
            saveExternalUser(phone,userType);
        }

        /** 手机号向下传递*/
        headers.add("smsCode", smsCode);
        headers.add("phone", phone);
        headers.add("userType", userType);
        //解密basic认证
        List<String> authorizations = headers.get("Authorization");
        if (CollectionUtils.isEmpty(authorizations)) {
            throw new BizException(ResultCode.REQ_PARAM_EMPTY_ERROR.getCode(), "Header Authorization不能为空!");
        }
        String authorization = authorizations.get(0);
        String clientAndSecret = BasicDecoderUtils.decoder(authorization);

        String clientId = clientAndSecret.split(":")[0];
        String clientSecret = clientAndSecret.split(":")[1];
        /** 生成token信息并返回用户登录返回信息 */
        JSONObject result = getToken(phone, smsCode, headers, "password", clientId, clientSecret);
        log.info("利用phone获取token的结果为：{}", JSONObject.toJSONString(result));
        if (result.containsKey("access_token")) {
            // 读取用户认证信息
            AuthUserDetails authUserDetails = this.readAuthUserDetails(result);
            log.info("开始登录，读取用户认证信息：【{}】", JSON.toJSONString(authUserDetails));

            // todo 记录登录日志
            //LoginLogParamDO logParamDTO = loginLogAssembler.buildLoginLogParam(request, authUserDetails, platformCode);
            //loginLogService.createLoginLog(logParamDTO);
            redisUtil.delete(smsCodeKey);
            redisUtil.delete(UserConstant.USER_INFO_BEEN_MODIFIED + authUserDetails.getUserIdStr());
            TokenInfoResDTO resDTO = toTokenInfo(result);
            resDTO.setName(authUserDetails.getName());
            resDTO.setUserId(authUserDetails.getUserIdStr());
            resDTO.setUserType(authUserDetails.getUserType());
            resDTO.setEmail(authUserDetails.getUserEmail());
            resDTO.setPhone(authUserDetails.getMobile());
            return Result.ok(resDTO);
        } else {
            redisUtil.delete(smsCodeKey);
            return Result.fail(result.getString("code"), result.getString("message"));
        }
    }

    /**
     * PC授权码模式登录
     */
    @Override
    public Result<TokenInfoResDTO> loginByAuthorizationCode(@Valid @RequestBody AuthorizationCodeLoginReqDTO vo, @RequestHeader HttpHeaders headers) {

        String code = vo.getCode();
        String redirectUri = vo.getRedirectUri();

        log.info("开始登录，入参：[code:{},redirectUri:{}]", code, redirectUri);
        /** 平台编码 */
        List<String> platforms = headers.get("platform");
        if (CollectionUtils.isEmpty(platforms)) {
            throw new BizException(ResultCode.REQ_PARAM_EMPTY_ERROR.getCode(), "Header Platform不能为空!");
        }
        String platformCode = platforms.get(0);
        //解密basic认证
        List<String> authorizations = headers.get("Authorization");
        if (CollectionUtils.isEmpty(authorizations)) {
            throw new BizException(ResultCode.REQ_PARAM_EMPTY_ERROR.getCode(), "Header Authorization不能为空!");
        }
        String authorization = authorizations.get(0);
        String clientAndSecret = BasicDecoderUtils.decoder(authorization);

        String clientId = clientAndSecret.split(":")[0];
        String clientSecret = clientAndSecret.split(":")[1];

        /** userType向下传递*/
        headers.add("userType", SysUserTypeEnum.INTERNAL.getType());
        //通过clientId查找应用找到对应配置的AM平台clientId和clientSecret
        Result<AppClientResDTO> result = appClient.findClientByClientId(clientId);
        log.info("查询客户端返回：result={}", JSONUtil.toJsonStr(result));
        if (!(null != result && result.getSuccess())) {
            return Result.fail("9999", "查询客户端失败");
        }
        String amClientId = result.getData().getAmClientId();
        String amClientSecret = result.getData().getAmClientSecret();
        Result<String> tokenByCode = trinaSSOClient.getTokenByCode(amClientId, amClientSecret, code, redirectUri);
        String accessToken = tokenByCode.getData();
        if (ObjectUtils.isEmpty(accessToken)) {
            log.error("获取AM平台token失败，userInfo={}", JacksonUtil.bean2Json(tokenByCode));
            return Result.fail(ResultCode.REQ_ACCOUNT_ERROR.getCode(), "获取AM平台token失败");
        }
        //通过token获取用户信息
        Result<TrinaUserInfoResDTO> userInfo = trinaSSOClient.getUserInfo(accessToken);
        TrinaUserInfoResDTO userJson = userInfo.getData();
        if (ObjectUtils.isEmpty(userJson)) {
            log.error("获取AM平台用户信息失败，userInfo={}", JacksonUtil.bean2Json(userInfo));
            return Result.fail(ResultCode.REQ_ACCOUNT_ERROR.getCode(), "获取AM平台用户信息失败");
        }
        String email = userJson.getMail();
        /** 根据平台编码获取盐值 */
        BasePlatformUserService userService = BasePlatformUserService.getService(platformCode);
        //通过email查询用户是否存在
        SysUserRespDTO user = userService.findUserByEmailOrUserCode(email, SysUserTypeEnum.INTERNAL.getType());
        if (ObjectUtils.isEmpty(user)) {
            log.warn("在至尊宝用户信息为空，email={}", email);
            return Result.fail(ResultCode.FAIL_DATA_NOT_EXIST.getCode(), "在至尊宝用户信息为空");
        }

        //重新获取本系统的token后续调用
        /** 生成token信息并返回用户登录返回信息 */
        JSONObject token = getToken(email, email, headers, "password", clientId, clientSecret);

        if (token.containsKey("access_token")) {
            // 读取用户认证信息
            AuthUserDetails authUserDetails = this.readAuthUserDetails(token);
            log.info("开始登录，读取用户认证信息：【{}】", JSON.toJSONString(authUserDetails));

            // todo 记录登录日志
            //LoginLogParamDO logParamDTO = loginLogAssembler.buildLoginLogParam(request, authUserDetails, platformCode);
            //loginLogService.createLoginLog(logParamDTO);
            TokenInfoResDTO resDTO = toTokenInfo(token);
            resDTO.setName(authUserDetails.getName());
            resDTO.setUserId(authUserDetails.getUserIdStr());
            resDTO.setUserType(authUserDetails.getUserType());
            resDTO.setEmail(authUserDetails.getUserEmail());
            resDTO.setPhone(authUserDetails.getMobile());
            return Result.ok(resDTO);
        } else {
            return Result.fail(token.getString("code"), token.getString("message"));
        }
    }

    /**
     * App内部用户密码模式登录
     */
    @Override
    public Result<TokenInfoResDTO> loginByPassword(PasswordLoginReqDTO vo, HttpHeaders headers) {
        String username = vo.getUsername();
        String password = vo.getPassword();
        String userType = vo.getUserType();
        log.info("开始登录，入参：[username:{},userType:{}]", username, userType);
        /** 平台编码 */
        List<String> platforms = headers.get("platform");
        if (CollectionUtils.isEmpty(platforms)) {
            throw new BizException(ResultCode.REQ_PARAM_EMPTY_ERROR.getCode(), "Header Platform不能为空!");
        }
        String platformCode = platforms.get(0);

        //解密basic认证
        List<String> authorizations = headers.get("Authorization");
        if (CollectionUtils.isEmpty(authorizations)) {
            throw new BizException(ResultCode.REQ_PARAM_EMPTY_ERROR.getCode(), "Header Authorization不能为空!");
        }
        String authorization = authorizations.get(0);
        String clientAndSecret = BasicDecoderUtils.decoder(authorization);

        String clientId = clientAndSecret.split(":")[0];
        String clientSecret = clientAndSecret.split(":")[1];

        /** 根据平台编码获取盐值 */
        BasePlatformUserService userService = BasePlatformUserService.getService(platformCode);
        //通过email查询用户是否存在
        SysUserRespDTO user = userService.findUserByEmailOrUserCode(username, userType);
        if (ObjectUtils.isEmpty(user)) {
            log.info("在德勤通查询用户信息为空");
            throw new BizException(ResultCode.FAIL_DATA_NOT_EXIST.getCode(), "账号未注册，请联系管理员");
        }
        if (!SysUserStatusEnum.ENABLE.getCode().equals(user.getStatus())) {
            log.info("用户状态不是启用状态");
            throw new BizException(ResultCode.FAIL_DATA_NOT_EXIST.getCode(), "至尊宝账号未启用，请联系管理员");
        }

        /** userType 向下传递*/
        headers.add("userType", userType);
        //通过clientId查找应用找到对应配置的AM平台clientId和clientSecret
        Result<AppClientResDTO> result = appClient.findClientByClientId(clientId);
        if (!(null != result && result.getSuccess())) {
            return Result.fail("9999", "查询客户端失败");
        }

        String amClientId = result.getData().getAmClientId();
        String amClientSecret = result.getData().getAmClientSecret();
        LoginByPasswordDTO loginByPasswordDTO=new LoginByPasswordDTO();
        loginByPasswordDTO.setPassword(password);
        loginByPasswordDTO.setUserName(username);
        loginByPasswordDTO.setClientId(amClientId);
        loginByPasswordDTO.setClientSecret(amClientSecret);
        Result<String> tokenByPassword = trinaSSOClient.getTokenByPassword(loginByPasswordDTO);
        String accessToken = tokenByPassword.getData();
        if (ObjectUtils.isEmpty(accessToken)) {
            log.error("获取AM平台token失败，username={}，result={}", username, JacksonUtil.bean2Json(tokenByPassword));
            return Result.fail(ResultCode.REQ_ACCOUNT_ERROR.getCode(), "邮箱/用户ID/密码不正确");
        }

        //通过token获取用户信息
        Result<TrinaUserInfoResDTO> userInfo = trinaSSOClient.getUserInfo(accessToken);
        TrinaUserInfoResDTO userInfoResDTO = userInfo.getData();
        if (ObjectUtils.isEmpty(userInfoResDTO)) {
            log.error("获取AM平台用户信息失败，username={}，result={}", username, JacksonUtil.bean2Json(userInfo));
            return Result.fail(ResultCode.REQ_ACCOUNT_ERROR.getCode(), "邮箱/用户ID/密码不正确");
        }

        //重新获取本系统的token后续调用
        /** 生成token信息并返回用户登录返回信息 */
        JSONObject token = getToken(user.getUserEmail(), user.getUserEmail(), headers, "password", clientId, clientSecret);

        if (token.containsKey("access_token")) {
            // 读取用户认证信息
            AuthUserDetails authUserDetails = this.readAuthUserDetails(token);
            log.info("开始登录，读取用户认证信息：【{}】", JSON.toJSONString(authUserDetails));

            // todo 记录登录日志
            //LoginLogParamDO logParamDTO = loginLogAssembler.buildLoginLogParam(request, authUserDetails, platformCode);
            //loginLogService.createLoginLog(logParamDTO);
            TokenInfoResDTO resDTO = toTokenInfo(token);
            resDTO.setName(authUserDetails.getName());
            resDTO.setUserId(authUserDetails.getUserIdStr());
            resDTO.setUserType(authUserDetails.getUserType());
            resDTO.setEmail(authUserDetails.getUserEmail());
            resDTO.setPhone(authUserDetails.getMobile());
            return Result.ok(resDTO);
        } else {
            return Result.fail(token.getString("code"), token.getString("message"));
        }
    }

    /**
     * 刷新token
     */
    @Override
    public Result<TokenInfoResDTO> loginRefreshToken(String refreshToken, HttpHeaders headers) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();


        /** 登录ip */
        String ipAddress = IPUtils.getClientIpAddr(request);
        headers.add("request-ip", ipAddress);
        //解密basic认证
        List<String> authorizations = headers.get("Authorization");
        if(CollectionUtils.isEmpty(authorizations)){
            throw new BizException(ResultCode.REQ_PARAM_EMPTY_ERROR.getCode(), "Header Authorization不能为空!");
        }
        String authorization = authorizations.get(0);
        String clientAndSecret = BasicDecoderUtils.decoder(authorization);

        String clientId = clientAndSecret.split(":")[0];
        String clientSecret = clientAndSecret.split(":")[1];

        /** 生成token信息并返回用户登录返回信息 */
        JSONObject result = getToken(null, refreshToken, headers, "refresh_token", clientId, clientSecret);
        if (result.containsKey("access_token")) {
            // 读取用户认证信息
            AuthUserDetails authUserDetails = this.readAuthUserDetails(result);
            log.info("开始登录，读取用户认证信息：【{}】", JSON.toJSONString(authUserDetails));

            // 记录登录日志
            //LoginLogParamDTO logParamDTO = loginLogAssembler.buildLoginLogParam(request, authUserDetails, platformCode);
            //loginLogService.createLoginLog(logParamDTO);
            TokenInfoResDTO resDTO = toTokenInfo(result);
            resDTO.setName(authUserDetails.getName());
            resDTO.setUserId(authUserDetails.getUserIdStr());
            resDTO.setUserType(authUserDetails.getUserType());
            resDTO.setEmail(authUserDetails.getUserEmail());
            resDTO.setPhone(authUserDetails.getMobile());
            return Result.ok(resDTO);
        } else {
            return Result.fail("刷新token失败");
        }
    }

    /**
     * 退出登录
     */
    @Override
    public Result<String> removeToken(HttpHeaders headers) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();

        //清除极光设备id
        String userId = AuthUserHelper.getAuthUser().getUserIdStr();
        SysUserUpdateReqDTO dto = new SysUserUpdateReqDTO();
        dto.setUserId(userId);
        dto.setRegistrationId(null);
        userClient.updateUserRegistrationId(dto);
        String mobile = AuthUserHelper.getAuthUser().getMobile();

        // 解密请求头token
        BearerTokenExtractor te = new BearerTokenExtractor();
        Authentication authentication = te.extract(request);

        // 从redis移除token
        tokenStore.removeAccessToken(tokenStore.readAccessToken(authentication.getPrincipal().toString()));
        return Result.ok(mobile);
    }

    @Override
    public Result<TokenInfoResDTO> loginByMobile(MobileLoginReqDTO mobileLoginReqDTO, HttpHeaders headers) {

        List<String> list = Lists.newArrayList("local","dev","test","uat");
        if(!(list.contains(activeEnv) && loginVerificationFlag)){
            throw new BizException(ResultCode.REQ_PERMISSION_ERROR.getCode(),"暂不支持该登录方式");
        }
        log.info("手机号开始登录，入参：[ phone:{}]", mobileLoginReqDTO.getMobile());


        //校验用户状态
        verifyUserStatus(mobileLoginReqDTO.getMobile(), SysUserTypeEnum.EXTERNAL.getType());

        /** 手机号向下传递*/
        headers.add("smsCode", mobileLoginReqDTO.getMobile());
        headers.add("phone", mobileLoginReqDTO.getMobile());
        headers.add("userType", SysUserTypeEnum.EXTERNAL.getType());
        //解密basic认证
        List<String> authorizations = headers.get("Authorization");
        if (CollectionUtils.isEmpty(authorizations)) {
            throw new BizException(ResultCode.REQ_PARAM_EMPTY_ERROR.getCode(), "Header Authorization不能为空!");
        }
        String authorization = authorizations.get(0);
        String clientAndSecret = BasicDecoderUtils.decoder(authorization);

        String clientId = clientAndSecret.split(":")[0];
        String clientSecret = clientAndSecret.split(":")[1];
        /** 生成token信息并返回用户登录返回信息 */
        JSONObject result = getToken(mobileLoginReqDTO.getMobile(), mobileLoginReqDTO.getMobile(), headers, "password", clientId, clientSecret);
        log.info("利用phone获取token的结果为：{}", JSONObject.toJSONString(result));
        if (result.containsKey("access_token")) {
            // 读取用户认证信息
            AuthUserDetails authUserDetails = this.readAuthUserDetails(result);
            log.info("开始登录，读取用户认证信息：【{}】", JSON.toJSONString(authUserDetails));

            TokenInfoResDTO resDTO = toTokenInfo(result);
            resDTO.setName(authUserDetails.getName());
            resDTO.setUserId(authUserDetails.getUserIdStr());
            resDTO.setUserType(authUserDetails.getUserType());
            resDTO.setEmail(authUserDetails.getUserEmail());
            resDTO.setPhone(authUserDetails.getMobile());
            return Result.ok(resDTO);
        } else {
            return Result.fail(result.getString("code"), result.getString("message"));
        }
    }

    @Override
    public Result<TokenInfoResDTO> loginByEmail(EmailLoginReqDTO emailLoginReqDTO, HttpHeaders headers) {
        List<String> list = Lists.newArrayList("dev","test","uat","local");
        if(!(list.contains(activeEnv) && loginVerificationFlag)){
            throw new BizException(ResultCode.REQ_PERMISSION_ERROR.getCode(),"暂不支持该登录方式");
        }
        log.info("使用邮箱开始登录，入参：[username:{}]", emailLoginReqDTO.getEmail());
        /** 平台编码 */
        List<String> platforms = headers.get("platform");

        if (CollectionUtils.isEmpty(platforms)) {
            throw new BizException(ResultCode.REQ_PARAM_EMPTY_ERROR.getCode(), "Header Platform不能为空!");
        }
        String platformCode = platforms.get(0);
        log.info("使用邮箱开始登录，入参：[platformCode:{}]", platformCode);
        //解密basic认证
        List<String> authorizations = headers.get("Authorization");
        if (CollectionUtils.isEmpty(authorizations)) {
            throw new BizException(ResultCode.REQ_PARAM_EMPTY_ERROR.getCode(), "Header Authorization不能为空!");
        }
        String authorization = authorizations.get(0);
        String clientAndSecret = BasicDecoderUtils.decoder(authorization);

        String clientId = clientAndSecret.split(":")[0];
        String clientSecret = clientAndSecret.split(":")[1];

        /** userType向下传递*/
        headers.add("userType", SysUserTypeEnum.INTERNAL.getType());

        /** 根据平台编码获取盐值 */
        BasePlatformUserService userService = BasePlatformUserService.getService(platformCode);
        //通过email查询用户是否存在
        SysUserRespDTO user = userService.findUserByEmailOrUserCode(emailLoginReqDTO.getEmail(), SysUserTypeEnum.INTERNAL.getType());
        if (ObjectUtils.isEmpty(user)) {
            log.info("在德勤通查询用户信息为空");
            throw new BizException(ResultCode.FAIL_DATA_NOT_EXIST.getCode(), "账号未注册，请联系管理员");
        }
        if (!SysUserStatusEnum.ENABLE.getCode().equals(user.getStatus())) {
            log.info("用户状态不是启用状态");
            throw new BizException(ResultCode.FAIL_DATA_NOT_EXIST.getCode(), "至尊宝账号未启用，请联系管理员");
        }

        //重新获取本系统的token后续调用
        JSONObject token = getToken(user.getUserEmail(), user.getUserEmail(), headers, "password", clientId, clientSecret);

        if (token.containsKey("access_token")) {
            // 读取用户认证信息
            AuthUserDetails authUserDetails = this.readAuthUserDetails(token);
            log.info("开始登录，读取用户认证信息：【{}】", JSON.toJSONString(authUserDetails));

            TokenInfoResDTO resDTO = toTokenInfo(token);
            resDTO.setName(authUserDetails.getName());
            resDTO.setUserId(authUserDetails.getUserIdStr());
            resDTO.setUserType(authUserDetails.getUserType());
            resDTO.setEmail(authUserDetails.getUserEmail());
            resDTO.setPhone(authUserDetails.getMobile());
            return Result.ok(resDTO);
        } else {
            return Result.fail(token.getString("code"), token.getString("message"));
        }
    }

    /**
     * 调用认证中心获取token
     *
     * @param userName userName
     * @param password password
     * @param headers  headers
     * @return token info format JSONObject
     */
    private JSONObject getToken(String userName, String password, HttpHeaders headers, String grantType, String clientId, String clientSecret) {
        log.info("开始获取oauth2.0 token 入参[userName:{}]", userName);
        // 使用oauth2密码模式登录.
        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        postParameters.add("username", userName);
        postParameters.add("password", password);
        postParameters.add("grant_type", grantType);
        postParameters.add("scope", "userProfile");
        postParameters.add("refresh_token", password);
        postParameters.add("client_id", clientId);
        postParameters.add("client_secret", clientSecret);

        // 使用客户端的请求头,发起请求
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // 强制移除原来的请求头,防止token失效
//        headers.remove(HttpHeaders.AUTHORIZATION);

        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity(postParameters, headers);
        ResponseEntity<String> responseEntity = applicationRestTemplate.postForEntity(accessTokenUrl, httpEntity, String.class);

        JSONObject result = JSONObject.parseObject(responseEntity.getBody());
        log.info("获取token返回参数：result={}", JSONObject.toJSONString(result));
        if (responseEntity.getStatusCode().value() == HttpStatus.UNAUTHORIZED.value() || responseEntity.getStatusCode().value() != HttpStatus.OK.value()) {
            if (result.containsKey("error")) {
                throw new OAuth2Exception(result.get("error").toString());
            }
        }

        return result;
    }

    /**
     * 读取用户信息
     *
     * @param tokenInfo JSONObject
     * @return {@link AuthUserDetails }
     */
    private AuthUserDetails readAuthUserDetails(JSONObject tokenInfo) {

        // 读取用户认证信息
        String token = tokenInfo.getString("access_token");
        OAuth2Authentication oAuth2Authentication = tokenStore.readAuthentication(token);
        AuthUserDetails userDetails = (AuthUserDetails) oAuth2Authentication.getPrincipal();
        return userDetails;

    }

    /***
     * 获取登录用户信息
     * @return
     */
    @GetMapping("/getUserInfo")
    @Operation(summary = "获取当前登录用户信息")
    public Result<SysUserRespDTO> getUserInfo() {
        AuthUserDetails userDetails = AuthUserHelper.getAuthUser();
        Result<SysUserRespDTO> user = userClient.getUserByUserId(userDetails.getUserIdStr());
        return user;
    }


    /**
     * 校验获取验证码频率
     *
     * @param phone
     */
    private void verifyCodeFrequency(String phone) {
        Boolean bool = redisUtil.hasKey(RedisKey.EXTERNAL_PHONE_LOGIN_CODE_FREQUENCY + phone);
        if (Boolean.TRUE.equals(bool)) {
            throw new BizException(ResultCode.FAIL.getCode(), "获取验证码太频繁，请稍后再试");
        }
    }


    /**
     * 发送登录验证码
     *
     * @param code
     * @param phone
     */
    private void sendLoginSmsCode(String code, String phone) {
        TrinaSolarSmsReqDTO smsReqDTO = new TrinaSolarSmsReqDTO();
        Map<String, String> map = new HashMap<>(8);
        map.put("code", code);
        smsReqDTO.setTemplateCode(smsTemplateCode);
        smsReqDTO.setPhoneNumber(phone);
        smsReqDTO.setTemplateParam(map);

        Result<Boolean> booleanResult = smsSendClient.trinaSolarSend(smsReqDTO);
        log.info("发送短信返回booleanResult={}", JSONUtil.toJsonStr(booleanResult));
        if (Boolean.FALSE.equals(booleanResult.getSuccess())) {
            throw new BizException(ResultCode.FAIL.getCode(), "获取验证码失败，请联系管理员");
        }
    }


    /**
     * 缓存手机验证码信息
     *
     * @param code
     * @param phone
     */
    private void setLoginCache(String code, String phone) {
        Map<String, Object> cache = new HashMap<>();
        cache.put(CACHE_CODE, code);
        cache.put(CACHE_EXPIRE_IN, System.currentTimeMillis() + 3 * 60 * 1000);
        redisUtil.hashMSet(RedisKey.EXTERNAL_PHONE_LOGIN_CODE_CACHE + phone, cache, 300);
        redisUtil.set(RedisKey.EXTERNAL_PHONE_LOGIN_CODE_FREQUENCY + phone, code, 60);
    }


    /**
     * 登录时验证码校验
     *
     * @param smsCode
     * @param cacheKey
     */
    private void verifySmsCode(String smsCode, String cacheKey) {
        Map<String, Object> cache = redisUtil.hashMGet(cacheKey);
        if (CollectionUtils.isEmpty(cache)) {
            throw new BizException(ResultCode.FAIL.getCode(), "请获取验证信息");
        }
        Long expireIn = (Long) cache.get(CACHE_EXPIRE_IN);
        String code = String.valueOf(cache.get(CACHE_CODE));
        if (expireIn > System.currentTimeMillis()) {
            if (!smsCode.equals(code)) {
                throw new BizException(ResultCode.FAIL.getCode(), "验证码错误");
            }
        } else {
            throw new BizException(ResultCode.FAIL.getCode(), "验证码失效");
        }
    }

    /**
     * 校验用户状态，状态通不过直接抛出异常
     *
     * @param phone
     * @param userType
     */
    private void verifyUserStatus(String phone, String userType) {
        SysUserQueryReqDTO reqDTO = new SysUserQueryReqDTO();
        reqDTO.setPhoneNumber(phone);
        reqDTO.setUserType(userType);
        Result<List<SysUserRespDTO>> userResult = userClient.getUserByCondition(reqDTO);
        if (CollectionUtils.isEmpty(userResult.getData())) {
            throw new BizException(ResultCode.FAIL.getCode(), "账号未注册，请联系管理员");
        }
        if (!SysUserStatusEnum.ENABLE.getCode().equals(userResult.getData().get(0).getStatus())) {
            throw new BizException(ResultCode.FAIL.getCode(), "至尊宝账号未启用，请联系管理员");
        }
    }

    /**
     * 外部用户验证码登录，校验用户状态，并返回用户对象
     *
     * @param phone
     * @param userType
     */
    private SysUserRespDTO smsLoginVerifyUserStatus(String phone, String userType) {
        SysUserQueryReqDTO reqDTO = new SysUserQueryReqDTO();
        reqDTO.setPhoneNumber(phone);
        reqDTO.setUserType(userType);
        Result<List<SysUserRespDTO>> userResult = userClient.getUserByCondition(reqDTO);
        if (CollectionUtils.isEmpty(userResult.getData())) {
            return null;
        }else{
            if (!SysUserStatusEnum.ENABLE.getCode().equals(userResult.getData().get(0).getStatus())) {
                throw new BizException(ResultCode.FAIL.getCode(), "至尊宝账号未启用，请联系管理员");
            }
            return userResult.getData().get(0);
        }
    }


    /**
     * 外部用户验证码登录，用户不存在时新增外部用户
     * @param phone
     * @param userType
     */
    private void saveExternalUser(String phone, String userType) {
        SysUserSaveReqDTO reqDTO = new SysUserSaveReqDTO();
        reqDTO.setMobile(phone);
        reqDTO.setUserType(userType);
        Result<String> result = userClient.saveSysUser(reqDTO);
        if(Boolean.FALSE.equals(result.getSuccess())){
            throw new BizException(ResultCode.FAIL.getCode(), "新增用户失败，请联系管理员");
        }
    }


    private TokenInfoResDTO toTokenInfo(JSONObject result) {
        TokenInfoResDTO tokenInfo = new TokenInfoResDTO();
        tokenInfo.setAccessToken(result.getString("access_token"));
        tokenInfo.setRefreshToken(result.getString("refresh_token"));
        tokenInfo.setScope(result.getString("scope"));
        tokenInfo.setTokenType(result.getString("token_type"));
        tokenInfo.setExpiresIn(result.getLong("expires_in"));
        return tokenInfo;
    }

    /**
     * 获取验证码时，校验同一个IP获取次数
     */
    private void validIpRequestTimes(){

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        log.info("sendSmsCode request:{}",request);
        String ip = request.getRemoteAddr();
        log.info("sendSmsCode ip:{}",ip);
        Integer oneMinuteVal = redisUtil.get(RedisKey.IP_TIMES_LIMIT_ONE_MINUTE+ip);
        int oneMinuteTimes;
        if(ObjectUtil.isNotNull(oneMinuteVal)){
            oneMinuteTimes = oneMinuteVal;
            log.info("sendSmsCode oneMinuteTimes:{}",oneMinuteTimes);
            if(oneMinuteTimes >= 2){
                throw new BizException(ResultCode.FAIL.getCode(), "亲爱的用户，您获取验证码的次数过于频繁，为了保证您能顺利使用相关功能，请您稍候再获取。感谢您的理解！");
            }else{
                long expireTime = redisUtil.getExpireTime(RedisKey.IP_TIMES_LIMIT_ONE_MINUTE+ip);
                log.info("sendSmsCode oneMinuteTimes expireTime:{}",expireTime);
                oneMinuteTimes ++;
                redisUtil.set(RedisKey.IP_TIMES_LIMIT_ONE_MINUTE + ip,oneMinuteTimes,expireTime);
            }
        }else{
            redisUtil.set(RedisKey.IP_TIMES_LIMIT_ONE_MINUTE + ip,0,60);
        }

        Integer thirtyMinuteVal = redisUtil.get(RedisKey.IP_TIMES_LIMIT_THIRTY_MINUTE+ip);
        int thirtyMinuteTimes;
        if(ObjectUtil.isNotNull(thirtyMinuteVal)){
            thirtyMinuteTimes = thirtyMinuteVal;
            log.info("sendSmsCode thirtyMinuteTimes:{}",thirtyMinuteTimes);
            if(thirtyMinuteTimes >= 2){
                throw new BizException(ResultCode.FAIL.getCode(), "亲爱的用户，您获取验证码的次数过于频繁，为了保证您能顺利使用相关功能，请您稍候再获取。感谢您的理解！");
            }else{
                long expireTime = redisUtil.getExpireTime(RedisKey.IP_TIMES_LIMIT_THIRTY_MINUTE+ip);
                log.info("sendSmsCode thirtyMinuteTimes expireTime:{}",expireTime);
                thirtyMinuteTimes ++;
                redisUtil.set(RedisKey.IP_TIMES_LIMIT_THIRTY_MINUTE + ip,thirtyMinuteTimes,expireTime);
            }
        }else{
            redisUtil.set(RedisKey.IP_TIMES_LIMIT_THIRTY_MINUTE + ip,0,1800);
        }
    }

}