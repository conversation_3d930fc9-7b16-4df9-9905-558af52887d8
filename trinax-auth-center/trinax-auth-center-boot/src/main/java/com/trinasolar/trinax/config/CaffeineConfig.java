package com.trinasolar.trinax.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class CaffeineConfig {
    @Bean
    public Cache<String, Object> permissionCaffeineCache() {
        return Caffeine.newBuilder()
                // 设置最后一次写入或访问后经过固定时间过期
                .expireAfterWrite(365, TimeUnit.DAYS)
                // 初始的缓存空间大小
                .initialCapacity(16)
                // 缓存的最大条数
                .maximumSize(500)
                .build();
    }

}


