package com.trinasolar.trinax.billing.invoice.service;

import com.trinasolar.trinax.billing.dto.input.*;
import com.trinasolar.trinax.billing.dto.input.changeowner.InvoiceChangeOwnerReqDTO;
import com.trinasolar.trinax.billing.dto.mq.InvoiceMessageMqDTO;
import com.trinasolar.trinax.billing.dto.output.InvoiceApplicationDetailResDTO;
import com.trinasolar.trinax.billing.dto.output.InvoiceDownResDTO;
import com.trinasolar.trinax.billing.dto.output.InvoiceProductResDTO;
import com.trinasolar.trinax.billing.dto.output.InvoiceResDTO;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.contract.dto.input.changeowner.ContractChangeOwnerReqDTO;

import java.util.List;

public interface InvoiceApplicationService {
    Result<String> createInvoiceApplication(CreateApplicationReqDTO req);

    Result<List<InvoiceResDTO>> invoiceApplicationList(InvoiceListReqDTO req);

    Result<InvoiceApplicationDetailResDTO> invoiceApplicationDetail(InvoiceDetailQryReqDTO req);

    Result<PageResponse<InvoiceResDTO>> invoiceApplicationPage(PageRequest<InvoiceListReqDTO> req);

    Result<String> modifyInvoiceApplication(ModifyApplicationReqDTO req);

    Result<PageResponse<InvoiceResDTO>> invoiceApplicationPagePc(PageRequest<InvoiceListPcReqDTO> req);

    Result<List<InvoiceResDTO>> invoiceApplicationListPc(InvoiceListPcReqDTO req);

    void sendMessageForInvoice(InvoiceMessageMqDTO req);

    Result<String> delInvoiceApplication(RecallInvoiceReqDTO req);

    Result<List<InvoiceDownResDTO>> downloadInvoiceListPc(InvoiceListPcReqDTO req);

    Result<String> submitInvoiceApplication(RecallInvoiceReqDTO req);

    Result<String> invoiceChangeOwner(InvoiceChangeOwnerReqDTO reqDTO);
}
