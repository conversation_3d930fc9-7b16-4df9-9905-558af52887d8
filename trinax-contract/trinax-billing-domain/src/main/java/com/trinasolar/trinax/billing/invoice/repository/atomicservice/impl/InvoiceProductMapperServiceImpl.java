package com.trinasolar.trinax.billing.invoice.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.billing.invoice.repository.atomicservice.InvoiceProductMapperService;
import com.trinasolar.trinax.billing.invoice.repository.mapper.InvoiceProductMapper;
import com.trinasolar.trinax.billing.invoice.repository.po.InvoiceProductPO;
import org.springframework.stereotype.Service;

@Service
public class InvoiceProductMapperServiceImpl extends ServiceImpl<InvoiceProductMapper, InvoiceProductPO> implements InvoiceProductMapperService {
}
