package com.trinasolar.trinax.billing.invoice.service.impl;

import com.trinasolar.trinax.basic.constants.enums.changeowner.ChangeSituationEnum;
import com.trinasolar.trinax.billing.constants.enums.InvoiceMessageSituationEnum;
import com.trinasolar.trinax.billing.dto.input.*;
import com.trinasolar.trinax.billing.dto.input.changeowner.InvoiceChangeOwnerReqDTO;
import com.trinasolar.trinax.billing.dto.mq.InvoiceMessageMqDTO;
import com.trinasolar.trinax.billing.dto.output.InvoiceApplicationDetailResDTO;
import com.trinasolar.trinax.billing.dto.output.InvoiceDownResDTO;
import com.trinasolar.trinax.billing.dto.output.InvoiceResDTO;
import com.trinasolar.trinax.billing.invoice.service.InvoiceApplicationService;
import com.trinasolar.trinax.billing.invoice.service.biz.*;
import com.trinasolar.trinax.billing.invoice.service.biz.changeowner.InvoiceChangeOwnerBiz;
import com.trinasolar.trinax.billing.invoice.service.biz.message.*;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class InvoiceApplicationServiceImpl implements InvoiceApplicationService {

    final CreateInvoiceBiz createInvoiceBiz;
    final InvoiceQueryBiz invoiceQueryBiz;
    final ModifyInvoiceBiz modifyInvoiceBiz;
    final InvoicePcQueryBiz invoicePcQueryBiz;
    final InvoiceExternalSubmitMessageBiz invoiceExternalSubmitMessageBiz;
    final RecallInvoiceBiz recallInvoiceBiz;
    final ConfirmInvoiceBiz confirmInvoiceBiz;
    final InvoiceChangeOwnerBiz invoiceChangeOwnerBiz;


    @Override
    public Result<String> createInvoiceApplication(CreateApplicationReqDTO req) {
        return createInvoiceBiz.createInvoiceApplication(req);
    }

    @Override
    public Result<String> modifyInvoiceApplication(ModifyApplicationReqDTO req) {
        return modifyInvoiceBiz.modifyInvoiceApplication(req);
    }

    @Override
    public Result<List<InvoiceResDTO>> invoiceApplicationList(InvoiceListReqDTO req) {
        return invoiceQueryBiz.invoiceApplicationList(req);
    }

    @Override
    public Result<InvoiceApplicationDetailResDTO> invoiceApplicationDetail(InvoiceDetailQryReqDTO req) {
        return invoiceQueryBiz.invoiceApplicationDetail(req);
    }

    @Override
    public Result<PageResponse<InvoiceResDTO>> invoiceApplicationPage(PageRequest<InvoiceListReqDTO> req) {
        return invoiceQueryBiz.invoiceApplicationPage(req);
    }

    @Override
    public Result<PageResponse<InvoiceResDTO>> invoiceApplicationPagePc(PageRequest<InvoiceListPcReqDTO> req) {
        return invoicePcQueryBiz.invoiceApplicationPagePc(req);
    }

    @Override
    public Result<List<InvoiceResDTO>> invoiceApplicationListPc(InvoiceListPcReqDTO req) {
        return invoicePcQueryBiz.invoiceApplicationListPc(req);
    }

    @Override
    public void sendMessageForInvoice(InvoiceMessageMqDTO req) {
       if (InvoiceMessageSituationEnum.INVOICE_EXTERNAL_SUBMIT.getCode().equals(req.getSituationType())) {
            invoiceExternalSubmitMessageBiz.execute(req);
        }else {
            log.info("开票申请消息场景类型不存在");
        }
    }

    @Override
    public Result<String> delInvoiceApplication(RecallInvoiceReqDTO req) {
        return recallInvoiceBiz.delInvoiceApplication(req);
    }

    @Override
    public Result<List<InvoiceDownResDTO>> downloadInvoiceListPc(InvoiceListPcReqDTO req) {
        return invoicePcQueryBiz.downloadInvoiceListPc(req);
    }

    @Override
    public Result<String> submitInvoiceApplication(RecallInvoiceReqDTO req) {
        return confirmInvoiceBiz.submitInvoiceApplication(req);
    }

    @Override
    public Result<String> invoiceChangeOwner(InvoiceChangeOwnerReqDTO reqDTO) {
        return invoiceChangeOwnerBiz.changeOwner(reqDTO);
    }
}
