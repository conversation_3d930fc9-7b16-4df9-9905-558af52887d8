package com.trinasolar.trinax.billing.invoice.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 开票OA合同产品行信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("invoice_contract_item")
public class InvoiceContractItemPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主表业务主键
     */
    private String mainBusinessId;

    /**
     * 关联表业务主键
     */
    private String relationBusinessId;

    /**
     * 唯一id：合同族id+产品+功率+单价
     */
    private String uniqueId;

    /**
     * 产品规格
     */
    private String product;

    /**
     * 产品类型：Module-组件；Part - 备件和其他
     */
    private String productType;

    /**
     * 产品类别：PRODUCT-产品；COMPLIMENTARY - 赠品
     */
    private String productCategory;

    /**
     * 产品小图
     */
    private String productIcon;

    /**
     * 功率
     */
    private String power;

    /**
     * 价格
     */
    private String price;

    /**
     * 剩余可开票数量
     */
    private BigDecimal quantity;

    /**
     * 产品容量（MW）
     */
    private BigDecimal volume;

    /**
     * 是否已删除;1：已删除 0：未删除
     */
    private Integer isDeleted;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建人姓名
     */
    private String createdName;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新人姓名
     */
    private String updatedName;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;


}
