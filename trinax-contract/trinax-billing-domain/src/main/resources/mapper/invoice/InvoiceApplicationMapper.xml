<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trinasolar.trinax.billing.invoice.repository.mapper.InvoiceApplicationMapper">

    <sql id="invoiceList">
        select
        <include refid="invoiceQueryTitle"/>
        from invoice_application ia
        where 1=1
        <if test="query.currentUserType == 'EXTERNAL'">
            and ((ia.created_by,ia.enterprise_id) in
            <foreach item="item" collection="query.subDealerRel" index="index" open="(" separator=","
                     close=")">
                (#{item.dealerUserId},#{item.enterpriseId})
            </foreach>
            or ia.created_by = #{query.currentUserId})
        </if>
        <if test="query.currentUserType == 'INTERNAL'">
            and ia.trina_contact_id in
            <foreach item="item" collection="query.userList" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.statusList != null and query.statusList.size() > 0">
            and ia.application_status in
            <foreach collection="query.statusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.searchStr != null and query.searchStr != ''">
            and (ia.enterprise_name like CONCAT('%', #{query.searchStr}, '%') or
            ia.invoice_application_id like CONCAT('%', #{query.searchStr}, '%')
            )
        </if>
        order by created_time desc
    </sql>

    <sql id="invoiceQueryTitle">
        invoice_application_id
        invoiceApplicationId,
       enterprise_id                enterpriseId,
       enterprise_name              enterpriseName,
       tax_number                   taxNumber,
       head_id                      headId,
       marketing_account_id         marketingAccountId,
       marketing_account_ou         marketingAccountOu,
       invoice_type                 invoiceType,
       invoice_application_amount   invoiceApplicationAmount,
       invoicing_unit               invoicingUnit,
       (case invoicing_unit when 'W' then '是' else '否' end) as isInvoicingUnitW,
       other_unit                   otherUnit,
       address                      address,
       mobile                       mobile,
       bank_name                    bankName,
       bank_no                      bankNo,
       trina_contact_id             trinaContactId,
       trina_contact_name           trinaContactName,
       trina_contact_mobile         trinaContactMobile,
       operation_internal_user_id   operationInternalUserId,
       operation_internal_user_name operationInternalUserName,
       comment                      comment,
       applicants_user_id           applicantsUserId,
       applicants_user_name         applicantsUserName,
       applicants_mobile            applicantsMobile,
       application_status           applicationStatus,
       invoice_quantity_mw          invoiceQuantityMw,
       is_deleted                   isDeleted,
       created_by                   createdBy,
       created_name                 createdName,
       created_time                 createdTime,
       updated_by                   updatedBy,
       updated_name                 updatedName,
       updated_time                 updatedTime
    </sql>

    <sql id="invoiceListPc">
        select
        <include refid="invoiceQueryTitle"/>
        from invoice_application ia
        where 1=1
        <include refid="pcQueryCondition"/>
        order by created_time desc
    </sql>

    <sql id="pcQueryCondition">
        <if test="query.currentUserType == 'SALES'">
            and ia.trina_contact_id in
            <foreach item="item" collection="query.userIdList" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.currentUserType == 'OPERATOR'">
            and ia.operation_internal_user_id in
            <foreach item="item" collection="query.userIdList" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.currentUserType == 'EXTERNAL'">
            and ((ia.created_by,ia.enterprise_id) in
            <foreach item="item" collection="query.subDealerRel" index="index" open="(" separator=","
                     close=")">
                (#{item.dealerUserId},#{item.enterpriseId})
            </foreach>
            or ia.created_by = #{query.currentUserId})
        </if>
        <if test="query.applicationStatus != null and query.applicationStatus !=''">
            and ia.application_status = #{query.applicationStatus}
        </if>
        <if test="query.invoiceApplicationId != null and query.invoiceApplicationId != ''">
            and ia.invoice_application_id like CONCAT('%',#{query.invoiceApplicationId}, '%')
        </if>
        <if test="query.enterpriseName != null and query.enterpriseName != ''">
            and ia.enterprise_name like CONCAT('%',#{query.enterpriseName}, '%')
        </if>
        <if test="query.taxNumber != null and query.taxNumber != ''">
            and ia.tax_number like CONCAT('%',#{query.taxNumber}, '%')
        </if>
        <if test="query.marketingAccountId != null and query.marketingAccountId != ''">
            and ia.marketing_account_id = #{query.marketingAccountId}
        </if>
        <if test="query.applicantsUserName != null and query.applicantsUserName != ''">
            and ia.applicants_user_name like CONCAT('%',#{query.applicantsUserName}, '%')
        </if>
        <if test="query.enterpriseId != null and query.enterpriseId != ''">
            and ia.enterprise_id = #{query.enterpriseId}
        </if>
        <if test="query.trinaContactName != null and query.trinaContactName != ''">
            and ia.trina_contact_name like CONCAT('%',#{query.trinaContactName}, '%')
        </if>
        <if test="query.applyTimeBegin != null">
            and ia.created_time &gt; #{query.applyTimeBegin}
        </if>
        <if test="query.applyTimeEnd != null">
            and ia.created_time &lt; #{query.applyTimeEnd}
        </if>
    </sql>


    <select id="qryInvoicePageFromApp"
            resultType="com.trinasolar.trinax.billing.dto.output.InvoiceResDTO">
        <include refid="invoiceList"/>
    </select>

    <select id="qryInvoiceFromApp"
            resultType="com.trinasolar.trinax.billing.dto.output.InvoiceResDTO">
        <include refid="invoiceList"/>
    </select>

    <select id="qryInvoiceFromPc"
            resultType="com.trinasolar.trinax.billing.dto.output.InvoiceResDTO">
        <include refid="invoiceListPc"/>
    </select>


    <select id="qryInvoicePageFromPc"
            resultType="com.trinasolar.trinax.billing.dto.output.InvoiceResDTO">
        <include refid="invoiceListPc"/>
    </select>


    <select id="qryDownloadInvoice"
            resultType="com.trinasolar.trinax.billing.dto.output.InvoiceDownResDTO">
        select ia.invoice_application_id invoiceApplicationId,
        ia.enterprise_name enterpriseName,
        ia.tax_number taxNumber,
        ia.marketing_account_ou marketingAccountOu,
        ia.bank_name bankName,
        ia.bank_no bankNo,
        ia.address address,
        ia.mobile mobile,
        ia.invoice_quantity_mw invoiceQuantityMw,
        ia.invoice_application_amount invoiceApplicationAmount,
        ia.trina_contact_mobile trinaContactMobile,
        ia.applicants_mobile applicantsMobile,
        ia.updated_name updatedName,
        ia.updated_time updatedTime,
        (case ia.invoicing_unit
        when 'W' then '是'
        else '否' end) isInvoicingUnitW,
        (case ia.invoicing_unit
        when 'watt' then '瓦'
        when 'block' then '块'
        when 'piece' then '片'
        when 'size' then '个'
        when 'other' then '其他'
        else ia.invoicing_unit end) invoicingUnitStr,
        ia.trina_contact_name trinaContactName,
        ia.applicants_user_name applicantsUserName,
        ia.created_time createdTime,
        (case ia.application_status
        when 'un submitted' then '未提交'
        when 'Submitted' then '已提交'
        else ia.invoicing_unit end) applicationStatusStr
        from invoice_application ia
        where 1=1
        <include refid="pcQueryCondition"/>
        order by created_time desc
    </select>
    <select id="selectChangeOwnerRecord"
            resultType="com.trinasolar.trinax.billing.invoice.repository.po.InvoiceApplicationPO">
        select t1.*
        from
        trinax_contract.invoice_application t1
        where t1.application_status !='UN_SUBMITTED'
        <if test="req.changeType=='SALES_USER_CHANGE' and req.conditionList != null and req.conditionList.size() > 0">
            and t1.trina_contact_id = #{req.originUserId}
            and (t1.enterprise_id,t1.applicants_user_id) in
            <foreach collection="req.conditionList" item="item" index="index" open="(" close=")" separator=",">
                (#{item.enterpriseId}, #{item.userId})
            </foreach>
        </if>
        <if test="req.changeType=='OPERATION_USER_CHANGE' and req.conditionList != null and req.conditionList.size() > 0">
            and t1.operation_internal_user_id = #{req.originUserId}
            and (t1.trina_contact_id) in
            <foreach collection="req.conditionList" item="item" index="index" open="(" close=")" separator=",">
                (#{item.userId})
            </foreach>
        </if>
        <if test="req.changeType=='EXTERNAL_USER_CHANGE' and req.conditionList != null and req.conditionList.size() > 0">
            and t1.applicants_user_id = #{req.originUserId}
            and (t1.enterprise_id,t1.trina_contact_id) in
            <foreach collection="req.conditionList" item="item" index="index" open="(" close=")" separator=",">
                (#{item.enterpriseId}, #{item.userId})
            </foreach>
        </if>
    </select>
</mapper>
