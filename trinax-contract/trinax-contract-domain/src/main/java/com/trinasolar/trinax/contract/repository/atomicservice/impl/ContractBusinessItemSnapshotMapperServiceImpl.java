package com.trinasolar.trinax.contract.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.contract.repository.atomicservice.ContractBusinessItemSnapshotMapperService;
import com.trinasolar.trinax.contract.repository.mapper.ContractBusinessItemSnapshotMapper;
import com.trinasolar.trinax.contract.repository.po.ContractBusinessItemSnapshotPO;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ContractBusinessItemSnapshotMapperServiceImpl extends ServiceImpl<ContractBusinessItemSnapshotMapper, ContractBusinessItemSnapshotPO> implements ContractBusinessItemSnapshotMapperService {

    @Override
    public List<ContractBusinessItemSnapshotPO> listByContractId(String contractId) {
        return list(new LambdaQueryWrapper<ContractBusinessItemSnapshotPO>()
                .eq(ContractBusinessItemSnapshotPO::getContractId, contractId)
//                .eq(ContractBusinessItemSnapshotPO::getIsDeleted, 0L)
        );
    }
}
