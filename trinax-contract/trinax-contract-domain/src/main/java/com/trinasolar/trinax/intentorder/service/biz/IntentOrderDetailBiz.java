package com.trinasolar.trinax.intentorder.service.biz;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.trinasolar.trinax.basic.api.OptionFeign;
import com.trinasolar.trinax.basic.dto.Region.RegionQueryResDTO;
import com.trinasolar.trinax.basic.dto.input.OptionItemBatchReqDTO;
import com.trinasolar.trinax.basic.dto.output.OptionItemLanguageResDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.contract.repository.atomicservice.ContractBusinessMapperService;
import com.trinasolar.trinax.contract.repository.po.ContractBusinessPO;
import com.trinasolar.trinax.intentorder.constants.IntentOrderConstant;
import com.trinasolar.trinax.intentorder.constants.enums.*;
import com.trinasolar.trinax.intentorder.dto.input.IntentOrderPCQueryReqDTO;
import com.trinasolar.trinax.intentorder.dto.output.IntentOrderDetailResDTO;
import com.trinasolar.trinax.intentorder.dto.output.IntentOrderItemResDTO;
import com.trinasolar.trinax.intentorder.manager.BasicRegionManager;
import com.trinasolar.trinax.intentorder.manager.GetSubordinateManager;
import com.trinasolar.trinax.intentorder.manager.OrganizationManager;
import com.trinasolar.trinax.intentorder.manager.UserManager;
import com.trinasolar.trinax.intentorder.repository.atomicservice.IntentOrderItemMapperService;
import com.trinasolar.trinax.intentorder.repository.atomicservice.IntentOrderMapperService;
import com.trinasolar.trinax.intentorder.repository.dao.input.IntentOrderQueryObjectDAO;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderItemPO;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderPO;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseTypeEnum;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.SysOrganizationTypeEnum;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import com.trinasolar.trinax.user.dto.input.SysUserQueryDTO;
import com.trinasolar.trinax.user.dto.output.SysOrganizationRespDTO;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.trinasolar.trinax.user.api.SysOrganizationFeign;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
public class IntentOrderDetailBiz {

    @Autowired
    private IntentOrderItemMapperService intentOrderItemMapperService;

    @Autowired
    private IntentOrderMapperService intentOrderMapperService;

    @Autowired
    private OptionFeign optionFeign;

    @Autowired
    private BasicRegionManager basicRegionManager;

    @Autowired
    private ContractBusinessMapperService contractBusinessMapperService;

    @Autowired
    private SysUserFeign sysUserFeign;

    @Autowired
    private UserManager userManager;

    @Autowired
    private OrganizationManager organizationManager;

    @Autowired
    private GetSubordinateManager getSubordinateManager;

    @Autowired
    private SysOrganizationFeign sysOrganizationFeign;


    /**
     * 手机端分页查询
     *
     * @param reqDTO
     * @return
     */
    public IntentOrderDetailResDTO findIntentOrderDetail(IntentOrderPCQueryReqDTO reqDTO) {
        IntentOrderPO intentOrderPO = intentOrderMapperService.lambdaQuery()
                .eq(IntentOrderPO::getIntentOrderNo, reqDTO.getIntentOrderNo())
                .eq(IntentOrderPO::getIsDeleted, DeleteFlagEnum.NOT_DELETE.getCode()).one();
        if (ObjectUtils.isEmpty(intentOrderPO)) {
            return null;
        }
        IntentOrderDetailResDTO detailResDTO = BeanUtil.toBean(intentOrderPO, IntentOrderDetailResDTO.class);
        //查询详情时，需要校验该意向单当前用户是否有权限查看
        //当前用户是否是管理员角色
        boolean isAdmin = userManager.isAdmin(reqDTO.getCurrentUserId());

        //管理员能够看所有的，不做控制
        if (!isAdmin) {
            //其他用户走权限验证：无权限时直接返回空
            if (!permissionCheck(detailResDTO, reqDTO)) {
                return null;
            }
        }
        //组装返回
        generate(detailResDTO, reqDTO);
        return detailResDTO;
    }

    /**
     * 权限校验，有数据权限返回true；无数据权限返回false
     *
     * @return
     */
    private boolean permissionCheck(IntentOrderDetailResDTO detailResDTO, IntentOrderPCQueryReqDTO reqDTO) {
        // 草稿状态数据只有自己查看
        if(IntentOrderStatusEnum.UN_SUBMIT.getCode().equals(detailResDTO.getStatus())){
            return reqDTO.getCurrentUserId().equals(detailResDTO.getCreatedBy());
        }
        //非管理员区分内外部用户分别校验
        if (SysUserTypeEnum.INTERNAL.getType().equals(reqDTO.getCurrentUserType())) {
            String orgType = organizationManager.getOrgTypeByUserId(reqDTO.getCurrentUserId());
            //用户组织类型为common时，查询所有类型为销售的业务组织编码
            if (SysOrganizationTypeEnum.COMMON.getCode().equals(orgType)) {
                List<String> subUserIdList = getSubordinateManager.getOperationSubUserList(reqDTO.getCurrentUserId());
                subUserIdList.add(reqDTO.getCurrentUserId());
                return subUserIdList.contains(detailResDTO.getSalesInternalUserId())
                        || subUserIdList.contains(detailResDTO.getOperationInternalUserId());
            } else if (SysOrganizationTypeEnum.SALES.getCode().equals(orgType)) {
                List<IntentOrderQueryObjectDAO> queryObjectDAOS = getSubordinateManager.getSalesSubordinate(reqDTO.getCurrentUserId());
                List<IntentOrderQueryObjectDAO> permissionList = queryObjectDAOS.stream()
                        .filter(e -> e.getUserId().equals(detailResDTO.getSalesInternalUserId()) && e.getBizOrganizationCode().equals(detailResDTO.getBizOrganizationCode()))
                        .collect(Collectors.toList());
                return !CollectionUtils.isEmpty(permissionList);
            } else if (SysOrganizationTypeEnum.OPERATION.getCode().equals(orgType)) {
                List<String> operationUserIdList = getSubordinateManager.getOperationSubUserList(reqDTO.getCurrentUserId());
                //兼容返回运营人员为空的情况
                operationUserIdList.add(reqDTO.getCurrentUserId());
                return operationUserIdList.contains(detailResDTO.getOperationInternalUserId());
            } else {
                return false;
            }
        } else {
            List<IntentOrderQueryObjectDAO> queryObjectDAOS = getSubordinateManager.getDealerSubordinate(reqDTO.getCurrentUserId());
            List<IntentOrderQueryObjectDAO> permissionList = queryObjectDAOS.stream()
                    .filter(e -> (e.getUserId().equals(detailResDTO.getExternalUserId()) && e.getEnterpriseId().equals(detailResDTO.getEnterpriseId()))
                            ||(e.getUserId().equals(detailResDTO.getExternalUserId()) && e.getEnterpriseId().equals(detailResDTO.getCapitalEnterpriseId())))
                    .collect(Collectors.toList());
            return !CollectionUtils.isEmpty(permissionList);
        }
    }

    /**
     * 组装返回
     *
     * @param detailResDTO
     * @param reqDTO
     */
    private void generate(IntentOrderDetailResDTO detailResDTO, IntentOrderPCQueryReqDTO reqDTO) {

        //这里的意向单行信息，手机端详情接口需要要
        List<IntentOrderItemPO> itemPOList = intentOrderItemMapperService.lambdaQuery()
                .eq(IntentOrderItemPO::getIntentOrderNo, reqDTO.getIntentOrderNo())
                .eq(IntentOrderItemPO::getIsDeleted, DeleteFlagEnum.NOT_DELETE.getCode())
                .list();
        List<IntentOrderItemResDTO> itemResList = BeanUtil.copyToList(itemPOList, IntentOrderItemResDTO.class);
        detailResDTO.setItemList(itemResList);

        detailResDTO.setIncotermText(DeliveryTypeEnum.getDescByCode(detailResDTO.getIncoterm()));
        detailResDTO.setOpportunitySegmentText(BusinessChanceMarketEnum.getDescByCode(detailResDTO.getOpportunitySegment()));
        detailResDTO.setGoodsArrivalRegionText(DeliveryRegionEnum.getDescByCode(detailResDTO.getGoodsArrivalRegion()));
        detailResDTO.setFactoryRegionTypeText(FactoryTypeEnum.getDescByCode(detailResDTO.getFactoryRegionType()));
        boolean ignore = SysUserTypeEnum.EXTERNAL.getType().equals(reqDTO.getCurrentUserType());
        if (ignore) {
            detailResDTO.setStatusText(IntentOrderStatusEnum.getNameByCodeIgnore(detailResDTO.getStatus()));
        } else {
            detailResDTO.setStatusText(IntentOrderStatusEnum.getNameByCode(detailResDTO.getStatus()));
        }
        detailResDTO.setIsSurrogateOrderText(OrderMethodEnum.getDescByCode(detailResDTO.getIsSurrogateOrder()));
        detailResDTO.setRegionalKeyAccountText(Boolean.TRUE.equals(detailResDTO.getRegionalKeyAccount()) ? "是" : "否");
        detailResDTO.setOpportunityCurrencyText(CurrencyTypeEnum.getDescByCode(detailResDTO.getOpportunityCurrency()));
        detailResDTO.setOpportunityChannelTypeText(BusinessChanceChannelEnum.getNameByCode(detailResDTO.getOpportunityChannelType()));
        detailResDTO.setAccountRoleText(AccountRoleEnum.getDescByCode(detailResDTO.getAccountRole()));
        detailResDTO.setEfcText(detailResDTO.isEfc() ? "是" : "否");
        detailResDTO.setSignEntity(StringUtils.isEmpty(detailResDTO.getCapitalEnterpriseName())?detailResDTO.getEnterpriseName():detailResDTO.getCapitalEnterpriseName());
        detailResDTO.setCustomerCategoryText(EnterpriseTypeEnum.getDescByCode(detailResDTO.getCustomerCategory()));
        //战区数据填充
        Result<List<SysOrganizationRespDTO>> sysOrganizationResult = sysOrganizationFeign.getOrgListByType(SysOrganizationTypeEnum.SALES.getCode());
        Map<String, String> organizationInfoMap =  sysOrganizationResult.getData().stream().collect(Collectors.toMap(SysOrganizationRespDTO::getOrganizationCode, SysOrganizationRespDTO::getOrganizationName));
        detailResDTO.setBizOrganizationName(organizationInfoMap.get(detailResDTO.getBizOrganizationCode()));
        // 区域信息转换
        List<String> codeList = new ArrayList<>();
        codeList.add(detailResDTO.getGoodsArrivalSubRegion());
        codeList.add(detailResDTO.getArea());
        Map<String, List<RegionQueryResDTO>> regionMap = basicRegionManager.getRegionMap(codeList);
        if (!CollectionUtils.isEmpty(regionMap.get(detailResDTO.getGoodsArrivalSubRegion()))) {
            detailResDTO.setGoodsArrivalSubRegionText(regionMap.get(detailResDTO.getGoodsArrivalSubRegion()).get(0).getName());
        }
        if (!CollectionUtils.isEmpty(regionMap.get(detailResDTO.getArea()))) {
            detailResDTO.setAreaText(regionMap.get(detailResDTO.getArea()).get(0).getName());
        }

        //批量查询字典进行转换：外部用户取消原因，内部用户取消原因，竞争对手，付款条款
        OptionItemBatchReqDTO itemBatchReqDTO = new OptionItemBatchReqDTO();
        itemBatchReqDTO.setLanguage(IntentOrderConstant.OPTION_ITEM_LANGUAGE_CN);
        List<String> optionGroupList = new ArrayList<>();
        optionGroupList.add(IntentOrderConstant.CANCEL_REASON_EXTERNAL);
        optionGroupList.add(IntentOrderConstant.CANCEL_REASON_INTERNAL);
        optionGroupList.add(IntentOrderConstant.CANCEL_REASON_COMPETITOR);
        optionGroupList.add(IntentOrderConstant.OPTION_ITEM_PAYMENT_TERM);
        itemBatchReqDTO.setOptionGroupList(optionGroupList);
        Result<List<OptionItemLanguageResDTO>> optionItemResult = optionFeign.getOptionItemListBatch(itemBatchReqDTO);
        if (Boolean.TRUE.equals(optionItemResult.getSuccess()) && !CollectionUtils.isEmpty(optionItemResult.getData())) {
            //通过optionValue分组
            Map<String, List<OptionItemLanguageResDTO>> optionItemMap = optionItemResult.getData()
                    .stream().collect(Collectors.groupingBy(item -> item.getOptionGroup() + "_" + item.getOptionValue()));

            if (!CollectionUtils.isEmpty(optionItemMap.get(IntentOrderConstant.CANCEL_REASON_EXTERNAL + "_" + detailResDTO.getCancelReasonType()))) {
                detailResDTO.setCancelReasonType(optionItemMap.get(IntentOrderConstant.CANCEL_REASON_EXTERNAL + "_" + detailResDTO.getCancelReasonType()).get(0).getOptionName());
            }
            if (!CollectionUtils.isEmpty(optionItemMap.get(IntentOrderConstant.CANCEL_REASON_INTERNAL + "_" + detailResDTO.getCancelReasonType()))) {
                detailResDTO.setCancelReasonType(optionItemMap.get(IntentOrderConstant.CANCEL_REASON_INTERNAL + "_" + detailResDTO.getCancelReasonType()).get(0).getOptionName());
            }
            if (!CollectionUtils.isEmpty(optionItemMap.get(IntentOrderConstant.CANCEL_REASON_COMPETITOR + "_" + detailResDTO.getCompetitorInfo()))) {
                detailResDTO.setCompetitorInfo(optionItemMap.get(IntentOrderConstant.CANCEL_REASON_COMPETITOR + "_" + detailResDTO.getCompetitorInfo()).get(0).getOptionName());
            }
            if (!CollectionUtils.isEmpty(optionItemMap.get(IntentOrderConstant.OPTION_ITEM_PAYMENT_TERM + "_" + detailResDTO.getPaymentTerm()))) {
                detailResDTO.setPaymentTermText(optionItemMap.get(IntentOrderConstant.OPTION_ITEM_PAYMENT_TERM + "_" + detailResDTO.getPaymentTerm()).get(0).getOptionName());
            }
        }
        if (StringUtils.isNotBlank(detailResDTO.getFullRegion())) {
            detailResDTO.setFullRegionText(detailResDTO.getFullRegion().replace("|", ""));
        }

        //填充合同信息
        List<ContractBusinessPO> contractBusinessPOS = contractBusinessMapperService.listByIntentOrderNos(Lists.newArrayList(detailResDTO.getIntentOrderNo()));
        detailResDTO.setContractIds(contractBusinessPOS.stream().map(ContractBusinessPO::getContractId).collect(Collectors.toList()));

        //用户相关信息填充
        List<String> userIdList = new ArrayList<>();
        userIdList.add(detailResDTO.getSalesInternalUserId());
        userIdList.add(detailResDTO.getCancelUserId());
        if(!CollectionUtils.isEmpty(userIdList)){
            SysUserQueryDTO queryDTO = new SysUserQueryDTO();
            queryDTO.setUserIdList(userIdList);
            Result<List<SysUserRespDTO>> listResult = sysUserFeign.listUser(queryDTO);
            if(Boolean.TRUE.equals(listResult.getSuccess()) && !CollectionUtils.isEmpty(listResult.getData())){
                Map<String,List<SysUserRespDTO>> map = listResult.getData().stream().collect(Collectors.groupingBy(SysUserRespDTO::getUserId));
                if (StringUtils.isNotBlank(detailResDTO.getSalesInternalUserId()) && !CollectionUtils.isEmpty(map.get(detailResDTO.getSalesInternalUserId()))) {
                    detailResDTO.setSalesInternalUserPhone(map.get(detailResDTO.getSalesInternalUserId()).get(0).getMobile());
                }
                if (StringUtils.isNotBlank(detailResDTO.getCancelUserId()) && !CollectionUtils.isEmpty(map.get(detailResDTO.getCancelUserId()))) {
                    detailResDTO.setCancelUserName(map.get(detailResDTO.getCancelUserId()).get(0).getUserName());
                }
            }
        }
    }
}
