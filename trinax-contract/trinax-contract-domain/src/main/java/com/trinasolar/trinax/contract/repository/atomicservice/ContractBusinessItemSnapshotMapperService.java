package com.trinasolar.trinax.contract.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.trinax.contract.repository.po.ContractBusinessItemSnapshotPO;

import java.util.List;

public interface ContractBusinessItemSnapshotMapperService extends IService<ContractBusinessItemSnapshotPO> {

    List<ContractBusinessItemSnapshotPO> listByContractId(String contractId);

}
