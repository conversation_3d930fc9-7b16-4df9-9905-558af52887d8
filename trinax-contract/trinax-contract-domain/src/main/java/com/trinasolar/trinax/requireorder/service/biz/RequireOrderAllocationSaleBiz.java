package com.trinasolar.trinax.requireorder.service.biz;

import cn.hutool.json.JSONUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.requireorder.constants.RequireOrderConstant;
import com.trinasolar.trinax.requireorder.constants.RequireOrderResultCode;
import com.trinasolar.trinax.requireorder.constants.enums.AllocationSaleTypeEnum;
import com.trinasolar.trinax.requireorder.constants.enums.RequireOrderMessageSituationEnum;
import com.trinasolar.trinax.requireorder.constants.enums.RequireOrderStatusEnum;
import com.trinasolar.trinax.requireorder.constants.enums.TrackerTypeEnum;
import com.trinasolar.trinax.requireorder.dto.input.RequireOrderAllocationSaleReqDTO;
import com.trinasolar.trinax.requireorder.dto.mq.RequireOrderMessageMqDTO;
import com.trinasolar.trinax.requireorder.manager.RequireBizTransferManager;
import com.trinasolar.trinax.requireorder.manager.TodoCompleteManager;
import com.trinasolar.trinax.requireorder.repository.atomicservice.RequireOrderMapperService;
import com.trinasolar.trinax.requireorder.repository.po.RequireOrderPO;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.dto.input.SysSalesOperationRelationReqDTO;
import com.trinasolar.trinax.user.dto.output.SysSalesOperationRelationRespDTO;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.ExecutorService;


@Service
@Slf4j
public class RequireOrderAllocationSaleBiz {

    @Autowired
    private RequireOrderMapperService requireOrderMapperService;

    @Autowired
    private SysUserFeign sysUserFeign;

    @Autowired
    private MqManager mqManager;

    @Autowired
    private ExecutorService executorService;

    @Autowired
    private RequireBizTransferManager requireBizTransferManager;

    @Autowired
    private TodoCompleteManager todoCompleteManager;

    public void allocationSale(RequireOrderAllocationSaleReqDTO reqDTO) {
        //校验并获取数据
        RequireOrderPO requireOrderPO = check(reqDTO.getRequireOrderNo());
        //分配区域总或者销售（都是更新到同一个字段）
        doAllocation(requireOrderPO,reqDTO);
        //分配需求单后处理
        executorService.execute(()->sendAllocationMsg(requireOrderPO,reqDTO));
        //保存添加操作记录
        requireBizTransferManager.saveTransferRecord(reqDTO.getRequireOrderNo(),reqDTO.getCurrentUserId(),reqDTO.getCurrentUserName(),reqDTO.getSalesInternalUserId());
    }


    private RequireOrderPO check(String requireOrderNo){
        RequireOrderPO requireOrderPO = requireOrderMapperService.getByRequireOrderNo(requireOrderNo);
        if(ObjectUtils.isEmpty(requireOrderPO)){
            throw new BizException(ResultCode.FAIL.getCode(),"需求单分配失败：需求单不存在");
        }
        if(RequireOrderStatusEnum.CANCELED.getCode().equals(requireOrderPO.getRequireOrderStatus())){
            throw new BizException(RequireOrderResultCode.REQUIRE_ORDER_CANCELED.getCode(),RequireOrderResultCode.REQUIRE_ORDER_CANCELED.getMessage());
        }
        return requireOrderPO;
    }

    /**
     * 分配需求单
     * @param reqDTO
     */
    private void doAllocation(RequireOrderPO requireOrderPO,RequireOrderAllocationSaleReqDTO reqDTO){

        //分配销售后，运营也同步变化
        setOperation(requireOrderPO,reqDTO.getSalesInternalUserId());
        //分配区域总，额外还需要更新：需求单战区，需求所属人信息
        if(AllocationSaleTypeEnum.ALLOCATION_MANAGER.getCode().equals(reqDTO.getAllocationSaleType())){
            allocationManagerParamDeal(requireOrderPO,reqDTO);
        }
        requireOrderPO.setSalesInternalUserId(reqDTO.getSalesInternalUserId());
        requireOrderPO.setOriginSalesInternalUserId(reqDTO.getSalesInternalUserId());
        requireOrderPO.setSalesInternalUserName(reqDTO.getSalesInternalUserName());
        requireOrderPO.setUpdatedBy(reqDTO.getCurrentUserId());
        requireOrderPO.setUpdatedName(reqDTO.getCurrentUserName());
        requireOrderPO.setUpdatedTime(LocalDateTime.now());
        requireOrderPO.setTrackerUserType(TrackerTypeEnum.SALES.getCode());
        requireOrderPO.setPartnerUserId("");
        requireOrderPO.setPartnerUserName("");
        requireOrderPO.setPartnerEnterpriseId("");
        requireOrderPO.setPartnerEnterpriseName("");
        boolean isUpdated = requireOrderMapperService.updateById(requireOrderPO);
        if(!isUpdated){
            throw new BizException(ResultCode.FAIL.getCode(),reqDTO.getCurrentUserName() + "分配需求单" + reqDTO.getRequireOrderNo() + "失败");
        }
    }


    /**
     * 分配区域总时额外处理：需求单战区，需求跟进人，需求所属人都需要修改为新的区域总对应信息
     */
    private void allocationManagerParamDeal(RequireOrderPO requireOrderPO,RequireOrderAllocationSaleReqDTO reqDTO){
        Result<SysUserRespDTO> result =sysUserFeign.getUserByUserId(reqDTO.getSalesInternalUserId());
        if(Boolean.FALSE.equals(result.getSuccess()) && ObjectUtils.isEmpty(result.getData())){
            throw new BizException(ResultCode.FAIL.getCode(),reqDTO.getCurrentUserName() + "分配需求单" + reqDTO.getRequireOrderNo() + "时，获取区域总信息失败");
        }
        requireOrderPO.setBizOrganizationCode(result.getData().getOrganizationCode());
        requireOrderPO.setRequireOrderOwnerId(reqDTO.getSalesInternalUserId());
        requireOrderPO.setOriginRequireOrderOwnerId(reqDTO.getSalesInternalUserId());
        requireOrderPO.setRequireOrderOwnerName(reqDTO.getSalesInternalUserName());


    }

    /**
     * 查找最新运营信息
     * @param requireOrderPO
     * @param newSaleInternalUserId
     */
    private void setOperation(RequireOrderPO requireOrderPO,String newSaleInternalUserId){
        if(StringUtils.isNotBlank(newSaleInternalUserId) && StringUtils.isNotBlank(requireOrderPO.getBizOrganizationCode())){
            SysSalesOperationRelationReqDTO operationReqDTO = new SysSalesOperationRelationReqDTO();
            operationReqDTO.setBizOrganizationCode(requireOrderPO.getBizOrganizationCode());
            operationReqDTO.setSalesUserId(newSaleInternalUserId);
            Result<SysSalesOperationRelationRespDTO> relationResult = sysUserFeign.getOperationBySalesUserId(operationReqDTO);
            if(Boolean.TRUE.equals(relationResult.getSuccess()) && ObjectUtils.isNotEmpty(relationResult.getData())){
                requireOrderPO.setOperationInternalUserId(relationResult.getData().getOperationUserId());
                requireOrderPO.setOperationInternalUserName(relationResult.getData().getOperationUserName());
            }
        }
    }
    /**
     * 分配需求单后发送待办消息
     * @param reqDTO
     */
    private void sendAllocationMsg(RequireOrderPO po,RequireOrderAllocationSaleReqDTO reqDTO){

        //分配给其他内部用户后，完成需求单对应所有待办
        todoCompleteManager.completeByBizNo(reqDTO.getRequireOrderNo(),reqDTO.getCurrentUserId(),reqDTO.getCurrentUserName());

        log.info("分配需求给区域管理或者销售时，给分配人员发送待办消息");
        RequireOrderMessageMqDTO messageMqDTO = new RequireOrderMessageMqDTO();
        if(AllocationSaleTypeEnum.ALLOCATION_SALE.getCode().equals(reqDTO.getAllocationSaleType())){
            messageMqDTO.setSaleInternalUserId(po.getSalesInternalUserId());
            messageMqDTO.setSituationType(RequireOrderMessageSituationEnum.RQ_ALLOCATION_TO_SALE.getCode());
        }else if(AllocationSaleTypeEnum.ALLOCATION_MANAGER.getCode().equals(reqDTO.getAllocationSaleType())){
            messageMqDTO.setAreaManagerUserId(po.getSalesInternalUserId());
            messageMqDTO.setSituationType(RequireOrderMessageSituationEnum.RQ_AREA_MANAGER_ALLOCATION_TO_ANOTHER.getCode());
        }else{
            return;
        }
        messageMqDTO.setCurrentUserId(reqDTO.getCurrentUserId());
        messageMqDTO.setCurrentUserName(reqDTO.getCurrentUserName());
        messageMqDTO.setRequireOrderNo(po.getRequireOrderNo());
        messageMqDTO.setEnterpriseName(po.getEnterpriseName());
        messageMqDTO.setExternalUserId(po.getBusinessExternalUserId());
        messageMqDTO.setExternalUserName(po.getBusinessExternalUserName());
        mqManager.sendTopic(RequireOrderConstant.REQUIRE_ORDER_CHANGED, JSONUtil.toJsonStr(messageMqDTO),true);
    }

}
