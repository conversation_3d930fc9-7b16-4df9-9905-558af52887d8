package com.trinasolar.trinax.contract.service.biz;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.trinasolar.trinax.basic.api.TodoListFeign;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoBizCodeEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoStatusEnum;
import com.trinasolar.trinax.basic.dto.input.TodoListUpdateReqDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.contract.constants.ContractConstants;
import com.trinasolar.trinax.contract.constants.UtilsConstants;
import com.trinasolar.trinax.contract.constants.enums.ContractSituationEnum;
import com.trinasolar.trinax.contract.constants.enums.ContractStatusEnum;
import com.trinasolar.trinax.contract.constants.enums.DataSoftStatusEnum;
import com.trinasolar.trinax.contract.dto.input.ContractFileItems;
import com.trinasolar.trinax.contract.dto.input.ContractInfoReqDTO;
import com.trinasolar.trinax.contract.dto.input.ContractProductItems;
import com.trinasolar.trinax.contract.dto.input.QuickOrderContractSubmitReqDTO;
import com.trinasolar.trinax.contract.dto.mq.ContractMessageMqDTO;
import com.trinasolar.trinax.contract.dto.output.QuickOrderContractPreviewResDTO;
import com.trinasolar.trinax.contract.manager.ContractManager;
import com.trinasolar.trinax.contract.repository.atomicservice.*;
import com.trinasolar.trinax.contract.repository.mapper.ContractMapper;
import com.trinasolar.trinax.contract.repository.po.*;
import com.trinasolar.trinax.contract.utils.IdGenerator;
import com.trinasolar.trinax.integration.api.IntegrationContractFeign;
import com.trinasolar.trinax.integration.dto.input.contract.SfContractFileItemReqDTO;
import com.trinasolar.trinax.integration.dto.input.contract.SfSyncContractReqDTO;
import com.trinasolar.trinax.integration.dto.input.oa.Attachment;
import com.trinasolar.trinax.integration.dto.input.oa.SaleContractToOaReqDTO;
import com.trinasolar.trinax.integration.dto.output.contract.SfSyncContractDTO;
import com.trinasolar.trinax.intentorder.constants.enums.*;
import com.trinasolar.trinax.intentorder.dto.input.IntentOrderItemReqDTO;
import com.trinasolar.trinax.intentorder.repository.atomicservice.IntentOrderItemMapperService;
import com.trinasolar.trinax.intentorder.repository.atomicservice.IntentOrderMapperService;
import com.trinasolar.trinax.intentorder.repository.mapper.IntentOrderMapper;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderItemPO;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderPO;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.dto.output.EnterpriseBizRelationResDTO;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.dto.input.SysUserQueryDTO;
import com.trinasolar.trinax.user.dto.output.SysInternalUserDetailRespDTO;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.trinasolar.trinax.contract.constants.ContractConstants.OFFLINE_CONTRACT;
import static com.trinasolar.trinax.contract.constants.ContractConstants.ONLINE_CONTRACT;

@Service
@Slf4j
@RefreshScope
public class ContractAddBiz {

    @Autowired
    ContractMapper contractMapper;

    @Autowired
    ContractMapperService contractMapperService;

    @Autowired
    ContractBusinessMapperService cBusMapperService;

    @Autowired
    ContractFileMapperService fileMapperService;
    @Autowired
    IntentOrderMapper intentOrderMapper;

    @Autowired
    private IntentOrderMapperService intentOrderMapperService;

    @Autowired
    private IntentOrderItemMapperService intentOrderItemMapperService;

    @Autowired
    IdGenerator idGenerator;

    @Autowired
    ContractBusinessItemMapperService itemMapperService;
    @Autowired
    EnterpriseFeign enterpriseFeign;

    @Autowired
    SysUserFeign sysUserFeign;
    @Autowired
    MqManager mqManager;
    @Autowired
    TodoListFeign todoListFeign;
    @Autowired
    ContractManager contractManager;
    @Autowired
    RedissonClient redissonClient;

    @Autowired
    IntegrationContractFeign integrationContractFeign;
    @Autowired
    ContractFileMapperService contractFileMapperService;
    @Autowired
    QuickOrderContractBiz quickOrderContractBiz;

    @Value("${host.esign}")
    String esignHost;
    @Autowired
    private ContractBusinessItemSnapshotMapperService contractBusinessItemSnapshotMapperService;


    public Result<String> addContract(ContractInfoReqDTO reqDTO) {
        RLock cacheLock = redissonClient.getLock(ContractConstants.CONTRACT_ACCESS + reqDTO.getContractVersion());
        Result<String> result;
        try {
            if (cacheLock.tryLock()) {
                result = doBusiness(reqDTO);
                cacheLock.unlock();
            } else {
                return Result.fail(ResultCode.FAIL.getCode(), "获取合同同步并发锁失败");
            }
        } catch (Exception e) {
            log.error("接受合同异常: ", e);
            throw new BizException(ResultCode.FAIL.getCode(), ":业务异常：" + e.getMessage());
        } finally {
            if (cacheLock.isLocked()) {
                cacheLock.unlock();
            }
        }
        return result;
    }

    private Result<String> doBusiness(ContractInfoReqDTO reqDTO) {
        //意向单数据
        IntentOrderPO intentOrderInfo = getIntentOrder(reqDTO.getOpportunityId());
        if(IntentOrderTypeEnum.QUICK_ORDER.getCode().equals(intentOrderInfo.getOrderType())){
            return quickIntentOrderContract(reqDTO,intentOrderInfo);
        }else{
            return saveIntentOrderContract(reqDTO,intentOrderInfo);
        }
    }

    private Result<String> quickIntentOrderContract(ContractInfoReqDTO reqDTO, IntentOrderPO intentOrderInfo) {
//        List<SysUserRespDTO> userList = loadUserByLocal(reqDTO,intentOrderInfo);
        List<SysUserRespDTO> userList =loadUser(reqDTO);

        ContractPO lastContract = lastContractInit(reqDTO.getContractVersion());
        if (lastContract == null||lastContract.getContractId()==null) {
            return Result.fail(ResultCode.FAIL.getCode(), "获取当前合同信息失败，无法进行后续操作");
        }
        final int MAX_RETRY = 10;
        final int RETRY_INTERVAL = 2;

        int fileCount = 0;
        int retryAttempt = 1;
        while (retryAttempt <= MAX_RETRY) {
            fileCount = contractManager.getContractFile(lastContract.getContractId());
            if (fileCount > 0) {
                break;
            }
            log.info("{}：合同文件未生成，第{}次尝试等待", reqDTO.getContractVersion(), retryAttempt);
            try {
                TimeUnit.SECONDS.sleep(RETRY_INTERVAL);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("合同文件等待线程被中断", e);
                break;
            }
            retryAttempt++;
        }

        if (fileCount == 0) {
            log.info("{}：合同文件未生成，{}次尝试等待都失败", reqDTO.getContractVersion(), MAX_RETRY);
            return Result.fail(ResultCode.FAIL.getCode(), "获取当前合同文件信息失败，无法进行后续操作");
        }

        //contract表初始化
        ContractPO contract = contractPOInit(reqDTO, userList, lastContract, intentOrderInfo);

        //contract_business初始化
        ContractBusinessPO businessPO = businessInit(reqDTO, userList, intentOrderInfo, contract);

        //contract_file初始化
        List<ContractFilePO> contractFiles = contractFilesInit(reqDTO, contract);

        //business_item 初始化
        List<ContractBusinessItemPO> items = businessItemInit(reqDTO, contract, intentOrderInfo);
        businessItemSnapshotInit(reqDTO, contract, items, IntentOrderTypeEnum.QUICK_ORDER.getCode());
        contractManager.batchSaveContractInfo(contract, businessPO, contractFiles, items);

        if(reqDTO.getBpmInfomation()!=null&&StringUtils.isBlank(lastContract.getBpmTaskId())){
            //只有没有bpmTaskId的时候才发送bpm消息
            SaleContractToOaReqDTO convertedReqDTO=bpmReqBuild(reqDTO.getBpmInfomation(),lastContract,intentOrderInfo);
            Result<String> result = integrationContractFeign.sendContractToOa(convertedReqDTO);
            ContractBpmLogPO contractBpmLogPO=contractBpmLogInit(reqDTO,contract,convertedReqDTO);

            if(result.getSuccess()){
                //发送bpm消息成功后，更新contract表bpmTaskId
                contractBpmLogPO.setBpmTaskId(result.getData());
                contractBpmLogPO.setStatus(1);
            }else{
                contractBpmLogPO.setStatus(0);
                contractBpmLogPO.setRemark(result.getMessage());
            }

            contractManager.saveBpmResult(contractBpmLogPO);
            if(result.getSuccess()){
                //修改sf的合同状态
                syncContractStatus(contract,intentOrderInfo);
            }

        }

        return Result.ok();
    }

    private void syncContractStatus(ContractPO contract,IntentOrderPO intentOrderInfo) {
        SfSyncContractReqDTO sfReqDTO = new SfSyncContractReqDTO();
        sfReqDTO.setSfContractId(contract.getSfRecordId());
        sfReqDTO.setContractStatus(ContractStatusEnum.APPROVING.getCode());
        sfReqDTO.setIntentOrderNo(intentOrderInfo.getIntentOrderNo());

        List<ContractFilePO> contractFilePOS=contractFileMapperService.listByContractId(contract.getContractId());
        sfReqDTO.setContractFileItem(contractFilePOS.stream().map(item->{
            SfContractFileItemReqDTO sfContractFileItemReqDTO=new SfContractFileItemReqDTO();
            sfContractFileItemReqDTO.setFileName(item.getFileName());
            sfContractFileItemReqDTO.setFileUrl(item.getFileUrl());
            sfContractFileItemReqDTO.setFileId(item.getFileUrl().substring(item.getFileUrl().lastIndexOf("/")+1));
            return sfContractFileItemReqDTO;
        }).collect(Collectors.toList()));
        Result<SfSyncContractDTO> sfResp = integrationContractFeign.syncContractStatusToSFDC(sfReqDTO);
        log.info("修改合同状态为审批中,contractId={},result={}",contract.getContractId(),JSONObject.toJSONString(sfResp));
    }

    private List<SysUserRespDTO> loadUserByLocal(ContractInfoReqDTO reqDTO,IntentOrderPO intentOrderInfo) {
        SysUserQueryDTO sysUserQueryDTO = new SysUserQueryDTO();
        List<String> userIdList = new ArrayList<>();
        userIdList.add(intentOrderInfo.getOperationInternalUserId());
        userIdList.add(intentOrderInfo.getSalesInternalUserId());
        sysUserQueryDTO.setUserIdList(userIdList);
        Result<List<SysUserRespDTO>> result = sysUserFeign.listUser(sysUserQueryDTO);

        result.getData().forEach(userInfo -> {
            if (intentOrderInfo.getOperationInternalUserId().equals(userInfo.getUserId())) {
                reqDTO.setCreatedById(userInfo.getUserId());
            }
        });

        return result.getData();
    }

    private SaleContractToOaReqDTO bpmReqBuild(SaleContractToOaReqDTO bpmInfomation, ContractPO lastContract, IntentOrderPO intentOrderInfo) {
        SaleContractToOaReqDTO convertedReqDTO= BeanUtil.copyProperties(bpmInfomation, SaleContractToOaReqDTO.class);

        ContractBusinessPO businessPO=cBusMapperService.getOne(new LambdaQueryWrapper<ContractBusinessPO>().eq(ContractBusinessPO::getContractId,lastContract.getContractId()));
        List<ContractBusinessItemPO> items=itemMapperService.list(new LambdaQueryWrapper<ContractBusinessItemPO>().eq(ContractBusinessItemPO::getContractId,lastContract.getContractId()));
        Map<String,ContractBusinessItemPO> optItemMap=items.stream().collect(Collectors.toMap(ContractBusinessItemPO::getSfContractItemId, Function.identity()));

        convertedReqDTO.setFrameInstanceId(businessPO.getFrameContractNo());
        convertedReqDTO.setSfczzr(0);
        if(businessPO.getRebateMoney()!=null){
            convertedReqDTO.setSfczzr(1);
            convertedReqDTO.setSaleAmount(businessPO.getRebateMoney().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        }
        convertedReqDTO.setPayTime(businessPO.getDeadlineDate());
        convertedReqDTO.setPayDueDays(String.valueOf(businessPO.getPayDateGap()));
        convertedReqDTO.setDeliveryTime(DateUtils.format(businessPO.getDeliveryDate(), "yyyy-MM-dd"));
        convertedReqDTO.setDeliveryDueDays(String.valueOf(businessPO.getHandleDateGap()));
        convertedReqDTO.setOrderType(String.valueOf(IntentOrderTypeEnum.QUICK_ORDER.getCode()));
        String companyName=StringUtils.isNotBlank(intentOrderInfo.getCapitalEnterpriseName())?intentOrderInfo.getCapitalEnterpriseName():intentOrderInfo.getEnterpriseName();
        convertedReqDTO.setFileList(getContractFileList(lastContract, companyName+"_"+lastContract.getShortContractNo()+".pdf"));

        convertedReqDTO.getMainProducts().forEach(a->{
            ContractBusinessItemPO contractBusinessItemPO=null;
            if((contractBusinessItemPO=optItemMap.get(a.getSfContractItemId()))!=null){
                a.setPowderRange(convertToRange(contractBusinessItemPO));
//                a.setRemark(convertToRemark(a,contractBusinessItemPO));
            }
        });

        //替换发起人
        Result<SysInternalUserDetailRespDTO> sysInternalUserDetailRespDTOResult=sysUserFeign.getInternalUserByUserId(lastContract.getCreatedBy());
        if(sysInternalUserDetailRespDTOResult.getSuccess()){
            SysInternalUserDetailRespDTO sysInternalUserDetailRespDTO=sysInternalUserDetailRespDTOResult.getData();
            convertedReqDTO.setProposer(sysInternalUserDetailRespDTO.getUserEmail());
        }

        //邮箱替换
        if(convertedReqDTO.getSalesperson().endsWith(".invalid")){
            convertedReqDTO.setSalesperson(convertedReqDTO.getSalesperson().replace(".invalid",""));
        }

        //框架合同替换
        convertedReqDTO.setFrameInstanceId(intentOrderInfo.getContractFrameId());

        //默认标准合同
        convertedReqDTO.setSfbzcgmb(1);

        return convertedReqDTO;
    }


    private List<Attachment> getContractFileList(ContractPO lastContract,final String fileName) {
        List<ContractFilePO> contractFiles = fileMapperService.list(new LambdaQueryWrapper<ContractFilePO>().eq(ContractFilePO::getContractId, lastContract.getContractId()));

        return contractFiles.stream().map(a->{
            Attachment attachment=new Attachment();
            attachment.setFileUrl(a.getFileUrl().replace("pub","browse")+"/"+fileName);
            attachment.setFileName(fileName);
            attachment.setFileType("false");
            return attachment;
        }).collect(Collectors.toList());
    }

    private String convertToRange(ContractBusinessItemPO contractBusinessItemPO) {
        StringBuilder multiPowerRange=new StringBuilder();
        if(StringUtils.isNotBlank(contractBusinessItemPO.getMultiPowerBegin())){
            multiPowerRange.append(contractBusinessItemPO.getMultiPowerBegin());
        }
        if(StringUtils.isNotBlank(contractBusinessItemPO.getMultiPowerEnd())
                &&!contractBusinessItemPO.getMultiPowerEnd().equals(contractBusinessItemPO.getMultiPowerBegin())){
            multiPowerRange.append("-"+contractBusinessItemPO.getMultiPowerEnd());
        }
        return multiPowerRange.toString();
    }

    private ContractBpmLogPO contractBpmLogInit(ContractInfoReqDTO reqDTO, ContractPO contract,SaleContractToOaReqDTO convertedReqDTO) {
        ContractBpmLogPO contractBpmLogPO=new ContractBpmLogPO();
        //只有bpmtask没有值的时候，需要调用接口创建bpm表单
        contractBpmLogPO.setContractId(contract.getContractId());
        contractBpmLogPO.setOriginalMsg(JSONObject.toJSONString(reqDTO.getBpmInfomation()));
        contractBpmLogPO.setConvertedMsg(JSONObject.toJSONString(convertedReqDTO));
        contractBpmLogPO.setCreatedTime(LocalDateTime.now());
        return contractBpmLogPO;
    }

    private Result<String> saveIntentOrderContract(ContractInfoReqDTO reqDTO, IntentOrderPO intentOrderInfo){
        List<SysUserRespDTO> userList = loadUser(reqDTO);

        //合同是草稿状态，不做存储
        if (StringUtils.equals(ContractStatusEnum.CONTRACT_DRAFT.getCode(), reqDTO.getStatus())) {
            return Result.ok();
        }

        ContractPO lastContract = lastContractInit(reqDTO.getContractVersion());

        //意向单数据
//        IntentOrderPO intentOrderInfo = getIntentOrder(reqDTO.getOpportunityId());


        //contract表初始化
        ContractPO contract = contractPOInit(reqDTO, userList, lastContract, intentOrderInfo);

        //contract_business初始化
        ContractBusinessPO businessPO = businessInit(reqDTO, userList, intentOrderInfo, contract);

        //contract_file初始化
        List<ContractFilePO> contractFiles = contractFilesInit(reqDTO, contract);

        //business_item 初始化
        List<ContractBusinessItemPO> items = businessItemInit(reqDTO, contract, intentOrderInfo);
        businessItemSnapshotInit(reqDTO, contract, items, IntentOrderTypeEnum.COMMON_INTENT_ORDER.getCode());
        boolean needConfig = needConfigMultiPower(reqDTO.getContractProductItems(), contract, businessPO, intentOrderInfo);

        contractManager.batchSaveContractInfo(contract, businessPO, contractFiles, items);

        //发送消息通知
        sendContractMessage(reqDTO, intentOrderInfo, lastContract, businessPO, intentOrderInfo.getEnterpriseName());
        if (needConfig) {
//            sendConfigMessage(reqDTO, intentOrderInfo, businessPO, intentOrderInfo.getEnterpriseName());
        }

        return Result.ok();
    }


    private ContractPO lastContractInit(String contractVersion) {
        ContractPO contract = contractMapperService.lambdaQuery().eq(ContractPO::getSfContractNo, contractVersion).one();
        if (ObjectUtils.isEmpty(contract)) {
            contract = new ContractPO();
        }
        return contract;
    }

    private boolean needConfigMultiPower(List<ContractProductItems> productItems, ContractPO contract, ContractBusinessPO businessPO, IntentOrderPO intentOrderInfo) {
        if (businessPO.getIsOpenContract() == 0) {
            return false;
        }
        Set<String> updateIds = productItems.stream().map(ContractProductItems::getPbiProductId).collect(Collectors.toSet());
        Set<String> originIds;
        int originSize;
        List<ContractBusinessItemPO> itemInfos = itemMapperService.listByContractId(contract.getContractId());
        if (CollectionUtils.isEmpty(itemInfos)) {
            // 新增的场景
            // 检查和意向单数据是否匹配
            List<IntentOrderItemPO> itemPOList = intentOrderItemMapperService.lambdaQuery()
                    .eq(IntentOrderItemPO::getIntentOrderNo, intentOrderInfo.getIntentOrderNo())
                    .eq(IntentOrderItemPO::getIsDeleted, DeleteFlagEnum.NOT_DELETE.getCode()).list();
            originIds = itemPOList.stream().map(IntentOrderItemPO::getProductId).collect(Collectors.toSet());
            originSize = itemPOList.size();
        } else {
            originIds = itemInfos.stream().map(ContractBusinessItemPO::getPbiProductId).collect(Collectors.toSet());
            originSize = itemInfos.size();
        }
        return !(productItems.size() == originSize
                && updateIds.stream().filter(id -> !originIds.contains(id)).collect(Collectors.toSet()).isEmpty());
    }


    /**
     * 根据商机id获取意向单数据
     */
    private IntentOrderPO getIntentOrder(String opportunityId) {
        LambdaQueryWrapper<IntentOrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntentOrderPO::getOpportunityId, opportunityId);
        return intentOrderMapper.selectOne(queryWrapper);
    }

    /**
     * 根据合同状态发送对应的消息通知
     */
    private void sendContractMessage(ContractInfoReqDTO reqDTO, IntentOrderPO intentOrderInfo, ContractPO lastContract,
                                     ContractBusinessPO contractBusiness, String enterpriseName) {

        ContractMessageMqDTO messageReq = new ContractMessageMqDTO();
        String curEnterpriseName = StringUtils.isBlank(intentOrderInfo.getCapitalEnterpriseName()) ? enterpriseName : intentOrderInfo.getCapitalEnterpriseName();
        messageReq.setEnterpriseName(curEnterpriseName);
        messageReq.setOperationInternalUserId(contractBusiness.getOperationInternalUserId());
        messageReq.setPotentialSalesVolume(reqDTO.getTotalVolumeMw());
        messageReq.setContractNo(contractBusiness.getContractId());
        messageReq.setExternalUserId(intentOrderInfo.getExternalUserId());
        messageReq.setExternalUserName(intentOrderInfo.getExternalUserName());

        messageReq.setCurrentUserId(reqDTO.getCreatedById());
        String oldContractStatus = ObjectUtils.isEmpty(lastContract) ? "" : lastContract.getContractStatus();
        //合同单签通知生态伙伴
        if (ContractStatusEnum.SINGLE_SIGNED.getCode().equals(reqDTO.getStatus())
                && !ContractStatusEnum.SINGLE_SIGNED.getCode().equals(oldContractStatus)) {
            log.info("send Contract SignedMsgNotice：合同单签通知生态伙伴");
            messageReq.setSituationType(ContractSituationEnum.CONTRACT_SINGLE_SIGNED.getCode());
        }
        //审批未通过发送分销运营
        if (ContractStatusEnum.REJECTED.getCode().equals(reqDTO.getStatus())) {
            log.info("send Contract Rejected MsgNotice：合同审批未通过发送分销运营");
            messageReq.setSituationType(ContractSituationEnum.CONTRACT_UN_APPROVE.getCode());
        }
        mqManager.sendTopic(ContractConstants.CONTRACT_CHANGED, JacksonUtil.bean2Json(messageReq));
        //合同上一个状态为单签&&当前同步的合同状态非单签  完成单签的代办
        if (!ContractStatusEnum.SINGLE_SIGNED.getCode().equals(reqDTO.getStatus())
                && ContractStatusEnum.SINGLE_SIGNED.getCode().equals(oldContractStatus)) {
            TodoListUpdateReqDTO req = TodoListUpdateReqDTO.builder()
                    .bizNo(contractBusiness.getContractId())
                    .bizCode(TodoBizCodeEnum.CONTRACT_SINGLE_SIGNED.getCode())
                    .todoStatus(TodoStatusEnum.DONE.getCode())
                    .doneTime(LocalDateTime.now())
                    .updatedBy(reqDTO.getCreatedById())
                    .updatedName(reqDTO.getCreatedById())
                    .build();
            todoListFeign.updateStatus(req);
        }
    }

    private void sendConfigMessage(ContractInfoReqDTO reqDTO, IntentOrderPO intentOrderInfo,
                                   ContractBusinessPO contractBusiness, String enterpriseName) {

        ContractMessageMqDTO messageReq = new ContractMessageMqDTO();
        String curEnterpriseName = StringUtils.isBlank(intentOrderInfo.getCapitalEnterpriseName()) ? enterpriseName : intentOrderInfo.getCapitalEnterpriseName();
        messageReq.setEnterpriseName(curEnterpriseName);
        messageReq.setOperationInternalUserId(contractBusiness.getOperationInternalUserId());
        messageReq.setSalesInternalUserId(contractBusiness.getSalesInternalUserId());
        messageReq.setSalesInternalUserName(contractBusiness.getSalesInternalUserName());
        messageReq.setContractNo(contractBusiness.getContractId());
        messageReq.setExternalUserId(intentOrderInfo.getExternalUserId());
        messageReq.setExternalUserName(intentOrderInfo.getExternalUserName());

        messageReq.setCurrentUserId(reqDTO.getCreatedById());
        log.info("SF同步合同，待运营配置功率，发送分销运营");
        messageReq.setSituationType(ContractSituationEnum.CONTRACT_CONFIG_MULTI_POWER.getCode());
        mqManager.sendTopic(ContractConstants.CONTRACT_CHANGED, JacksonUtil.bean2Json(messageReq));
    }


    /**
     *
     */
    private List<SysUserRespDTO> loadUser(ContractInfoReqDTO reqDTO) {
        SysUserQueryDTO sysUserQueryDTO = new SysUserQueryDTO();
        List<String> sfIdList = new ArrayList<>();
        sfIdList.add(reqDTO.getCreatedById());
        sfIdList.add(reqDTO.getSalesInternalUserId());
        sfIdList.add(reqDTO.getOperationInternalUserId());
        sysUserQueryDTO.setSfIdList(sfIdList);
        Result<List<SysUserRespDTO>> result = sysUserFeign.listUser(sysUserQueryDTO);

        result.getData().forEach(userInfo -> {
            if (reqDTO.getCreatedById().equals(userInfo.getSfId())) {
                reqDTO.setCreatedById(userInfo.getUserId());
            }
        });

        return result.getData();
    }


    /**
     * 合同主键id生成
     */
    private String trinaContractIdInit(ContractInfoReqDTO reqDTO, ContractPO lastContract) {
        String trinaContractId = "";
        //查询contract表是否存在
        if (StringUtils.isBlank(lastContract.getContractId())) {
            trinaContractId = idGenerator.getUniqueId(UtilsConstants.ID_GENERATOR_CONTRACT);
        } else {
            trinaContractId = lastContract.getContractId();
            if (StringUtils.equals(reqDTO.getStatus(), ContractStatusEnum.SINGLE_SIGNED.getCode()) &&
                    StringUtils.equals(ContractStatusEnum.COUNTERSIGNED.getCode(), lastContract.getContractStatus())) {
                throw new BizException(ResultCode.FAIL.getCode(), "合同已经双签，单签同步失败");
            }
        }
        return trinaContractId;
    }

    /**
     * 更新contract_business_item_snapshot
     */
    private void businessItemSnapshotInit(ContractInfoReqDTO reqDTO, ContractPO contract, List<ContractBusinessItemPO> items, int orderType) {
        List<ContractProductItems> productItems = reqDTO.getContractProductItems();
        if (orderType == IntentOrderTypeEnum.COMMON_INTENT_ORDER.getCode()) {
            List<ContractBusinessItemPO> itemInfos = itemMapperService.listByContractId(contract.getContractId());
            if (CollectionUtils.isEmpty(itemInfos) && CollectionUtils.isNotEmpty(productItems)) {
                // 新增的场景，增加快照表
                List<ContractBusinessItemSnapshotPO> toSave = BeanUtil.copyToList(items, ContractBusinessItemSnapshotPO.class);
                contractBusinessItemSnapshotMapperService.saveBatch(toSave);
            }
        } else if (orderType == IntentOrderTypeEnum.QUICK_ORDER.getCode()) {
            // 快速订单，没有快照则插入快照
            List<ContractBusinessItemSnapshotPO> datas = contractBusinessItemSnapshotMapperService.listByContractId(contract.getContractId());
            if (CollectionUtils.isEmpty(datas)) {
                List<ContractBusinessItemSnapshotPO> toSave = BeanUtil.copyToList(items, ContractBusinessItemSnapshotPO.class);
                contractBusinessItemSnapshotMapperService.saveBatch(toSave);
            }
        }

    }

    /**
     * 更新contract_business_item
     * 先删后增
     */
    private List<ContractBusinessItemPO> businessItemInit(ContractInfoReqDTO reqDTO, ContractPO contract, IntentOrderPO intentOrderInfo) {
        List<ContractProductItems> productItems = reqDTO.getContractProductItems();
        List<ContractBusinessItemPO> itemInfos = itemMapperService.listByContractId(contract.getContractId());
        log.error("ceshiceshi 功率起止ss + 查询结果：" + JSON.toJSONString(itemInfos));
        List<ContractBusinessItemPO> items = new ArrayList<>();

        if (!productItems.isEmpty()) {
            for (ContractProductItems item : productItems) {
                ContractBusinessItemPO businessItemPO = new ContractBusinessItemPO();
                itemInit(item, reqDTO, businessItemPO, itemInfos, contract, intentOrderInfo);
                items.add(businessItemPO);
            }
        }
        return items;
    }

    /**
     * contract_business_item加载
     */
    private void itemInit(ContractProductItems item, ContractInfoReqDTO reqDTO, ContractBusinessItemPO businessItemPO,
                          List<ContractBusinessItemPO> itemInfos, ContractPO contract, IntentOrderPO intentOrderInfo) {

        if (itemInfos == null || itemInfos.isEmpty()) {
            businessItemPO.setContractBusinessItemId(idGenerator.getUniqueId(UtilsConstants.ID_GENERATOR_ITEM));
            List<IntentOrderItemPO> itemPOList = intentOrderItemMapperService.lambdaQuery()
                    .eq(IntentOrderItemPO::getIntentOrderNo, intentOrderInfo.getIntentOrderNo())
                    .eq(IntentOrderItemPO::getProductId, item.getPbiProductId())
                    .eq(IntentOrderItemPO::getIsDeleted, DeleteFlagEnum.NOT_DELETE.getCode()).list();
            if (CollectionUtils.isNotEmpty(itemPOList)) {
                IntentOrderItemPO intentPo = itemPOList.get(0);
                if (StringUtils.isNotBlank(intentPo.getMultiPowerBegin()) || StringUtils.isNotBlank(intentPo.getMultiPowerEnd())) {
                    businessItemPO.setMultiPowerBegin(intentPo.getMultiPowerBegin());
                    businessItemPO.setMultiPowerEnd(intentPo.getMultiPowerEnd());
                }
            }
        } else {
            for (ContractBusinessItemPO itemInfo : itemInfos) {
                if (StringUtils.equals(item.getSfContractItemId(), itemInfo.getSfContractItemId())) {
                    businessItemPO.setContractBusinessItemId(itemInfo.getContractBusinessItemId());
                    businessItemPO.setMultiPowerBegin(itemInfo.getMultiPowerBegin());
                    businessItemPO.setMultiPowerEnd(itemInfo.getMultiPowerEnd());
//                    businessItemPO.setNotes(itemInfo.getNotes());
                    businessItemPO.setId(itemInfo.getId());
                    log.error("ceshiceshi 回调功率起止1：" + itemInfo.getMultiPowerBegin());
                    break;
                }else if(StringUtils.equals(item.getPbiProductId(), itemInfo.getPbiProductId())
                        && ObjectUtils.compare(item.getPower(), itemInfo.getPower())==0
                        && ObjectUtils.compare(item.getQuantityQ(),itemInfo.getQuantityP())==0){
                    //渠道订单只能通过产品id和片数匹配唯一行
//                    businessItemPO.setContractBusinessItemId(itemInfo.getContractBusinessItemId());
                    businessItemPO.setMultiPowerBegin(itemInfo.getMultiPowerBegin());
                    businessItemPO.setMultiPowerEnd(itemInfo.getMultiPowerEnd());
//                    businessItemPO.setNotes(itemInfo.getNotes());
                    businessItemPO.setId(itemInfo.getId());
                    log.error("ceshiceshi 回调功率起止2：" + itemInfo.getMultiPowerBegin());
                    break;
                }
            }
            if (StringUtils.isBlank(businessItemPO.getContractBusinessItemId())) {
                businessItemPO.setContractBusinessItemId(idGenerator.getUniqueId(UtilsConstants.ID_GENERATOR_ITEM));
            }
        }
        businessItemPO.setIsDeleted(Long.parseLong(DataSoftStatusEnum.DELETE.getCode()));
        businessItemPO.setContractId(contract.getContractId());
        businessItemPO.setCurrency(reqDTO.getCurrency());
        businessItemPO.setSfOpportunityItemId(item.getSfOpportunityItemId());
        businessItemPO.setPlugConnector(item.getPlugConnector());
        businessItemPO.setInstallation(item.getInstallation());
        businessItemPO.setCableLength(item.getCableLength());
        businessItemPO.setCableLengthPositive(item.getCableLengthPositive());
        businessItemPO.setCableLengthCathode(item.getCableLengthCathode());
        businessItemPO.setPower(item.getPower());
        if (ObjectUtils.isEmpty(item.getExpectedDeliveryDate())) {
            throw new BizException(ResultCode.FAIL.getCode(), "expectedDeliveryDate must not be null");
        }
        businessItemPO.setExpectedDeliverDate(LocalDateTime.of(item.getExpectedDeliveryDate(), LocalTime.MIN));
        businessItemPO.setQuantityP(item.getQuantityQ());
        businessItemPO.setQuantityMw(item.getQuantityMW());
        businessItemPO.setQuantityW(item.getQuantityMW() == null ?
                null : item.getQuantityMW().divide(new BigDecimal(1000000)));
        businessItemPO.setNetPrice(item.getNetPrice());
        businessItemPO.setTotalAdditionalCost(item.getTotalAdditionalCost());
        businessItemPO.setUnitPriceW(item.getUnitPrice());
        businessItemPO.setUnitPriceP(item.getUnitPricePieces());
        businessItemPO.setNotes(item.getNotes());
        businessItemPO.setDestinationPortId(item.getDestinationPortId());
        businessItemPO.setLoadingPortId(item.getLoadingPortId());
//        businessItemPO.setRemainingQuantityP(item.getRemainingQuantity()); // 暂时不用这个字段，每次查询都从SF查询
        businessItemPO.setTotalAmount(item.getTotalAmount());
        businessItemPO.setProductName(item.getProductName());
        businessItemPO.setProductCategory(item.getProductCatgory());
        businessItemPO.setPriceBook(item.getPriceBookEntryId());
        businessItemPO.setEfc(item.getEfc());

        businessItemPO.setModuleType(item.getModuleType());
        businessItemPO.setItemType(item.getItemType());
        businessItemPO.setCreatedBy(contract.getCreatedBy());
        businessItemPO.setCreatedName(contract.getCreatedName());

        businessItemPO.setCreatedTime(reqDTO.getCreatedDate());
        businessItemPO.setUpdatedBy(contract.getUpdatedBy());
        businessItemPO.setUpdatedName(contract.getUpdatedName());

        businessItemPO.setUpdatedTime(reqDTO.getLastModifiedDate());
        businessItemPO.setSfProductId(item.getSfProductId());
        businessItemPO.setPbiProductId(item.getPbiProductId());
        businessItemPO.setSfParentItemId(item.getSfParentItemId());
        businessItemPO.setInputType(item.getInputType());
        businessItemPO.setSfParentItemId(item.getSfParentItemId());

        //兼容料号 sf 传空的场景
        if (StringUtils.isBlank(item.getPbiProductId())
                && StringUtils.equals(ModuleTypeEnum.PART.getCode(), item.getModuleType())) {
            businessItemPO.setPbiProductId(item.getProductName());
        }
        businessItemPO.setSfContractItemId(item.getSfContractItemId());
    }

    /**
     * 更新contract_file
     * 先删后增
     */
    private List<ContractFilePO> contractFilesInit(ContractInfoReqDTO reqDTO, ContractPO contract) {
        List<ContractFileItems> contractFileItems = reqDTO.getContractFileItems();
        List<ContractFilePO> contractFiles = new ArrayList<>();

        if (!contractFileItems.isEmpty()) {
            for (ContractFileItems fileItem : contractFileItems) {
                ContractFilePO contractFile = new ContractFilePO();
                contractFileInit(fileItem, reqDTO, contractFile, contract);
                contractFiles.add(contractFile);
            }
        }
        return contractFiles;
    }

    /**
     * contract_file加载
     */
    private void contractFileInit(ContractFileItems fileItem, ContractInfoReqDTO reqDTO, ContractFilePO contractFile, ContractPO contract) {
        contractFile.setContractId(contract.getContractId());
        contractFile.setFileName(fileItem.getFile_name());
        contractFile.setFileUrl(fileItem.getFile_url());
        contractFile.setFileSource(StringUtils.isBlank(fileItem.getFile_source()) ? "SF" : fileItem.getFile_source());
        //默认未删除
        contractFile.setIsDeleted(Long.getLong("0"));
        contractFile.setCreatedBy(contract.getCreatedBy());
        contractFile.setCreatedName(contract.getCreatedName());
        contractFile.setCreatedTime(reqDTO.getCreatedDate());
        contractFile.setUpdatedTime(LocalDateTime.now());
        contractFile.setUpdatedBy(contract.getUpdatedBy());
        contractFile.setCreatedName(contract.getCreatedName());

    }

    /**
     * 参数映射转换-contract_business
     */
    private ContractBusinessPO businessInit(ContractInfoReqDTO reqDTO, List<SysUserRespDTO> userList,
                                            IntentOrderPO intentOrderInfo, ContractPO contract) {
        ContractBusinessPO businessPO = new ContractBusinessPO();
        businessPO.setContractId(contract.getContractId());

        //防止数据更新时刷掉 drSfId
        ContractBusinessPO oldBusinessPO = cBusMapperService.findByContractId(contract.getContractId());
        if (ObjectUtils.isNotEmpty(oldBusinessPO)){
            businessPO.setDrSfId(oldBusinessPO.getDrSfId());
        }

        SysUserRespDTO curUser = userList.stream().filter(a -> a.getSfId().equals(reqDTO.getOperationInternalUserId())).findFirst().orElse(new SysUserRespDTO());

        if (StringUtils.isBlank(curUser.getUserId())) {
            throw new BizException(ResultCode.FAIL.getCode(), "OperationInternalUserId 异常");
        }

        businessPO.setOperationInternalUserName(intentOrderInfo.getOperationInternalUserName());
        businessPO.setOperationInternalUserId(intentOrderInfo.getOperationInternalUserId());
        businessPO.setOriginOperationInternalUserId(intentOrderInfo.getOriginOperationInternalUserId());

        businessPO.setSalesInternalUserId(intentOrderInfo.getSalesInternalUserId());
        businessPO.setSalesInternalUserName(intentOrderInfo.getSalesInternalUserName());
        businessPO.setOriginSalesInternalUserId(intentOrderInfo.getOriginSalesInternalUserId());

        businessPO.setExternalUserId(intentOrderInfo.getExternalUserId());
        businessPO.setExternalUserName(intentOrderInfo.getExternalUserName());
        businessPO.setOriginExternalUserId(intentOrderInfo.getOriginExternalUserId());


        businessPO.setMarketingAccountId(reqDTO.getErpOperatingUnit());
        businessPO.setTaxClassificationId(reqDTO.getErpTaxClassificationId());
        businessPO.setTax(reqDTO.getTax());
        businessPO.setIsLinkFrameContract(reqDTO.getIsFromFrameContract());
        businessPO.setFrameContractNo(reqDTO.getFrameContractNo());
        businessPO.setArrivalCityCode(reqDTO.getArea());
        businessPO.setArrivalCountryCode(reqDTO.getGoodsArrivalRegion());
        businessPO.setArrivalProvinceCode(reqDTO.getGoodsArrivalSubRegion());
        businessPO.setIncoterm(reqDTO.getIncoterm());
        businessPO.setOdm(reqDTO.getOdm());

        businessPO.setIsOpenContract(0);
        if (StringUtils.isNotBlank(reqDTO.getOpenContract()) && reqDTO.getOpenContract().contains("PCs & Power")) {
            businessPO.setIsOpenContract(1);
        }
        businessPO.setOpenContract(reqDTO.getOpenContract());
        businessPO.setSfOpportunityId(reqDTO.getOpportunityId());

        List<EnterpriseBizRelationResDTO> enterpriseBizRelationDTO = enterpriseFeign.getEnterpriseBizRelationByEnterpriseId(contract.getEnterpriseId()).getData();
        businessPO.setBizOrganizationCode(enterpriseBizRelationDTO.stream().findFirst()
                .orElse(new EnterpriseBizRelationResDTO()).getBizOrganizationCode());
        if (StringUtils.isBlank(businessPO.getBizOrganizationCode())) {
            throw new BizException(ResultCode.FAIL.getCode(), "accountId 异常");
        }
        businessPO.setPaymentNotes(textDeal(reqDTO.getPaymentNotes()));
        businessPO.setPaymentTerms(reqDTO.getPaymentTerms());
        businessPO.setContractTermsNotes(textDeal(reqDTO.getTermsRemark()));
        businessPO.setTotalAmountRmb(reqDTO.getTotalAmountRmb());
        businessPO.setTotalAmountUsd(reqDTO.getTotalAmountUsd());
        businessPO.setPriceBookId(reqDTO.getPriceBookId());
        businessPO.setSalesRegionCode(reqDTO.getSalesRegionCode());

        SysUserRespDTO curUsers = userList.stream().filter(a -> a.getSfId().equals(reqDTO.getSalesInternalUserId()))
                .findFirst().orElse(new SysUserRespDTO());
        if (StringUtils.isBlank(curUsers.getUserId())) {
            throw new BizException(ResultCode.FAIL.getCode(), "销售SalesInternalUserId 异常");
        }

        businessPO.setSalesInternalUserNo(curUsers.getUserCode());

        businessPO.setRequiredDeliveryDate(reqDTO.getRequiredDeliveryDate());
        //scheduledDeliveryDate计划交货日期 为空取 requiredDeliveryDate
        businessPO.setScheduledDeliveryDate(reqDTO.getScheduledDeliveryDate() == null ? reqDTO.getRequiredDeliveryDate() : reqDTO.getScheduledDeliveryDate());
        businessPO.setShippingNotes(textDeal(reqDTO.getShippingNotes()));
        businessPO.setBillToAddressId(reqDTO.getBillToAddressId());
        businessPO.setSubRegionCode(reqDTO.getSalesSubRegion());
        businessPO.setTotalVolumeMw(reqDTO.getTotalVolumeMw());
        businessPO.setIndustryLevelOne(reqDTO.getIndustryLevelOne());
        businessPO.setIndustryLevelTwo(reqDTO.getIndustryLevelTwo());
        businessPO.setApplication(reqDTO.getApplication());
        businessPO.setCurrency(reqDTO.getCurrency());
        businessPO.setBeneficiaryBankId(reqDTO.getBeneficiaryBankId());
        businessPO.setBillToAddressId(reqDTO.getBillToAddressId());
        businessPO.setCountryOfInstallation(reqDTO.getCountryOfInstallation());
        businessPO.setDestinationPortId(reqDTO.getDestinationPortId());
        businessPO.setLoadingPortId(reqDTO.getLoadingPortId());
        businessPO.setChannel(reqDTO.getChannel());
        businessPO.setCreatedTime(reqDTO.getCreatedDate());
        businessPO.setCreatedBy(contract.getCreatedBy());
        businessPO.setCreatedName(contract.getCreatedName());

        businessPO.setUpdatedTime(reqDTO.getLastModifiedDate());
        businessPO.setUpdatedBy(contract.getUpdatedBy());
        businessPO.setUpdatedName(contract.getUpdatedName());

        businessPO.setIntentOrderNo(intentOrderInfo.getIntentOrderNo());

        businessPO.setRebate(reqDTO.getRebate() != null && reqDTO.getRebate());

        return businessPO;
    }

    /**
     * 文本处理，截掉SF 的<p></p>标签
     */
    private String textDeal(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }
        if (text.charAt(0) != '<') {
            return text;
        }
        if (text.contains("<p>") && text.contains("</p>")) {
            text = text.replace("<p>", "").replace("</p>", "");
        }
        return text;

    }

    /**
     * 参数映射转换-contract
     */
    private ContractPO contractPOInit(ContractInfoReqDTO reqDTO, List<SysUserRespDTO> userList, ContractPO lastContract, IntentOrderPO intentOrderInfo) {
        ContractPO contractPO = new ContractPO();
        contractPO.setSfNextContractNo(getNextContractNo(reqDTO.getContractVersion()));

        if(StringUtils.isBlank(lastContract.getCreatedBy())){
            //渠道订单创建人需要在后续使用
            contractPO.setCreatedBy(reqDTO.getCreatedById());
        }

        SysUserRespDTO createUser = userList.stream().filter(a -> reqDTO.getCreatedById().equals(a.getUserId())).findFirst().orElse(new SysUserRespDTO());
        contractPO.setCreatedName(createUser.getUserName());
        contractPO.setContractId(trinaContractIdInit(reqDTO, lastContract));
        contractPO.setCreatedTime(reqDTO.getCreatedDate());
        contractPO.setUpdatedTime(reqDTO.getLastModifiedDate());

//        if (StringUtils.isBlank(createUser.getUserId())) {
//            throw new BizException(ResultCode.FAIL.getCode(), "CreatedById 异常");
//        }
        contractPO.setUpdatedBy(reqDTO.getLastModifiedById());
        contractPO.setUpdatedName(reqDTO.getLastModifiedByName());
        contractPO.setSfContractId(reqDTO.getContractId());
        contractPO.setSfContractFamilyNo(reqDTO.getContractNo());
        contractPO.setSfContractNo(reqDTO.getContractVersion());
        contractPO.setSfLastContractNo(getLastContract(reqDTO.getOriginContract()));
        contractPO.setSfRecordId(reqDTO.getRecordId());
        contractPO.setSfRecordType(reqDTO.getRecordType());
        contractPO.setSfEnterpriseId(reqDTO.getAccountId());


        contractPO.setEffectStart(reqDTO.getEffectStart() == null ? null : LocalDateTime.of(reqDTO.getEffectStart(), LocalTime.MIN));
        contractPO.setEffectEnd(reqDTO.getEffectEnd() == null ? null : LocalDateTime.of(reqDTO.getEffectEnd(), LocalTime.MIN));
        contractPO.setContractStatus(reqDTO.getStatus());
        if (StringUtils.equals(ContractStatusEnum.APPROVED.getCode(), reqDTO.getStatus())) {
            contractPO.setApprovedTime(LocalDateTime.now());
        }
        if(StringUtils.isNotBlank(reqDTO.getApprovedTaskId())){
            contractPO.setBpmTaskId(reqDTO.getApprovedTaskId());
        }
        contractPO.setComments(reqDTO.getComments());
        contractPO.setContractSource(reqDTO.getContractSource());
        contractPO.setSummary(reqDTO.getContractSummary());
        contractPO.setContractType(reqDTO.getContractType());

        if (null == reqDTO.getCounterSignedDate()) {
            contractPO.setCounterSignedDate(null);
        } else {
            contractPO.setCounterSignedDate(LocalDateTime.of(reqDTO.getCounterSignedDate(), LocalTime.MIN));
        }
        contractPO.setDiscardReason(reqDTO.getDiscardReason());
        contractPO.setIsOnlineContract(onLineContract(reqDTO, lastContract));

        String statusBeforeChange = reqDTO.getStatusBeforeChange();

        String oldContractStatus = lastContract.getContractStatus();

        //合同前一个状态和 sf 不一致时，且合同当前状态为单签或者双签时，取contract.contract_status
        boolean loadFromSF = StringUtils.equals(oldContractStatus, reqDTO.getStatusBeforeChange());
        boolean isSigned = ContractStatusEnum.SINGLE_SIGNED.getCode().equals(oldContractStatus)
                || ContractStatusEnum.COUNTERSIGNED.getCode().equals(oldContractStatus);
        if (!loadFromSF && isSigned) {
            statusBeforeChange = oldContractStatus;
        }

        contractPO.setStatusBeforeChange(statusBeforeChange);

        //有文件则是签署过
        contractPO.setIsSigned(reqDTO.getContractFileItems().isEmpty() ? ContractConstants.NO_SIGN : ContractConstants.SIGNED);
        contractPO.setEnterpriseContactName(reqDTO.getContactName());

        //根据sfID 获取映射的用户信息
        SysUserQueryDTO sysUserQueryDTO = new SysUserQueryDTO();
        sysUserQueryDTO.setSfId(contractPO.getSfEnterpriseId());
        contractPO.setEnterpriseId(intentOrderInfo.getEnterpriseId());
        contractPO.setEnterpriseName(intentOrderInfo.getEnterpriseName());
        contractPO.setCustomerCategory(intentOrderInfo.getCustomerCategory());
        contractPO.setCapitalEnterpriseId(intentOrderInfo.getCapitalEnterpriseId());
        contractPO.setCapitalEnterpriseName(intentOrderInfo.getCapitalEnterpriseName());
        return contractPO;
    }

    /**
     * 获取上一版合同号
     */
    private String getLastContract(String originRecordId) {
        if (StringUtils.isBlank(originRecordId)) {
            return null;
        }
        ContractPO contractPO = contractMapperService.lambdaQuery().eq(ContractPO::getSfRecordId, originRecordId).one();
        if (contractPO == null) {
            return null;
        }
        return contractPO.getSfContractNo();
    }

    /**
     * @return 1:线上签约 0:线下签约
     */
    private Integer onLineContract(ContractInfoReqDTO reqDTO, ContractPO lastContract) {
        lastContract = ObjectUtils.isEmpty(lastContract) ? new ContractPO() : lastContract;
        String curStatus = lastContract.getContractStatus();
        if (StringUtils.equals(ContractStatusEnum.SINGLE_SIGNED.getCode(), reqDTO.getStatus())) {
            //单签
            if (StringUtils.equals(ContractStatusEnum.SINGLE_SIGNED.getCode(), curStatus)) {
                return lastContract.getIsOnlineContract();
            } else {
                return OFFLINE_CONTRACT;
            }
        }

        //双签
        if (StringUtils.equals(ContractStatusEnum.COUNTERSIGNED.getCode(), reqDTO.getStatus())) {
            if(CollectionUtils.isEmpty(reqDTO.getContractFileItems())){
                return lastContract.getIsOnlineContract();
            }else{
                //基于合同附件的类型来判断，线上签署的都是契约锁的合同地址
                ContractFileItems contractFileItems= reqDTO.getContractFileItems().get(0);
                if(contractFileItems.getFile_url().startsWith(esignHost)){
                    return ONLINE_CONTRACT;
                }else{
                    return OFFLINE_CONTRACT;
                }
            }
//            if (StringUtils.equals(ContractStatusEnum.COUNTERSIGNED.getCode(), curStatus)) {
//                return lastContract.getIsOnlineContract();
//            } else {
//                return OFFLINE_CONTRACT;
//            }
        }
        return lastContract.getIsOnlineContract();
    }

    /**
     * 根据当前SF合同编码获取子合同编码
     */
    private String getNextContractNo(String contractVersion) {
        ContractPO contract = contractMapperService.lambdaQuery().eq(ContractPO::getSfLastContractNo, contractVersion).one();
        return contract == null ? null : contract.getSfContractNo();
    }

    public void addContractDraft(QuickOrderContractSubmitReqDTO reqDTO, SfSyncContractDTO sfSyncContractDTO) {
        reqDTO.setContractNo(sfSyncContractDTO.getContractNo());
        ContractPO contractPO = new ContractPO();
        Result<SysUserRespDTO> userInfo = sysUserFeign.getUserByUserId(reqDTO.getCurrentUserId());
        contractPO.setCreatedBy(reqDTO.getCurrentUserId());
        contractPO.setCreatedName(userInfo.getData().getUserName());
        contractPO.setUpdatedBy(reqDTO.getCurrentUserId());
        contractPO.setUpdatedName(userInfo.getData().getUserName());
        contractPO.setCreatedTime(LocalDateTime.now());
        contractPO.setUpdatedTime(LocalDateTime.now());
        String contractId = idGenerator.getUniqueId(UtilsConstants.ID_GENERATOR_CONTRACT);
        contractPO.setContractId(contractId);
        contractPO.setSfContractNo(sfSyncContractDTO.getContractNo());
        contractPO.setSfRecordId(sfSyncContractDTO.getRecordId());
        contractMapperService.save(contractPO);

        ContractBusinessPO businessPO = new ContractBusinessPO();
        businessPO.setContractId(contractId);
        businessPO.setRebateMoney(reqDTO.getRebateMoney());
        businessPO.setIsPrePayment(reqDTO.getIsPrePayment() ? 1 : 0);
        businessPO.setContractComment(reqDTO.getContractComment());
        businessPO.setEnterpriseType(reqDTO.getEnterpriseType());
        businessPO.setCreatedBy(reqDTO.getCurrentUserId());
        businessPO.setCreatedName(userInfo.getData().getUserName());
        businessPO.setUpdatedBy(reqDTO.getCurrentUserId());
        businessPO.setUpdatedName(userInfo.getData().getUserName());
        businessPO.setCreatedTime(LocalDateTime.now());
        businessPO.setUpdatedTime(LocalDateTime.now());
        businessPO.setPrePayDateGap(reqDTO.getPrePayDateGap());
        businessPO.setPrePayPercent(reqDTO.getPrePayPercent());
        businessPO.setPayDateGap(reqDTO.getPayDateGap());
        businessPO.setDeadlineDate(reqDTO.getDeadlineDate());
        businessPO.setDeliveryDate(reqDTO.getDeliveryDate());
        businessPO.setHandleDateGap(reqDTO.getHandleDateGap());
        businessPO.setIntentOrderNo(reqDTO.getIntentOrderNo());
        cBusMapperService.save(businessPO);

        List<ContractBusinessItemPO> toSave = Lists.newArrayList();
        for (IntentOrderItemReqDTO dto : reqDTO.getItemReqDTOList()) {
            ContractBusinessItemPO itemPO = new ContractBusinessItemPO();
            itemPO.setContractId(contractId);
            itemPO.setPbiProductId(dto.getProductId());
            itemPO.setQuantityP(dto.getQuantityP());
            itemPO.setContractBusinessItemId(idGenerator.getUniqueId(UtilsConstants.ID_GENERATOR_ITEM));
            itemPO.setSfOpportunityItemId(dto.getOpportunityItemId());
            if (NumberUtils.isCreatable(dto.getPower())) {
                itemPO.setPower(new BigDecimal(dto.getPower()));
            }
            itemPO.setMultiPowerBegin(dto.getMultiPowerBegin());
            itemPO.setMultiPowerEnd(dto.getMultiPowerEnd());
//            itemPO.setNotes(generateNotes(dto));
            itemPO.setCreatedBy(reqDTO.getCurrentUserId());
            itemPO.setCreatedName(userInfo.getData().getUserName());
            itemPO.setUpdatedBy(reqDTO.getCurrentUserId());
            itemPO.setUpdatedName(userInfo.getData().getUserName());
            itemPO.setCreatedTime(LocalDateTime.now());
            itemPO.setUpdatedTime(LocalDateTime.now());
            toSave.add(itemPO);
        }
        if (CollectionUtils.isNotEmpty(toSave)) {
            itemMapperService.saveBatch(toSave);
            log.error("ceshiceshi 功率起止：" + toSave.get(0).getMultiPowerBegin());
        }
        List<ContractBusinessItemPO> pos = itemMapperService.listByContractId(contractId);
        log.error("ceshiceshi 功率起止 + 保存结果：" + JSON.toJSONString(pos));

        ContractFilePO contractFile = new ContractFilePO();
        contractFile.setContractId(contractId);
        IntentOrderPO intentOrderPO= intentOrderMapperService.lambdaQuery().eq(IntentOrderPO::getIntentOrderNo, reqDTO.getIntentOrderNo()).one();
        String companyName=StringUtils.isNotBlank(intentOrderPO.getCapitalEnterpriseName())?intentOrderPO.getCapitalEnterpriseName():intentOrderPO.getEnterpriseName();
        contractFile.setFileName(companyName+"_"+contractPO.getShortContractNo()+".pdf");
        QuickOrderContractPreviewResDTO quickOrderContractPreviewResDTO=quickOrderContractBiz.genContract(reqDTO);
        contractFile.setFileUrl(quickOrderContractPreviewResDTO.getContractDocUrl());
        contractFile.setFileSource("ZZB");
        //默认未删除
        contractFile.setIsDeleted(Long.getLong("0"));
        contractFile.setCreatedBy(reqDTO.getCurrentUserId());
        contractFile.setCreatedName(userInfo.getData().getUserName());
        contractFile.setCreatedTime(LocalDateTime.now());
        contractFile.setUpdatedTime(LocalDateTime.now());
        contractFile.setUpdatedBy(reqDTO.getCurrentUserId());
        contractFile.setCreatedName(userInfo.getData().getUserName());

        contractFileMapperService.save(contractFile);
        LambdaUpdateWrapper<IntentOrderPO> updateWrapper = new LambdaUpdateWrapper<IntentOrderPO>()
                .eq(IntentOrderPO::getIntentOrderNo, businessPO.getIntentOrderNo()).eq(IntentOrderPO::getIsDeleted, 0)
                .set(IntentOrderPO::getStatus, IntentOrderStatusEnum.HAS_CONTRACT.getCode());
        intentOrderMapperService.update(updateWrapper);
    }

    private String generateNotes(IntentOrderItemReqDTO dto) {
        StringBuilder notesBuilder=new StringBuilder();
        if(dto.getItemType().equals(ItemTypeEnum.COMPLIMENTARY.getCode())){
            notesBuilder.append("赠品");
        }
        if(dto.getMultiPowerBegin()!=null&&dto.getMultiPowerEnd()!=null){
            notesBuilder.append("功率范围:"+dto.getMultiPowerBegin()+"-"+dto.getMultiPowerEnd());
        }
        return notesBuilder.toString();
    }
}
