package com.trinasolar.trinax.requireorder.service.biz;

import cn.hutool.core.collection.CollUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.dto.input.EnterpriseQueryDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseDTO;
import com.trinasolar.trinax.requireorder.constants.enums.RequireOrderUserRoleEnum;
import com.trinasolar.trinax.requireorder.dto.input.LoginUserRoleReqDTO;
import com.trinasolar.trinax.requireorder.repository.atomicservice.RequireOrderMapperService;
import com.trinasolar.trinax.requireorder.repository.po.RequireOrderPO;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.UserConstant;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class LoginUserRoleBiz {

    @Autowired
    private RequireOrderMapperService requireOrderMapperService;

    @Autowired
    private SysUserFeign sysUserFeign;

    @Autowired
    private EnterpriseFeign enterpriseFeign;


    public String loginUserRole(LoginUserRoleReqDTO reqDTO) {
        //1、如果当前用户有区域总角色时，
        Result<SysUserRespDTO> result = sysUserFeign.getUserByUserId(reqDTO.getCurrentUserId());
        if(Boolean.FALSE.equals(result.getSuccess()) || ObjectUtils.isEmpty(result.getData())){
            throw new BizException(ResultCode.FAIL.getCode(),"获取当前用户信息失败");
        }
        if(CollUtil.isNotEmpty(result.getData().getRoleIds())){
            if(result.getData().getRoleIds().contains(UserConstant.SALES_AREA_MANAGER_ROLE_ID)){
                return RequireOrderUserRoleEnum.AREA_MANAGER.getCode();
            }
        }

        //2、当前用户非区域总角色时，如果绑定了企业，则判断当前用户是否企业主销售
        RequireOrderPO requireOrderPO = requireOrderMapperService.getByRequireOrderNo(reqDTO.getRequireOrderNo());
        if(StringUtils.isNotBlank(requireOrderPO.getEnterpriseId())){
            Result<List<EnterpriseDTO>> enterpriseResult = enterpriseFeign.listByQuery(EnterpriseQueryDTO.builder().enterpriseId(requireOrderPO.getEnterpriseId()).build());
            if (Boolean.FALSE.equals(enterpriseResult.getSuccess()) || CollUtil.isEmpty(enterpriseResult.getData())) {
                throw new BizException(ResultCode.FAIL.getCode(), "获取需求单对应企业信息异常");
            }
            String mainSaleId = enterpriseResult.getData().get(0).getMainSalesUserId();
            if(reqDTO.getCurrentUserId().equals(mainSaleId)){
                return RequireOrderUserRoleEnum.MAIN_SALE.getCode();
            }
        }

        //3、既不是区域总，又不是企业主销售时，判断当前用户是否时需求单对应的“销售用户”
        if(requireOrderPO.getSalesInternalUserId().equals(reqDTO.getCurrentUserId())){
            return RequireOrderUserRoleEnum.SALE.getCode();
        }
        return "";
    }
}
