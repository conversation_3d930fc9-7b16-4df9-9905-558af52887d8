package com.trinasolar.trinax.requireorder.service.biz.message;

import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.messagetemplate.MessageTemplateNoticeTypeEnum;
import com.trinasolar.trinax.basic.dto.input.MessageSendCommonReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendNoticeReqDTO;
import com.trinasolar.trinax.basic.event.MessageBizCodeEnum;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.requireorder.dto.mq.RequireOrderMessageMqDTO;
import com.trinasolar.trinax.requireorder.manager.RequireMessageFillManager;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class ConfirmToIntentOrderBiz {

    @Autowired
    private MqManager mqManager;

    @Autowired
    private RequireMessageFillManager requireMessageFillManager;

    @Value("${message.template.rq.confirmToIntentOrderNotice}")
    private String noticeTemplateCode;


    public void execute(RequireOrderMessageMqDTO reqDTO) {
        log.info("确认转意向单,给外部用户发系统通知消息入参：{}", reqDTO);
        MessageSendCommonReqDTO noticeMessageReqDTO = new MessageSendCommonReqDTO();
        noticeMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.NOTICE.getCode());

        List<MessageSendNoticeReqDTO> noticeList = new ArrayList<>();

        Map<String,String> content = new HashMap<>();
        content.put("internalUserName",reqDTO.getSaleInternalUserName());
        content.put("requireOrderNo",reqDTO.getRequireOrderNo());
        content.put("intentOrderNo",reqDTO.getIntentOrderNo());

        MessageSendNoticeReqDTO noticeReqDTO = requireMessageFillManager.generateNotice(reqDTO,
                "", MessageBizCodeEnum.RQ_CONFIRM_TO_INTENT_ORDER.getValue(), noticeTemplateCode,
                Collections.singletonList(reqDTO.getExternalUserId()),content);

        noticeList.add(noticeReqDTO);
        noticeMessageReqDTO.setNoticeList(noticeList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC,JacksonUtil.bean2Json(noticeMessageReqDTO));
        log.info("确认转意向单,给外部用户发系统通知消息完成，param:{}", JacksonUtil.bean2Json(noticeMessageReqDTO));
    }


}
