package com.trinasolar.trinax.intentorder.manager;

import com.trinasolar.trinax.basic.api.TodoListFeign;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoStatusEnum;
import com.trinasolar.trinax.basic.dto.input.TodoListUpdateReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class CompleteTodoManager {
    @Autowired
    private TodoListFeign todoListFeign;


    /**
     * 完成待办
     */
    public void completeTodo(String bizCode, String bizNo, String updateUserId, String updateUserName) {
        TodoListUpdateReqDTO reqDTO = TodoListUpdateReqDTO.builder()
                .bizCode(bizCode)
                .bizNo(bizNo)
                .updatedBy(updateUserId)
                .updatedName(updateUserName)
                .todoStatus(TodoStatusEnum.DONE.getCode()).build();
        todoListFeign.updateStatus(reqDTO);
    }

}
