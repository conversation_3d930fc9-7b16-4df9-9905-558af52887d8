package com.trinasolar.trinax.contract.service;

import com.trinasolar.trinax.cart.dto.input.*;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.contract.dto.input.*;
import com.trinasolar.trinax.contract.dto.input.contract.DeliverCancelDTO;
import com.trinasolar.trinax.contract.dto.input.contract.DeliverContractItemDTO;
import com.trinasolar.trinax.contract.dto.input.contract.sales.*;
import com.trinasolar.trinax.contract.dto.input.statistic.ApprovedUnCountersignedContractReqDTO;
import com.trinasolar.trinax.contract.dto.input.statistic.StatisticContractMonthMoneyReqDTO;
import com.trinasolar.trinax.contract.dto.input.statistic.StatisticContractMwReqDTO;
import com.trinasolar.trinax.contract.dto.mq.ContractMessageMqDTO;
import com.trinasolar.trinax.contract.dto.output.*;
import com.trinasolar.trinax.contract.dto.output.contract.DeliverChangeItemResDTO;
import com.trinasolar.trinax.contract.dto.output.contract.frame.file.SalesContractExResDTO;
import com.trinasolar.trinax.contract.dto.output.contract.sales.ApprovedUnCountersignedContractDTO;
import com.trinasolar.trinax.contract.dto.output.contract.sales.CoBusinessResDTO;
import com.trinasolar.trinax.contract.dto.output.contract.sales.ContractTrendResDTO;
import com.trinasolar.trinax.contract.dto.output.contract.sales.CountersignedUnDeliveryContractDTO;
import com.trinasolar.trinax.contract.dto.output.file.ContractFile;
import com.trinasolar.trinax.contract.dto.output.statistic.StatisticContractMonthMoneyListDTO;
import com.trinasolar.trinax.contract.dto.output.statistic.StatisticContractMonthMoneyRespDTO;
import com.trinasolar.trinax.contract.dto.output.statistic.StatisticContractMwListDTO;
import com.trinasolar.trinax.contract.dto.output.statistic.StatisticContractMwRespDTO;
import com.trinasolar.trinax.integration.dto.output.contract.ContractChangeItemResDTO;
import com.trinasolar.trinax.intentorder.dto.input.IntentOrderPCQueryReqDTO;

import java.util.List;
import java.util.Map;

public interface ContractService {

    Result<String> modifyContractSta(List<ContactStaModifyReqDTO> reqDTO);

    Result<String> addContract(ContractInfoReqDTO reqDTO);

    List<ContractDeliveryResDTO> deliveryListByContractIds(List<String> contractIds);

    ContractDeliveryDetailResDTO qryListByContractIds(List<String> contractIds,List<String> contractBusinessItemIds);

    PageResponse<ContractResAppDTO> pageContractResAppAuth(PageRequest<ContractQueryAppReqDTO> pageReqDTO);

    PageResponse<ContractResAppDTO> pageContractResAppAuthWithCache(PageRequest<ContractQueryAppReqDTO> pageReqDTO);

    ContractDetailResAppDTO contractDetailResApp(String sfContractNo, Boolean isDeliver);

    PageResponse<ContractResPcDTO> pageContractResPcAuth(PageRequest<ContractQueryPcReqDTO> pageReqDTO);

    PageResponse<ContractResPcDTO> pageContractResPc(PageRequest<ContractQueryPcReqDTO> pageReqDTO);

    PageResponse<ContractResAppDTO> pageChangeContractResApp(PageRequest<ChangeContractQueryAppReqDTO> pageReqDTO);

    List<ContractFileResDTO> queryContractFile(String contractId);

    ContractDetailResPcDTO contractDetailResPc(ContractQueryPcDetailReqDTO reqDTO);

    Result<ContractResPcDTO> queryContactById(String contractId);

    /**
     * 消息通知
     *
     * @param req
     */
    void sendMessageForContract(ContractMessageMqDTO req);


    List<ContractResPcExcelDTO> listContractResPcExcelAuth(ContractQueryPcReqDTO reqDTO);

    List<ContractResPcExcelDTO> listContractResPcExcel(ContractQueryPcReqDTO reqDTO);

    ContractShipmentResDTO contractReleaseRes(String contractId);

    List<ContractShipmentTransportResDTO> contractShipmentTransportRes(String contractId, String erpSo);

    Double getCustomerSigningStatus(String intentOrderNo);

    List<ContractDeliveryResAppDTO> deliveryContractResApp(ContractDeliveryQueryReqDTO reqDTO);

    List<ContractDeliveryResAppDTO> deliveryContractForManagement(ContractDeliveryQueryReqDTO reqDTO);

    List<ContractResPc02DTO> listContractByIntentOrderNo(String intentOrderNo);

    void cleanCaffeineCache(ContractMessageMqDTO req);

    List<StatisticContractMwRespDTO> statisticContractMw(StatisticContractMwReqDTO reqDTO);

    List<StatisticContractMwListDTO> statisticContractMwList(StatisticContractMwReqDTO reqDTO);

    List<StatisticContractMonthMoneyRespDTO> statisticContractMonthMoney(StatisticContractMonthMoneyReqDTO reqDTO);

    List<StatisticContractMonthMoneyListDTO> statisticContractMonthMoneyList(StatisticContractMonthMoneyReqDTO reqDTO);

    Result<List<ContractReportResDTO>> reportPowerList(ContractReportReqDTO reqDTO);

    Result<Map<String, List<ContractReportResDTO>>> reportPowerChart(ContractReportReqDTO reqDTO);

    PageResponse<ApprovedUnCountersignedContractDTO> approvedUnCountersignedContractPage(PageRequest<ApprovedUnCountersignedContractReqDTO> pageReqDTO);

    List<ApprovedUnCountersignedContractDTO> approvedUnCountersignedContractList(ApprovedUnCountersignedContractReqDTO reqDTO);

    PageResponse<CountersignedUnDeliveryContractDTO> countersignedUnDeliveryContractPage(PageRequest<Integer> pageReqDTO);

    List<CountersignedUnDeliveryContractDTO> countersignedUnDeliveryContractList(int day);

    Result<List<ContractFile>> contractFiles(String contractId);

    Result<List<ContractChangeItemResDTO>> contractItemsModify(ContractItemsModifyReqDTO req);

    Result<List<ContractIntentOrderQueryResDTO>> getContractByIntentOrderNo(String intentOrderNo);

    List<SalesContractExResDTO> downSalesContract(ContractQueryAppReqDTO req);

    Result<List<CoBusinessResDTO>> qryContractBusiness(List<String> contractIds);

    Result<Void> updateDrSfId(List<DrSfIdUpdateReqDTO> req);

    Result<PageResponse<ContractItemReportResDTO>> reportContractItem(PageRequest<ContractItemReportQueryDTO> pageRequest);

    Result<List<ContractTrendResDTO>> contractTrendList(ContractTrendReqDTO req);

    PageResponse<DeliveryAgreementRespDTO> pageDeliveryAgreement(PageRequest<DeliveryAgreementQueryDTO> pageReqDTO);

    List<DeliveryAgreementRespDTO> exportDeliveryAgreement(DeliveryAgreementQueryDTO deliveryAgreementQueryDTO);

    Boolean cancelDeliveryAgreement(List<String> contractIds);

    PageResponse<EmailResDTOByContract> getEmail(PageRequest<EmailReqDTOByContract> req);

    Boolean sendEmailByExchange(SendEmailByExchangeDTOByContract req);

    SendEmailInfoDTO getSendEmailInfo(SendEmailInfoDTO req);

    Map<String, List<ContractBusinessItemResDTO>> queryBusinessItem(List<String> contractIds);

    QuickOrderContractReviewDTO reviewContract(IntentOrderPCQueryReqDTO intentOrderPCQueryReqDTO);

    QuickOrderContractPreviewResDTO previewContractDoc(QuickOrderContractSubmitReqDTO quickOrderContractSubmitReqDTO);

    Boolean submitQuickOrderContract(QuickOrderContractSubmitReqDTO quickOrderContractSubmitReqDTO);

    Boolean bpmCallBack(BPMContractReqDTO bpmContractReqDTO);

    Boolean retryQuickOrderContractToBPM(String contractId);

    void confirmOrder(ContractModifyReqDTO contractModifyReqDTO);
    void cancelOrder(ContractModifyReqDTO contractModifyReqDTO);

    DeliverChangeItemResDTO deliverContractItemsChange(List<DeliverContractItemDTO> itemDTOS);

    void deliverCancelContractItemsChange(DeliverCancelDTO deliverCancelDTO);
}
