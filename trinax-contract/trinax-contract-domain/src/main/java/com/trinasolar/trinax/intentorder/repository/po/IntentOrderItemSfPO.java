package com.trinasolar.trinax.intentorder.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 意向单商品行sf视图表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("intent_order_item_sf")
public class IntentOrderItemSfPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 意向单单号
     */
    private String intentOrderNo;

    /**
     * 源意向单行行号
     */
    private String originIntentOrderItemNo;

    /**
     * 意向单行行号
     */
    private String intentOrderItemNo;

    /**
     * SF商机行编号
     */
    private String opportunityItemId;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 功率
     */
    private String power;

    /**
     * 输入类型
     */
    private String inputType;

    /**
     * 需求量（W）
     */
    private BigDecimal quantityW;

    /**
     * 需求量（片）
     */
    private Long quantityP;

    /**
     * 需求量（MW）
     */
    private BigDecimal quantityMw;

    /**
     * 预计交货日期
     */
    private LocalDateTime estimatedDeliveryDate;

    /**
     * 安装方式（默认竖装，用户不可见、不可改）
     */
    private String installation;

    /**
     * 正极电缆长度（M）
     */
    private String cableLengthPositive;

    /**
     * 负极电缆长度（M）
     */
    @TableField("cable_Length_cathode")
    private String cableLengthCathode;

    /**
     * 端子规格
     */
    private String plugConnector;

    /**
     * 端子数量
     */
    private Long plugConnectorQuantity;

    /**
     * 参考价（元）：来自PBI
     */
    private BigDecimal guidePrice;

    /**
     * 销售价（元）-确认环节更新或代客下单直接提交
     */
    private BigDecimal salePrice;

    /**
     * 合计价格（参考价）
     */
    private BigDecimal guideTotalPrice;

    /**
     * 合计价格（销售价）
     */
    private BigDecimal saleTotalPrice;

    /**
     * 产品版本：来自PBI
     */
    private String productVersion;

    /**
     * 产品类型:组件;备件和其他
     */
    private String moduleType;

    /**
     * 行类型：PRODUCT-产品；COMPLIMENTARY - 赠品 
     */
    private String itemType;

    /**
     * 操作类型
     */
    private String opType;

    /**
     * 主栅数
     */
    private String busBar;

    /**
     * 组件长度
     */
    private BigDecimal moduleLength;

    /**
     * 组件宽度
     */
    private BigDecimal moduleWidth;

    /**
     * 最小输出功率
     */
    private String minPower;

    /**
     * 最大输出功率
     */
    private String maxPower;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建人姓名
     */
    private String createdName;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新人姓名
     */
    private String updatedName;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 删除状态：0-未删除，1-删除
     */
    private String isDeleted;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品图片
     */
    private String productImgUrl;

    /**
     * 产品类别
     */
    private String productCategory;

    /**
     * 意向单行分组id（同一组产品赠品该值相同）
     */
    private String groupId;

    /**
     * salesforce 同步状态：DRAFT-草稿；CONFIRMED-已确认
     */
    private String status;

    /**
     * 净价
     */
    private BigDecimal netPrice;

    /**
     * 其他成本价
     */
    private BigDecimal otherCostPrice;

    /**
     * 赠品关系组件id
     */
    private Long relationId;


}
