package com.trinasolar.trinax.contract.service.biz.message;

import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.messagetemplate.MessageTemplateNoticeTypeEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoBizCodeEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoOrganizationTypeEnum;
import com.trinasolar.trinax.basic.dto.input.MessageSendCommonReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendJPushReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendTodoReqDTO;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.contract.dto.mq.ContractMessageMqDTO;
import com.trinasolar.trinax.contract.manager.ContractMessageManager;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class SingleSignedMessageBiz {

    @Autowired
    private MqManager mqManager;
    @Autowired
    private ContractMessageManager contractMessageManager;

    @Value("${message.template.contractSingleSignedPush}")
    private String pushTemplateCode;

    @Value("${message.template.contractSingleSignedTodo}")
    private String todoTemplateCode;


    public void execute(ContractMessageMqDTO reqDTO){
        log.info("德勤单签后，通知给客户,入参：{}", reqDTO);
        log.info("德勤单签后，通知给客户start");
        MessageSendCommonReqDTO pushMessageReqDTO = new MessageSendCommonReqDTO();
        pushMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.JPUSH.getCode());

        List<MessageSendJPushReqDTO> jPushList = new ArrayList<>();
        Map<String,String> content = new HashMap<>();
        content.put("enterpriseName",reqDTO.getEnterpriseName());
        content.put("potentialSalesVolume",reqDTO.getPotentialSalesVolume().stripTrailingZeros().toPlainString());
        MessageSendJPushReqDTO pushReqDTO = contractMessageManager.generateJPush(pushTemplateCode,Collections.singletonList(reqDTO.getExternalUserId()),content);

        jPushList.add(pushReqDTO);
        pushMessageReqDTO.setJPushList(jPushList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC,JacksonUtil.bean2Json(pushMessageReqDTO));

        log.info("德勤单签后，通知给客户，给合作伙伴发送待办消息");
        MessageSendCommonReqDTO todoMessageReqDTO = new MessageSendCommonReqDTO();
        todoMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.TODO.getCode());

        List<MessageSendTodoReqDTO> todoList = new ArrayList<>();
        MessageSendTodoReqDTO todoReqDTO = contractMessageManager.generateTodo(reqDTO,todoTemplateCode,
                TodoBizCodeEnum.CONTRACT_SINGLE_SIGNED.getCode(),Collections.singletonList(reqDTO.getExternalUserId()),
                content,TodoOrganizationTypeEnum.PARTNER.getCode());

        todoList.add(todoReqDTO);
        todoMessageReqDTO.setTodoList(todoList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC,JacksonUtil.bean2Json(todoMessageReqDTO));
        log.info("德勤单签后，通知给客户完成,param:{}",JacksonUtil.bean2Json(todoMessageReqDTO));
    }

    public void executeConfig(ContractMessageMqDTO reqDTO) {
        log.info("SF同步合同信息，产品行变更，通知运营配置功率，入参：{}", reqDTO);
        MessageSendCommonReqDTO pushMessageReqDTO = new MessageSendCommonReqDTO();
        pushMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.TODO.getCode());

        Map<String, String> content = new HashMap<>();
        content.put("salesInternalUserName", reqDTO.getSalesInternalUserName());
        content.put("contractNo", reqDTO.getContractNo());
        List<MessageSendTodoReqDTO> todoList = new ArrayList<>();
        MessageSendTodoReqDTO todoReqDTO = contractMessageManager.generateTodo(reqDTO, TodoBizCodeEnum.CONTRACT_CONFIG_MULTI_POWER.getCode(),
                TodoBizCodeEnum.CONTRACT_CONFIG_MULTI_POWER.getCode(), Collections.singletonList(reqDTO.getOperationInternalUserId()),
                content, TodoOrganizationTypeEnum.OPERATION.getCode());
        todoList.add(todoReqDTO);
        pushMessageReqDTO.setTodoList(todoList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC, JacksonUtil.bean2Json(pushMessageReqDTO));

        log.info("SF同步合同信息，产品行变更，通知运营配置功率");
    }

}
