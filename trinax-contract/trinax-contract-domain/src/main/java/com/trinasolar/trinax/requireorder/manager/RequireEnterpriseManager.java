package com.trinasolar.trinax.requireorder.manager;

import cn.hutool.core.collection.CollUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.dto.output.EnterpriseBizRelationResDTO;
import dtt.cache.redisclient.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class RequireEnterpriseManager {

    @Autowired
    private EnterpriseFeign enterpriseFeign;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 查询企业对应战区
     * @param enterpriseId
     * @return
     */
    public String getBizOrgCode(String enterpriseId) {
        String redisKey = "ENTERPRISE:BIZ_ORG_CODE:" + enterpriseId;
        String redisVal = redisUtil.get(redisKey);
        if (StringUtils.isNotBlank(redisVal)) {
            return redisVal;
        }else {
            Result<List<EnterpriseBizRelationResDTO>> result = enterpriseFeign.getEnterpriseBizRelationByEnterpriseId(enterpriseId);
            if (Boolean.TRUE.equals(!result.getSuccess()) || CollUtil.isEmpty(result.getData())) {
                throw new BizException(ResultCode.FAIL.getCode(), "查询企业战区关联信息异常");
            }
            String bizOrgCode = result.getData().get(0).getBizOrganizationCode();
            if (StringUtils.isEmpty(bizOrgCode)) {
                throw new BizException(ResultCode.FAIL.getCode(), "企业信息异常，企业战区信息为空");
            }
            redisUtil.set(redisKey, bizOrgCode, 30L);
            return bizOrgCode;
        }
    }
}
