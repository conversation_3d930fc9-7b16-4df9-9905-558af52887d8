package com.trinasolar.trinax.contract.service.biz;

import cn.hutool.core.collection.CollUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.contract.dto.input.ContractReportReqDTO;
import com.trinasolar.trinax.contract.dto.output.ContractReportResDTO;
import com.trinasolar.trinax.contract.repository.mapper.ContractMapper;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.user.api.SysOrganizationFeign;
import com.trinasolar.trinax.user.dto.output.SysOrganizationRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ContractReportBiz {

    @Autowired
    private ContractMapper contractMapper;

    @Autowired
    private SysOrganizationFeign sysOrganizationFeign;

    @Autowired
    private EnterpriseFeign enterpriseFeign;

    /**
     * 功率统计报表
     * @param reqDTO
     * @return
     */
    public Map<String,List<ContractReportResDTO>> reportPowerChart(ContractReportReqDTO reqDTO) {
        List<ContractReportResDTO> result = reportPowerList(reqDTO);
        if(CollUtil.isNotEmpty(result)){
            Map<String,List<ContractReportResDTO>> map = result.stream().collect(Collectors.groupingBy(ContractReportResDTO::getBizOrganizationName));
            return map.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (oldValue, newValue) -> oldValue,
                            TreeMap::new
                    ));
        }else{
            return Collections.emptyMap();
        }
    }

    /**
     * 数量统计列表
     * @param reqDTO
     * @return
     */
    public List<ContractReportResDTO> reportPowerList(ContractReportReqDTO reqDTO) {
        //处理搜索时间范围
        prepareParam(reqDTO);

        //未选择企业类型时，直接查询汇总数据
        List<ContractReportResDTO> result = contractMapper.selectContractReport(reqDTO);
        if(CollUtil.isEmpty(result)){
            return Collections.emptyList();
        }
        //填充信息
        fillContent(result, reqDTO.getYear());
        log.info("---contract power result step1---:{}", JacksonUtil.bean2Json(result));
        if("2".equals(reqDTO.getApiType()) && CollUtil.isNotEmpty(result)){
            Map<String,List<ContractReportResDTO>> dataMap = result.stream().collect(Collectors.groupingBy(ContractReportResDTO::getBizOrganizationCode));
            for (Map.Entry<String, List<ContractReportResDTO>> entry : dataMap.entrySet()) {
                List<ContractReportResDTO> temp =  entry.getValue();
                temp = temp.stream().sorted(Comparator.comparing(ContractReportResDTO::getFullMonth)).collect(Collectors.toList());
                log.info("---contract power temp---:{}", JacksonUtil.bean2Json(result));
                BigDecimal power = new BigDecimal(0);
                for (ContractReportResDTO resDTO : temp) {
                    //统计功率
                    if(ObjectUtils.isNotEmpty(resDTO.getPotentialSalesVolume())){
                        power = power.add(resDTO.getPotentialSalesVolume());
                        resDTO.setPotentialSalesVolume(power);
                    }
                }
            }
        }
        log.info("---contract power result step2---:{}", JacksonUtil.bean2Json(result));
        return result.stream()
                .sorted(Comparator.comparing(ContractReportResDTO::getBizOrganizationName).thenComparing(ContractReportResDTO::getSingleMonth))
                .collect(Collectors.toList());
    }


    /**
     * 准备参数
     * @param reqDTO
     */
    private void prepareParam(ContractReportReqDTO reqDTO){
        if(ObjectUtils.isEmpty(reqDTO.getYear())){
            reqDTO.setYear(LocalDateTime.now().getYear());
        }
        //查询新增时，查询该年的数据;查询累计时，查询这一年每个月的累计加
        reqDTO.setStartTime(LocalDateTime.of(reqDTO.getYear(), 1,1,0,0,0));
        reqDTO.setEndTime(LocalDateTime.of(reqDTO.getYear(), 12,31,23,59,59));

        if(StringUtils.isNotBlank(reqDTO.getCustomerCategory())){
            reqDTO.setEnterpriseIdList(enterpriseFeign.listEnterpriseIdsByCustomerCategory(reqDTO.getCustomerCategory()));
        }
    }

    /**
     * 填充战区名字 & 构造不存在月份数据（数量默认0）
     * @param result
     */
    private void fillContent(List<ContractReportResDTO> result,int year){
        List<String> orgList = result.stream().map(ContractReportResDTO::getBizOrganizationCode).collect(Collectors.toList());
        Result<List<SysOrganizationRespDTO>> orgResult = sysOrganizationFeign.listOrgByOrganizationCodes(orgList);
        if(Boolean.TRUE.equals(orgResult.getSuccess()) && CollUtil.isNotEmpty(orgResult.getData())){
            Map<String,List<SysOrganizationRespDTO>> orgMap = orgResult.getData().stream().collect(Collectors.groupingBy(SysOrganizationRespDTO::getOrganizationCode));
            result.forEach(e->{
                if(CollUtil.isNotEmpty(orgMap.get(e.getBizOrganizationCode()))){
                    e.setBizOrganizationName(orgMap.get(e.getBizOrganizationCode()).get(0).getOrganizationName());
                }
            });
        }
        //如果是往年，需要构造12个月份数据；如果是当前年，需要构造已到月份数据
        List<String> totalFullMonth = new ArrayList<>();
        if(LocalDate.now().getYear() > year){
            for (int i = 1; i <= 12; i++) {
                totalFullMonth.add(year + String.format("%02d",i));
            }
        }else{
            int monthNumber = LocalDate.now().getMonthValue();
            for (int i = 1; i <= monthNumber; i++) {
                totalFullMonth.add(year + String.format("%02d",i));
            }
        }

        Map<String,List<ContractReportResDTO>> dataMap = result.stream().collect(Collectors.groupingBy(ContractReportResDTO::getBizOrganizationCode));
        for (Map.Entry<String, List<ContractReportResDTO>> entry : dataMap.entrySet()) {
            List<ContractReportResDTO> temp =  entry.getValue();
            List<String> existFullMonth = temp.stream().map(ContractReportResDTO::getFullMonth).collect(Collectors.toList());
            List<String> diffFullMonth = totalFullMonth.stream()
                    .filter(element -> !existFullMonth.contains(element))
                    .collect(Collectors.toList());
            diffFullMonth.forEach(e->{
                ContractReportResDTO resDTO = new ContractReportResDTO();
                resDTO.setBizOrganizationCode(entry.getKey());
                resDTO.setBizOrganizationName(entry.getValue().get(0).getBizOrganizationName());
                resDTO.setFullMonth(e);
                resDTO.setSingleYear(year);
                resDTO.setSingleMonth(Integer.valueOf(e.substring(4)));
                resDTO.setPotentialSalesVolume(new BigDecimal(0));
                result.add(resDTO);
            });
        }
    }
}
