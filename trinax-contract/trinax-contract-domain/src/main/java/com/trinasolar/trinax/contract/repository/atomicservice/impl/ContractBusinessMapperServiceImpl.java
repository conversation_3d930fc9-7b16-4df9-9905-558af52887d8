package com.trinasolar.trinax.contract.repository.atomicservice.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.contract.repository.atomicservice.ContractBusinessMapperService;
import com.trinasolar.trinax.contract.repository.mapper.ContractBusinessMapper;
import com.trinasolar.trinax.contract.repository.po.ContractBusinessPO;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class ContractBusinessMapperServiceImpl extends ServiceImpl<ContractBusinessMapper, ContractBusinessPO> implements ContractBusinessMapperService {
    @Override
    public ContractBusinessPO findByContractId(String contractId) {
        LambdaQueryWrapper<ContractBusinessPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractBusinessPO::getContractId, contractId);
        return getOne(queryWrapper);
    }

    @Override
    public List<ContractBusinessPO> listByContractIds(List<String> contractIds) {
        if (ObjectUtil.isEmpty(contractIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ContractBusinessPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ContractBusinessPO::getContractId, contractIds);
        return list(queryWrapper);
    }

    @Override
    public List<ContractBusinessPO> listByIntentOrderNos(List<String> intentOrderNos) {
        if (ObjectUtil.isEmpty(intentOrderNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ContractBusinessPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ContractBusinessPO::getIntentOrderNo, intentOrderNos);
        return list(queryWrapper);
    }


}
