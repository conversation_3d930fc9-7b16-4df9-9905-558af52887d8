package com.trinasolar.trinax.intentorder.manager;

import cn.hutool.core.bean.BeanUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.intentorder.dto.output.ProductPowerResDTO;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderItemPO;
import com.trinasolar.trinax.masterdata.api.ProductFeign;
import com.trinasolar.trinax.masterdata.constants.UpStatusEnum;
import com.trinasolar.trinax.masterdata.dto.input.ProductModulePowerStatusReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductModulePowerDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ProductPowerCheckManager {

    @Autowired
    private ProductFeign masterDataProductFeign;


    /**
     * 校验上下架信息:返回下架的产品信息
     */
    public List<ProductPowerResDTO> checkProductPowerInfo(List<IntentOrderItemPO> itemList) {
        List<ProductModulePowerStatusReqDTO> validReqList = new ArrayList<>();
        for (IntentOrderItemPO item: itemList) {
            ProductModulePowerStatusReqDTO validReq = new ProductModulePowerStatusReqDTO();
            validReq.setOutputPower(item.getPower());
            validReq.setProductId(item.getProductId());
            validReqList.add(validReq);
        }
        Result<List<ProductModulePowerDTO>> productPowerResult =  masterDataProductFeign.getModulePowerBatch(validReqList);
        List<ProductModulePowerDTO> powerResult = productPowerResult.getData();
        if(!CollectionUtils.isEmpty(powerResult)){
            List<ProductModulePowerDTO> powerDownResult = powerResult.stream().filter(e-> UpStatusEnum.DOWN.getCode().equals(e.getUpStatus())).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(powerDownResult)){
                return BeanUtil.copyToList(powerDownResult,ProductPowerResDTO.class);
            }
        }
        return Collections.emptyList();
    }




}
