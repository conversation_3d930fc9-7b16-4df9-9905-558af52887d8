package com.trinasolar.trinax.intentorder.manager;

import com.trinasolar.trinax.common.utils.ValidatorUtils;
import com.trinasolar.trinax.intentorder.constants.enums.DeliveryTypeEnum;
import com.trinasolar.trinax.intentorder.constants.enums.OrderMethodEnum;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderItemPO;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class IntentOrderCheckManager {

    /**
     * 校验的场景：
     * （1）外部用户提交意向单前；
     * （2）外部用户提交意向单时；
     * （3）内部用户确认意向单前；
     * （4）内部用户直接创建并确认意向单时；
     * （5）内部用户确认外部用户提交的意向单时
     * @param intentOrderPO
     * @param intentOrderItemPOList
     * @param currentUserId
     */
    public void checkIntentOrderInfo(IntentOrderPO intentOrderPO,List<IntentOrderItemPO> intentOrderItemPOList,String currentUserId) {
        checkIntentOrder(intentOrderPO,currentUserId);
        checkIntentOrderItem(intentOrderItemPOList);
    }


    /**
     * 校验意向单:只在提交和确认时会校验
     * @param orderPO
     */
    private void checkIntentOrder(IntentOrderPO orderPO,String currentUserId){
        ValidatorUtils.validateNotEmpty(orderPO.getEnterpriseId(),"请完善企业信息");
        ValidatorUtils.validateNotEmpty(orderPO.getIndustryAttributes(),"请完善行业属性信息");
        ValidatorUtils.validateNotEmpty(orderPO.getExpectDeliveryDate(),"请完善期望交货时间");
        ValidatorUtils.validateNotEmpty(orderPO.getOpportunitySegment(),"请完善应用场景信息");
        ValidatorUtils.validateNotEmpty(orderPO.getExternalUserId(),"请完善客户方联系人信息");
        ValidatorUtils.validateNotEmpty(orderPO.getIncoterm(),"配送方式不为空");
        ValidatorUtils.validateNotEmpty(orderPO.getSalesInternalUserId(),"请完善专属销售信息");
        ValidatorUtils.validateNotEmpty(orderPO.getOperationInternalUserId(),"请完善运营信息");
        //内部代创建意向单时，项目区域和销售账套必填
        if(OrderMethodEnum.ORDER_BY_EMPLOYEE.getCode().equals(orderPO.getIsSurrogateOrder())){
            ValidatorUtils.validateNotEmpty(orderPO.getGoodsArrivalRegion(),"请完善项目区域信息");
            ValidatorUtils.validateNotEmpty(orderPO.getRequestedOu(),"请完善销售账套信息");
        }
        //一、如果当前用户 和 意向单创建人是同一人.
        // （1）说明此时是由外部用户提交意向单;或者是内部用户代外部用户创建并确认意向单；
        // （2）否则是内部用户确认外部用户的意向单
        // （3）草稿状态意向单只有创建人能看见意向单，不存在提交/确认时创建人和当前人不同的情况
        //二、如果是确认外部用户提交的意向单需校验：预计交货时间；账套；项目区域信息
        if(!currentUserId.equals(orderPO.getCreatedBy())){
            ValidatorUtils.validateNotEmpty(orderPO.getEstimatedDeliveryDate(),"请完善预计交货时间");
            ValidatorUtils.validateNotEmpty(orderPO.getGoodsArrivalRegion(),"请完善项目区域信息");
            ValidatorUtils.validateNotEmpty(orderPO.getRequestedOu(),"请完善销售账套信息");
        }
    }

    /**
     * 校验意向单行
     * @param itemPOList
     */
    private void checkIntentOrderItem(List<IntentOrderItemPO> itemPOList){
        ValidatorUtils.validateNotEmpty(itemPOList,"请完善商品行信息");
        itemPOList.forEach(e->{
            ValidatorUtils.validateNotEmpty(e.getProductId(),"请选择商品" + e.getProductName() + "信息");
            ValidatorUtils.validateNotEmpty(e.getPower(),"请完善商品" + e.getProductName() + "的功率信息");
            ValidatorUtils.validateNotEmpty(e.getQuantityP(),"请完善商品" + e.getProductName() + "的片数量信息");
            ValidatorUtils.validateNotEmpty(e.getQuantityMw(),"请完善商品" + e.getProductName() + "的需求量（MW）信息");
        });

    }


}
