package com.trinasolar.trinax.intentorder.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.intentorder.repository.atomicservice.IntentOrderItemMapperService;
import com.trinasolar.trinax.intentorder.repository.mapper.IntentOrderItemMapper;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderItemPO;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 意向单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Service
public class IntentOrderItemMapperServiceImpl extends ServiceImpl<IntentOrderItemMapper, IntentOrderItemPO> implements IntentOrderItemMapperService {

}
