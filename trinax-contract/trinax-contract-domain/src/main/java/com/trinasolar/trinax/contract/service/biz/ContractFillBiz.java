package com.trinasolar.trinax.contract.service.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.trinasolar.trinax.basic.api.OptionFeign;
import com.trinasolar.trinax.basic.api.RegionFeign;
import com.trinasolar.trinax.basic.constants.enums.OptionGroupConstant;
import com.trinasolar.trinax.basic.constants.enums.RegionLevelEnum;
import com.trinasolar.trinax.basic.dto.Region.RegionQueryResDTO;
import com.trinasolar.trinax.basic.dto.output.OptionItemResDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.contract.constants.enums.ContractStatusEnum;
import com.trinasolar.trinax.contract.constants.enums.LinkFrameContractEnum;
import com.trinasolar.trinax.contract.constants.enums.OpenContractEnum;
import com.trinasolar.trinax.contract.dao.output.ContractIdContractNoDTO;
import com.trinasolar.trinax.contract.dto.output.*;
import com.trinasolar.trinax.contract.dto.output.contract.ContractAppPcCommonBusinessItemDTO;
import com.trinasolar.trinax.contract.manager.ContractManager;
import com.trinasolar.trinax.contract.repository.atomicservice.*;
import com.trinasolar.trinax.contract.repository.po.ContractBusinessItemPO;
import com.trinasolar.trinax.contract.repository.po.ContractBusinessItemSnapshotPO;
import com.trinasolar.trinax.contract.repository.po.ContractBusinessPO;
import com.trinasolar.trinax.contract.repository.po.ContractPO;
import com.trinasolar.trinax.intentorder.constants.enums.*;
import com.trinasolar.trinax.intentorder.repository.atomicservice.IntentOrderMapperService;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderPO;
import com.trinasolar.trinax.masterdata.api.BannerImageFeign;
import com.trinasolar.trinax.masterdata.api.ProductFeign;
import com.trinasolar.trinax.masterdata.dto.input.ProductModulePowerQueryReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductImageItemResDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductModulePowerDTO;
import com.trinasolar.trinax.partner.api.MarketingAccountFeign;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseTypeEnum;
import com.trinasolar.trinax.partner.dto.output.MarketingAccountRespDTO;
import com.trinasolar.trinax.user.api.SysOrganizationFeign;
import com.trinasolar.trinax.user.constants.SysOrganizationTypeEnum;
import com.trinasolar.trinax.user.dto.output.SysOrganizationRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ContractFillBiz {


    @Autowired
    private ContractBusinessItemMapperService contractBusinessItemMapperService;

    @Autowired
    private ContractBusinessMapperService contractBusinessMapperService;

    @Autowired
    private ContractMapperService contractMapperService;


    @Autowired
    private MarketingAccountFeign marketingAccountFeign;

    @Autowired
    private OptionFeign optionFeign;

    @Autowired
    private RegionFeign regionFeign;

    @Autowired
    private ContractFileMapperService contractFileMapperService;

    @Autowired
    private ContractManager contractManager;

    @Autowired
    private ExecutorService executorService;

    @Value("${spring.profiles.active}")
    private String active;

    @Value("${trina.multiPower.beginTime}")
    private String multiPowerBeginTime;
    @Autowired
    private SysOrganizationFeign sysOrganizationFeign;

    @Autowired
    private IntentOrderMapperService intentOrderMapperService;

    @Autowired
    private ProductFeign productFeign;

    @Autowired
    private ContractBusinessItemSnapshotMapperService contractBusinessItemSnapshotMapperService;


    public List<ContractDeliveryResDTO> convert2ContractDeliveryResDTO(List<ContractPO> contractPOS) {
        List<String> contractIds = contractPOS.stream().map(ContractPO::getContractId).collect(Collectors.toList());
        // 从contract_business查询关联信息
        List<ContractBusinessPO> contractBusinessPOS = contractBusinessMapperService.listByContractIds(contractIds);
        List<IntentOrderPO> intentOrderPOS=intentOrderMapperService.findByIntentOrderNos(contractBusinessPOS.stream().map(ContractBusinessPO::getIntentOrderNo).collect(Collectors.toList()));
        Map<String, ContractBusinessPO> contractId2contractBusinessPO = contractBusinessPOS.stream().collect(Collectors.toMap(ContractBusinessPO::getContractId, Function.identity()));
        Map<String,IntentOrderPO> intentOrderPOMap=intentOrderPOS.stream().collect(Collectors.toMap(IntentOrderPO::getIntentOrderNo,Function.identity()));
        return contractPOS.stream().map(e -> {
            ContractBusinessPO c = contractId2contractBusinessPO.get(e.getContractId());
            IntentOrderPO intentOrderPO=intentOrderPOMap.get(c.getIntentOrderNo());
            return ContractDeliveryResDTO.builder()
                    .contractId(e.getContractId())
                    .sfContractId(e.getSfContractId())
                    .sfContractNo(e.getSfContractNo())
                    .contractStatus(e.getContractStatus())
                    .orderStartDate(e.getCreatedTime())
                    .currency(c.getCurrency())
                    .subRegionCode(c.getSubRegionCode())
                    .arrivalCityCode(c.getArrivalCityCode())
                    .arrivalProvinceCode(c.getArrivalProvinceCode())
                    .marketingAccountId(c.getMarketingAccountId())
                    .priceBookId(c.getPriceBookId())
                    .sfOpportunityId(c.getSfOpportunityId())
                    .incoterm(c.getIncoterm())
                    .salesInternalUserId(c.getSalesInternalUserId())
                    .channel(c.getChannel())
                    .scheduledDeliveryDate(c.getScheduledDeliveryDate())
                    .application(c.getApplication())
                    .beneficiaryBankId(c.getBeneficiaryBankId())
                    .countryOfInstallation(c.getCountryOfInstallation())
                    .destinationPortId(c.getDestinationPortId())
                    .loadingPortId(c.getLoadingPortId())
                    .billToAddressId(c.getBillToAddressId())
                    .shipToAddressId(c.getShipToAddressId())
                    .taxClassificationId(c.getTaxClassificationId())
                    .odm(c.getOdm())
                    .sfRecordId(e.getSfRecordId())
                    .totalVolumeMw(c.getTotalVolumeMw())
                    .counterSignedDate(e.getCounterSignedDate())
                    .rebate(c.getRebate())
                    .isOpenContract(c.getIsOpenContract())
                    .openContract(c.getOpenContract())
                    .paymentTerms(c.getPaymentTerms())
                    .intentOrderNo(c.getIntentOrderNo())
                    .orderType(intentOrderPO.getOrderType())
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * 填充ContractResPcDTO的相关属性
     */
    public void fillContractPcDTO(List<ContractResPcDTO> contractResPcDTOS) {
        // 从合同提取数据列表
        List<String> optionValues01 = Lists.newArrayList();
        List<String> optionValues02 = Lists.newArrayList();
        for (ContractResPcDTO e : contractResPcDTOS) {
            optionValues01.add(e.getPaymentTerms());
            optionValues02.add(e.getContractType());
        }
        //从合同业务提取数据列表
        List<String> regionCodes = Lists.newArrayList();
        List<String> marketingAccountIds = Lists.newArrayList();
        contractResPcDTOS.forEach(e -> {
            regionCodes.add(e.getArrivalCountryCode());
            regionCodes.add(e.getArrivalCityCode());
            regionCodes.add(e.getArrivalProvinceCode());
            marketingAccountIds.add(e.getMarketingAccountId());
        });
        // 批量查询付款条件-basic
        Map<String, String> optionValue2desc01 = getOptionValue2desc(OptionGroupConstant.PAYMENT_TERM, optionValues01);
        // 批量合同类型-basic
        Map<String, String> optionValue2desc02 = getOptionValue2desc(OptionGroupConstant.CONTRACT_TYPE, optionValues02);
        // 批量查询区域信息-basic
        Result<List<RegionQueryResDTO>> regionByCodesResult = regionFeign.getRegionBySfDcCodes(regionCodes);
        Map<String, String> reginCode2regionName = Maps.newHashMap();
        if (ObjectUtil.isNotEmpty(regionByCodesResult.getData())) {
            reginCode2regionName.putAll(regionByCodesResult.getData().stream().collect(Collectors.toMap(e -> e.getSfdcCode() + "_" + e.getLevel(), RegionQueryResDTO::getName)));
        }
        // 批量查询账套信息-partner
        Result<List<MarketingAccountRespDTO>> marketingAccountRespDTOResult = marketingAccountFeign.listMarketingAccountRespDTO(marketingAccountIds);
        Map<String, String> accountId2accountOu = marketingAccountRespDTOResult.getData().stream().collect(Collectors.toMap(MarketingAccountRespDTO::getAccountId, MarketingAccountRespDTO::getAccountOu));
        // 填充数据
        Map<String, String> status_code2desc = Arrays.stream(ContractStatusEnum.values()).collect(Collectors.toMap(ContractStatusEnum::getCode, ContractStatusEnum::getDesc));
        //战区数据填充
        Result<List<SysOrganizationRespDTO>> sysOrganizationResult = sysOrganizationFeign.getOrgListByType(SysOrganizationTypeEnum.SALES.getCode());
        Map<String, String> organizationInfoMap = sysOrganizationResult.getData().stream().collect(Collectors.toMap(SysOrganizationRespDTO::getOrganizationCode, SysOrganizationRespDTO::getOrganizationName));
        contractResPcDTOS.forEach(e -> {
            e.setContractStatusText(status_code2desc.get(e.getContractStatus()));
            e.setPaymentTermsText(optionValue2desc01.get(e.getPaymentTerms()));
            e.setTaxText(getTaxText(e.getTax()));
            e.setIsLinkFrameContractText(LinkFrameContractEnum.getDescByCode(e.getIsLinkFrameContract()));
            e.setIncotermName(DeliveryTypeEnum.getDescByCode(e.getIncoterm()));
            e.setMarketingAccountName(accountId2accountOu.get(e.getMarketingAccountId()));
            e.setArrivalCountryName(reginCode2regionName.get(e.getArrivalCountryCode() + "_" + RegionLevelEnum.COUNTRY.getCode()));
            e.setArrivalCityName(reginCode2regionName.get(e.getArrivalCityCode() + "_" + RegionLevelEnum.CITY.getCode()));
            e.setArrivalProvinceName(reginCode2regionName.get(e.getArrivalProvinceCode() + "_" + RegionLevelEnum.PROVINCE.getCode()));
            e.setContractTypeText(optionValue2desc02.get(e.getContractType()));
            e.setSignEntity(StringUtils.isEmpty(e.getCapitalEnterpriseName()) ? e.getEnterpriseName() : e.getCapitalEnterpriseName());
            e.setCustomerCategoryText(EnterpriseTypeEnum.getDescByCode(e.getCustomerCategory()));

            if (StringUtils.isNotBlank(e.getOpenContract())) {
                String[] openContract = e.getOpenContract().split(";");
                StringBuilder openContractDesc = new StringBuilder();
                for (int i = 0; i < openContract.length; i++) {
                    openContractDesc.append(OpenContractEnum.getDescByCode(openContract[i])).append(";");
                }
                String openContractText = openContractDesc.substring(0, openContractDesc.length() - 1).toString();

                e.setOpenContractText(openContractText);
            }
            e.setBizOrganizationName(organizationInfoMap.get(e.getBizOrganizationCode()));
        });
    }

    /**
     * 填充ContractResAppDTO的相关属性
     *
     * @deprecated 此方法是同步接口，并发性能低，请使用异步接口 {@link #fillContractAppDTOWithAsync(List)}
     */
    @Deprecated
    public void fillContractAppDTO(List<? extends ContractResAppDTO> contractResAppDTOS) {
        //从合同提取数据列表
        List<String> contractIds = Lists.newArrayList();
        List<String> sfRecordIds = Lists.newArrayList();
        List<String> sfContractNos = Lists.newArrayList();
        List<String> sfLastContractNos = Lists.newArrayList();
        List<String> paymentTermsList = Lists.newArrayList();
        contractResAppDTOS.forEach(e -> {
            contractIds.add(e.getContractId());
            sfRecordIds.add(e.getSfRecordId());
            sfContractNos.add(e.getSfContractNo());
            if (ObjectUtil.isNotEmpty(e.getSfLastContractNo())) {
                sfLastContractNos.add(e.getSfLastContractNo());
            }
            if (ObjectUtil.isNotEmpty(e.getPaymentTerms())) {
                paymentTermsList.add(e.getPaymentTerms());
            }
        });
        LocalDateTime start = LocalDateTime.now();
        // 查询文件
        List<ContractFileResDTO> contractFileResDTOS = contractFileMapperService.listByContractIds(contractIds);
        Map<String, List<ContractFileResDTO>> contractId2contractFileResPOS = contractFileResDTOS.stream().collect(Collectors.groupingBy(ContractFileResDTO::getContractId));
        // 查询子合同（变更合同）
        List<ContractIdContractNoDTO> contractIdContractNoDTOS = contractMapperService.listBySfLastContractNos02(sfContractNos);
        Map<String, List<ContractIdContractNoDTO>> sfLastContractNo2contractIdContractNoDTOS = contractIdContractNoDTOS.stream().collect(Collectors.groupingBy(ContractIdContractNoDTO::getSfLastContractNo));
        // 查询上一版本合同
        List<ContractIdContractNoDTO> contractIdContractNoDTOS2 = contractMapperService.listBySfContractNos02(sfLastContractNos);
        Map<String, String> sfContractNo2contractId = contractIdContractNoDTOS2.stream().collect(Collectors.toMap(ContractIdContractNoDTO::getSfContractNo, ContractIdContractNoDTO::getContractId));
        // 查询合同行
        List<ContractBusinessItemPO> contractBusinessItemPOS = contractBusinessItemMapperService.listByContractIds(contractIds);
        List<ContractItemResAppDTO> contractItemResAppDTOS = BeanUtil.copyToList(contractBusinessItemPOS, ContractItemResAppDTO.class);
        Map<String, List<ContractItemResAppDTO>> contractId2contractItemResAppDTOS = contractItemResAppDTOS.stream().collect(Collectors.groupingBy(ContractItemResAppDTO::getContractId));
        log.info("合同列表-调用本数据库的表查询耗时{}毫秒, 环境: {}", Duration.between(start, LocalDateTime.now()).toMillis(), active);
        start = LocalDateTime.now();
        // 批量查询付款条件-basic
        Map<String, String> paymentTerms2desc = getOptionValue2desc(OptionGroupConstant.PAYMENT_TERM, paymentTermsList);
        // 查询图片-masterdata
        List<ProductImageItemResDTO> images = contractManager.listIconByProductIds(contractBusinessItemPOS.stream().map(ContractBusinessItemPO::getPbiProductId).distinct().collect(Collectors.toList()));
        Map<String, String> productId2imageUrl = images.stream().collect(Collectors.toMap(ProductImageItemResDTO::getProductId, ProductImageItemResDTO::getImageUrl));
        // 获取合同行发货数量-delivery
        Map<String, Integer> contractBusinessItemId2deliveryQuantityP = contractManager.getContractBusinessItemId2deliveryQuantityP(contractBusinessItemPOS.stream().map(ContractBusinessItemPO::getContractBusinessItemId).collect(Collectors.toList()));
        // 从SF获取合同剩余数量-salesforce
        Map<String, Integer> sfContractItemId2remainQuantityP = contractManager.getSfContractItemId2remainQuantityP(sfRecordIds, contractBusinessItemPOS);
        log.info("合同列表-调用外部服务查询耗时{}毫秒, 环境: {}", Duration.between(start, LocalDateTime.now()).toMillis(), active);
        // 填充数据
        Map<String, String> status_code2desc = Arrays.stream(ContractStatusEnum.values()).collect(Collectors.toMap(ContractStatusEnum::getCode, ContractStatusEnum::getDesc));
        for (ContractResAppDTO e : contractResAppDTOS) {
            e.setContractStatusText(status_code2desc.get(e.getContractStatus()));
            e.setPaymentTermsText(paymentTerms2desc.get(e.getPaymentTerms()));
            e.setTaxText(getTaxText(e.getTax()));
            e.setLastContractId(sfContractNo2contractId.get(e.getSfLastContractNo()));
            e.setContractItemResAppDTOS(contractId2contractItemResAppDTOS.get(e.getContractId()));
            List<ContractFileResDTO> sfFiles = contractId2contractFileResPOS.get(e.getContractId());
            e.setSfFiles(ObjectUtil.isNotNull(sfFiles) ? sfFiles : Collections.emptyList());
            List<ContractIdContractNoDTO> childrenContractNoDTOS = sfLastContractNo2contractIdContractNoDTOS.get(e.getSfContractNo());
            List<String> nextContractIds = Collections.emptyList();
            if (ObjectUtil.isNotEmpty(childrenContractNoDTOS)) {
                nextContractIds = childrenContractNoDTOS.stream().map(ContractIdContractNoDTO::getContractId).collect(Collectors.toList());
            }
            e.setNextContractIds(nextContractIds);
            int toBeExecutedQuantityPSum = 0;
            if (ObjectUtil.isNotEmpty(e.getContractItemResAppDTOS())) {
                for (ContractItemResAppDTO ei : e.getContractItemResAppDTOS()) {
                    ei.setImageUrl(productId2imageUrl.get(ei.getPbiProductId()));
                    // 处理未执行数量
                    int toBeExecutedQuantityP = contractManager.getToBeExecutedQuantityP(sfContractItemId2remainQuantityP, contractBusinessItemId2deliveryQuantityP, ei.getSfContractItemId(), ei.getContractBusinessItemId());
                    ei.setToBeExecutedQuantityP(toBeExecutedQuantityP);
                    toBeExecutedQuantityPSum += toBeExecutedQuantityP;
                    handlePartProduct(ei);
                }
            }
            e.setToBeExecutedQuantityP(toBeExecutedQuantityPSum);
        }
    }


    /**
     * 异步填充ContractResAppDTO的相关属性
     */
    public void fillContractAppDTOWithAsync(List<? extends ContractResAppDTO> contractResAppDTOS) {
        //从合同提取数据列表
        List<String> contractIds = Lists.newArrayList();
        List<String> sfRecordIds = Lists.newArrayList();
        List<String> sfContractNos = Lists.newArrayList();
        List<String> sfLastContractNos = Lists.newArrayList();
        List<String> paymentTermsList = Lists.newArrayList();
        List<String> intentOrderNos = Lists.newArrayList();
        contractResAppDTOS.forEach(e -> {
            contractIds.add(e.getContractId());
            sfRecordIds.add(e.getSfRecordId());
            sfContractNos.add(e.getSfContractNo());
            if (ObjectUtil.isNotEmpty(e.getSfLastContractNo())) {
                sfLastContractNos.add(e.getSfLastContractNo());
            }
            if (ObjectUtil.isNotEmpty(e.getPaymentTerms())) {
                paymentTermsList.add(e.getPaymentTerms());
            }if (StringUtils.isNotBlank(e.getIntentOrderNo())) {
                intentOrderNos.add(e.getIntentOrderNo());
            }

        });
        AtomicReference<Map<String, List<ContractFileResDTO>>> contractId2contractFileResPOS = new AtomicReference<>(Collections.emptyMap());
        AtomicReference<Map<String, List<ContractIdContractNoDTO>>> sfLastContractNo2contractIdContractNoDTOS = new AtomicReference<>(Collections.emptyMap());
        AtomicReference<Map<String, String>> sfContractNo2contractId = new AtomicReference<>(Collections.emptyMap());
        AtomicReference<Map<String, Integer>> intentOrderNoMap = new AtomicReference<>(Collections.emptyMap());
        CountDownLatch countDownLatch = new CountDownLatch(2);

        AtomicReference<Map<String, String>> paymentTerms2desc = new AtomicReference<>(Collections.emptyMap());
        // 异步执行1
        asyncExecute(() -> {
            // 查询文件
            List<ContractFileResDTO> contractFileResDTOS = contractFileMapperService.listByContractIds(contractIds);
            contractId2contractFileResPOS.set(contractFileResDTOS.stream().collect(Collectors.groupingBy(ContractFileResDTO::getContractId)));
            // 查询子合同（变更合同）
            List<ContractIdContractNoDTO> contractIdContractNoDTOS = contractMapperService.listBySfLastContractNos02(sfContractNos);
            sfLastContractNo2contractIdContractNoDTOS.set(contractIdContractNoDTOS.stream().collect(Collectors.groupingBy(ContractIdContractNoDTO::getSfLastContractNo)));
            // 查询上一版本合同
            List<ContractIdContractNoDTO> contractIdContractNoDTOS2 = contractMapperService.listBySfContractNos02(sfLastContractNos);
            sfContractNo2contractId.set(contractIdContractNoDTOS2.stream().collect(Collectors.toMap(ContractIdContractNoDTO::getSfContractNo, ContractIdContractNoDTO::getContractId)));
            // 批量查询付款条件-basic
            paymentTerms2desc.set(getOptionValue2desc(OptionGroupConstant.PAYMENT_TERM, paymentTermsList));
            List<IntentOrderPO> intentOrderPOS = intentOrderMapperService.findByIntentOrderNos(intentOrderNos);
            intentOrderNoMap.set(intentOrderPOS.stream().collect(Collectors.toMap(IntentOrderPO::getIntentOrderNo, IntentOrderPO::getOrderType, (v1, v2)->v2)));
        }, countDownLatch, "查询文件+查询子合同+查询上一版本合同+批量查询付款条");
        // 查询合同行
        List<ContractBusinessItemPO> contractBusinessItemPOS = contractBusinessItemMapperService.listByContractIds(contractIds);
        List<ContractItemResAppDTO> contractItemResAppDTOS = BeanUtil.copyToList(contractBusinessItemPOS, ContractItemResAppDTO.class);
        fillMultiPowerList(contractItemResAppDTOS);
        Map<String, List<ContractItemResAppDTO>> contractId2contractItemResAppDTOS = contractItemResAppDTOS.stream().collect(Collectors.groupingBy(ContractItemResAppDTO::getContractId));
        AtomicReference<Map<String, String>> productId2imageUrl = new AtomicReference<>(Collections.emptyMap());
        AtomicReference<Map<String, Integer>> contractBusinessItemId2deliveryQuantityP = new AtomicReference<>(Collections.emptyMap());
        // 异步执行2
        asyncExecute(() -> {
            // 查询图片-masterdata
            List<ProductImageItemResDTO> images = contractManager.listIconByProductIds(contractBusinessItemPOS.stream().map(ContractBusinessItemPO::getPbiProductId).distinct().collect(Collectors.toList()));
            productId2imageUrl.set(images.stream().collect(Collectors.toMap(ProductImageItemResDTO::getProductId, ProductImageItemResDTO::getImageUrl)));
            // 获取合同行发货数量-delivery
            contractBusinessItemId2deliveryQuantityP.set(contractManager.getContractBusinessItemId2deliveryQuantityP(contractBusinessItemPOS.stream().map(ContractBusinessItemPO::getContractBusinessItemId).collect(Collectors.toList())));
        }, countDownLatch, "查询图片+获取合同行发货数量");
        // 从SF获取合同剩余数量-salesforce
        Map<String, Integer> sfContractItemId2remainQuantityP = contractManager.getSfContractItemId2remainQuantityP(sfRecordIds, contractBusinessItemPOS);
        // 填充数据
        Map<String, String> status_code2desc = Arrays.stream(ContractStatusEnum.values()).collect(Collectors.toMap(ContractStatusEnum::getCode, ContractStatusEnum::getDesc));
        try {
            // 等待所有接口执行完毕
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        }
        Map<String, String> reginCode2regionName = Maps.newHashMap();
        List<String> regionCodes = new ArrayList<>();
        contractResAppDTOS.forEach(e -> {
            regionCodes.add(e.getArrivalCityCode());
            regionCodes.add(e.getArrivalProvinceCode());
        });
        Result<List<RegionQueryResDTO>> regionByCodesResult = regionFeign.getRegionBySfDcCodes(regionCodes);

        if (ObjectUtil.isNotEmpty(regionByCodesResult.getData())) {
            reginCode2regionName.putAll(regionByCodesResult.getData().stream().collect(Collectors.toMap(e -> e.getSfdcCode() + "_" + e.getLevel(), RegionQueryResDTO::getName)));
        }
        for (ContractResAppDTO e : contractResAppDTOS) {
            if(ContractStatusEnum.OA_FAILED.getCode().equals(e.getContractStatus())){
                //不给用户展示审批提交失败的状态描述
                e.setContractStatusText(ContractStatusEnum.APPROVING.getDesc());
            }else{
                e.setContractStatusText(status_code2desc.get(e.getContractStatus()));
            }
            e.setPaymentTermsText(paymentTerms2desc.get().get(e.getPaymentTerms()));
            e.setTaxText(getTaxText(e.getTax()));
            e.setLastContractId(sfContractNo2contractId.get().get(e.getSfLastContractNo()));
            e.setContractItemResAppDTOS(contractId2contractItemResAppDTOS.get(e.getContractId()));
            List<ContractFileResDTO> sfFiles = contractId2contractFileResPOS.get().get(e.getContractId());
            e.setSfFiles(ObjectUtil.isNotNull(sfFiles) ? sfFiles : Collections.emptyList());
            List<ContractIdContractNoDTO> childrenContractNoDTOS = sfLastContractNo2contractIdContractNoDTOS.get().get(e.getSfContractNo());
            List<String> nextContractIds = Collections.emptyList();
            if (ObjectUtil.isNotEmpty(childrenContractNoDTOS)) {
                nextContractIds = childrenContractNoDTOS.stream().map(ContractIdContractNoDTO::getContractId).collect(Collectors.toList());
            }
            e.setNextContractIds(nextContractIds);
            int toBeExecutedQuantityPSum = 0;
            boolean canBeShipped = false;
            if (ObjectUtil.isNotEmpty(e.getContractItemResAppDTOS())) {
                for (ContractItemResAppDTO ei : e.getContractItemResAppDTOS()) {
                    ei.setImageUrl(productId2imageUrl.get().get(ei.getPbiProductId()));
                    // 处理未执行数量
                    int toBeExecutedQuantityP = contractManager.getToBeExecutedQuantityP(sfContractItemId2remainQuantityP, contractBusinessItemId2deliveryQuantityP.get(), ei.getSfContractItemId(), ei.getContractBusinessItemId());
                    Integer remainingQuantityP = sfContractItemId2remainQuantityP.get(ei.getSfContractItemId());
                    if (toBeExecutedQuantityP > 0 && ModuleTypeEnum.MODULE.getCode().equals(ei.getModuleType())) {
                        //过滤掉备件
                        canBeShipped = true;
                    }
                    ei.setToBeExecutedQuantityP(toBeExecutedQuantityP);
                    ei.setRemainingQuantityP(remainingQuantityP);
                    toBeExecutedQuantityPSum += toBeExecutedQuantityP;
                    handlePartProduct(ei);
                }
            }
            e.setCanBeShipped(canBeShipped);
            if(ObjectUtil.isNotNull(e.getRebate())){
                e.setRebateText(Boolean.TRUE.equals(e.getRebate()) ? "是" : "否");
            }
            e.setToBeExecutedQuantityP(toBeExecutedQuantityPSum);
            e.setIncotermName(DeliveryTypeEnum.getDescByCode(e.getIncoterm()));
            e.setArrivalProvinceName(reginCode2regionName.get(e.getArrivalProvinceCode() + "_" + RegionLevelEnum.PROVINCE.getCode()));
            e.setArrivalCityName(reginCode2regionName.get(e.getArrivalCityCode() + "_" + RegionLevelEnum.CITY.getCode()));
            e.setSignEntity(StringUtils.isEmpty(e.getCapitalEnterpriseName()) ? e.getEnterpriseName() : e.getCapitalEnterpriseName());
            e.setOrderType(intentOrderNoMap.get().get(e.getIntentOrderNo()));
        }
    }

    public void fillMultiPowerList(List<? extends ContractAppPcCommonBusinessItemDTO> contractItemResAppDTOS) {
        Set<String> productIdSet=contractItemResAppDTOS.stream().map(ContractAppPcCommonBusinessItemDTO::getPbiProductId).collect(Collectors.toSet());
        ProductModulePowerQueryReqDTO productModulePowerQueryReqDTO=new ProductModulePowerQueryReqDTO();
        productModulePowerQueryReqDTO.setProductIds(productIdSet);
        Result<List<ProductModulePowerDTO>> result=productFeign.queryProductModulePowerList(productModulePowerQueryReqDTO);
        Assert.isTrue(result.getSuccess(),"查询产品功率范围出错");

        List<ProductModulePowerDTO> productModulePowerDTOS=result.getData();
        Map<String, List<ProductModulePowerDTO>> productModulePowerDTOMap=productModulePowerDTOS.stream().collect(Collectors.groupingBy(ProductModulePowerDTO::getProductId));
        contractItemResAppDTOS.forEach(item->{
            item.setMultiPowerList(calcMultiPowerList(item, productModulePowerDTOMap.get(item.getPbiProductId())));
        });
    }

    private List<Integer> calcMultiPowerList(ContractAppPcCommonBusinessItemDTO item, List<ProductModulePowerDTO> productModulePowerDTOs) {
        if(StringUtils.isNotBlank(item.getMultiPowerBegin())&& StringUtils.isNotBlank(item.getMultiPowerEnd())){
            Integer begin=Integer.parseInt(item.getMultiPowerBegin());
            Integer end=Integer.parseInt(item.getMultiPowerEnd());

            return productModulePowerDTOs.stream().map(ProductModulePowerDTO::getOutputPower).map(Integer::parseInt).
                    filter(power->power<=end&&power>=begin).collect(Collectors.toList());
        }else{
            if(item.getPower()!=null){
                return Arrays.asList(item.getPower().intValue());
            }else{
                return Collections.emptyList();
            }
        }

    }

    private void asyncExecute(Runnable tak, CountDownLatch countDownLatch, String title) {
        executorService.submit(() -> {
            try {
                LocalDateTime start = LocalDateTime.now();
                tak.run();
                log.info("合同列表-{}-耗时{}毫秒, 环境: {}", title, Duration.between(start, LocalDateTime.now()).toMillis(), active);
            } finally {
                countDownLatch.countDown();
            }
        });

    }


    private void handlePartProduct(ContractItemResCommonDTO e) {
        if (e.getModuleType().equals(ModuleTypeEnum.PART.getCode())) {
            // 端子产品销售价，金额固定为0
            e.setUnitPriceW(BigDecimal.ZERO);
            e.setTotalAmount(BigDecimal.ZERO);
            //产品名称特殊处理
            String newProductName = ProductNameEnum.getDescByCode(e.getPbiProductId());
            if (ObjectUtil.isNotEmpty(newProductName)) {
                e.setProductName(newProductName);
            }
        }
    }


    public List<ContractItemResPcDTO> convert2ContractItemResPcDTO(List<ContractBusinessItemPO> contractBusinessItemPOS, String sfRecordId) {
        // 从SF获取合同剩余数量
        Map<String, Integer> sfContractItemId2remainQuantityP = contractManager.getSfContractItemId2remainQuantityP(Lists.newArrayList(sfRecordId), contractBusinessItemPOS);
        log.info("pc 页面展示剩余发货数量sf ：{}", JacksonUtil.bean2Json(sfContractItemId2remainQuantityP));
        // 获取合同行发货数量
        List<String> contractBusinessItemIdList = contractBusinessItemPOS.stream().map(ContractBusinessItemPO::getContractBusinessItemId).collect(Collectors.toList());
        Map<String, Integer> contractBusinessItemId2deliveryQuantityP = contractManager.getContractBusinessItemId2deliveryQuantityP(contractBusinessItemIdList);
        log.info("pc 页面展示剩余发货数量deliver ：{}", JacksonUtil.bean2Json(contractBusinessItemId2deliveryQuantityP));

        return contractBusinessItemPOS.stream().map(e -> {
            int toBeExecutedQuantityP = contractManager.getToBeExecutedQuantityP(sfContractItemId2remainQuantityP, contractBusinessItemId2deliveryQuantityP, e.getSfContractItemId(), e.getContractBusinessItemId());
            ContractItemResPcDTO contractItemResPcDTO = ContractItemResPcDTO.builder()
                    .contractBusinessItemId(e.getContractBusinessItemId())
                    .contractId(e.getContractId())
                    .itemType(e.getItemType())
                    .installation(e.getInstallation())
                    .installationText(InstallationTypeEnum.getDescByCode(e.getInstallation()))
                    .quantityP(e.getQuantityP())
                    .quantityMw(e.getQuantityMw())
                    .netPrice(e.getNetPrice())
                    .totalAdditionalCost(e.getTotalAdditionalCost())
                    .unitPriceP(e.getUnitPriceP())
                    .notes(e.getNotes())
                    .currency(e.getCurrency())
                    .remainingQuantityP(sfContractItemId2remainQuantityP.get(e.getSfContractItemId()))
                    .totalAmount(e.getTotalAmount())
                    .efc(e.getEfc())
                    .power(e.getPower())
                    .expectedDeliverDate(e.getExpectedDeliverDate())
                    .expectedDeliverDate02(getLocalDateWithNull(e.getExpectedDeliverDate()))
                    .terminal(ModuleTypeEnum.PART.getCode().equals(e.getModuleType()) ? "是" : "否")
                    .giftText(ItemTypeEnum.COMPLIMENTARY.getCode().equals(e.getItemType()) ? "是" : "否")
                    .multiPowerBegin(e.getMultiPowerBegin())
                    .multiPowerEnd(e.getMultiPowerEnd())
                    .build();
            contractItemResPcDTO.setPbiProductId(e.getPbiProductId());
            contractItemResPcDTO.setProductName(e.getProductName());
            contractItemResPcDTO.setModuleType(e.getModuleType());
            contractItemResPcDTO.setUnitPriceW(e.getUnitPriceW());
            contractItemResPcDTO.setToBeExecutedQuantityP(toBeExecutedQuantityP);
            handlePartProduct(contractItemResPcDTO);
            contractItemResPcDTO.setSfContractItemId(e.getSfContractItemId());
            contractItemResPcDTO.setSfParentItemId(e.getSfParentItemId());
            return contractItemResPcDTO;
        }).collect(Collectors.toList());
    }

    public List<ContractItemResPcDTO> convert2ContractItemResPcDTO2(List<ContractBusinessItemSnapshotPO> snapshotPOS, String sfRecordId) {
        // 从SF获取合同剩余数量
        Map<String, Integer> sfContractItemId2remainQuantityP = contractManager.getSfContractItemId2remainQuantityP(Lists.newArrayList(sfRecordId), Lists.newArrayList());
        log.info("pc 页面展示剩余发货数量sf ：{}", JacksonUtil.bean2Json(sfContractItemId2remainQuantityP));
        // 获取合同行发货数量
        List<String> contractBusinessItemIdList = snapshotPOS.stream().map(ContractBusinessItemSnapshotPO::getContractBusinessItemId).collect(Collectors.toList());
        Map<String, Integer> contractBusinessItemId2deliveryQuantityP = contractManager.getContractBusinessItemId2deliveryQuantityP(contractBusinessItemIdList);
        log.info("pc 页面展示剩余发货数量deliver ：{}", JacksonUtil.bean2Json(contractBusinessItemId2deliveryQuantityP));

        return snapshotPOS.stream().map(e -> {
            int toBeExecutedQuantityP = contractManager.getToBeExecutedQuantityP(sfContractItemId2remainQuantityP, contractBusinessItemId2deliveryQuantityP, e.getSfContractItemId(), e.getContractBusinessItemId());
            ContractItemResPcDTO contractItemResPcDTO = ContractItemResPcDTO.builder()
                    .contractBusinessItemId(e.getContractBusinessItemId())
                    .contractId(e.getContractId())
                    .itemType(e.getItemType())
                    .installation(e.getInstallation())
                    .installationText(InstallationTypeEnum.getDescByCode(e.getInstallation()))
                    .quantityP(e.getQuantityP())
                    .quantityMw(e.getQuantityMw())
                    .netPrice(e.getNetPrice())
                    .totalAdditionalCost(e.getTotalAdditionalCost())
                    .unitPriceP(e.getUnitPriceP())
                    .notes(e.getNotes())
                    .currency(e.getCurrency())
                    .remainingQuantityP(sfContractItemId2remainQuantityP.get(e.getSfContractItemId()))
                    .totalAmount(e.getTotalAmount())
                    .efc(e.getEfc())
                    .power(e.getPower())
                    .expectedDeliverDate(e.getExpectedDeliverDate())
                    .expectedDeliverDate02(getLocalDateWithNull(e.getExpectedDeliverDate()))
                    .terminal(ModuleTypeEnum.PART.getCode().equals(e.getModuleType()) ? "是" : "否")
                    .giftText(ItemTypeEnum.COMPLIMENTARY.getCode().equals(e.getItemType()) ? "是" : "否")
                    .multiPowerBegin(e.getMultiPowerBegin())
                    .multiPowerEnd(e.getMultiPowerEnd())
                    .build();
            contractItemResPcDTO.setPbiProductId(e.getPbiProductId());
            contractItemResPcDTO.setProductName(e.getProductName());
            contractItemResPcDTO.setModuleType(e.getModuleType());
            contractItemResPcDTO.setUnitPriceW(e.getUnitPriceW());
            contractItemResPcDTO.setToBeExecutedQuantityP(toBeExecutedQuantityP);
            handlePartProduct(contractItemResPcDTO);
            return contractItemResPcDTO;
        }).collect(Collectors.toList());
    }

    private LocalDate getLocalDateWithNull(LocalDateTime dateTime) {
        if (ObjectUtil.isNotNull(dateTime)) {
            return dateTime.toLocalDate();
        }
        return null;
    }

    private String getTaxText(String tax) {
        if (ObjectUtil.isNotEmpty(tax)) {
            return tax + "%";
        }
        return null;
    }

    private String getMarketingAccountName(String marketingAccountId) {
        if (ObjectUtil.isNotEmpty(marketingAccountId)) {
            Result<MarketingAccountRespDTO> marketingAccountRespDTOResult = marketingAccountFeign.getMarketingAccountRespDTO(marketingAccountId);
            MarketingAccountRespDTO marketingAccountRespDTO = marketingAccountRespDTOResult.getData();
            if (Objects.isNull(marketingAccountRespDTO)) {
                throw new BizException(ResultCode.FAIL.getCode(), "未找到销售账套：" + marketingAccountId);
            }
            return marketingAccountRespDTO.getAccountOu();
        }
        return null;
    }

    /**
     * 批量获取值和描述的对应关系
     */
    private Map<String, String> getOptionValue2desc(String optionGroup, List<String> values) {
        if (ObjectUtil.isEmpty(values)) {
            return Collections.emptyMap();
        }
        Result<List<OptionItemResDTO>> batchOptionItemByValueResult = optionFeign.getBatchOptionItemByValue(values);
        if (ObjectUtil.isEmpty(batchOptionItemByValueResult.getData())) {
            return Collections.emptyMap();
        }
        return batchOptionItemByValueResult.getData().stream().filter(e -> e.getOptionGroup().equals(optionGroup)).collect(Collectors.toMap(OptionItemResDTO::getOptionValue, OptionItemResDTO::getOptionDesc));
    }

    public ContractDetailResAppDTO convert2ContractDetailResAppDTO(ContractResAppDTO contractResAppDTO,Boolean isDeliver) {
        ContractBusinessPO contractBusinessPO = contractBusinessMapperService.findByContractId(contractResAppDTO.getContractId());
        ContractDetailResAppDTO contractDetailResAppDTO = BeanUtil.toBean(contractResAppDTO, ContractDetailResAppDTO.class);
        IntentOrderPO intentOrderPO = intentOrderMapperService.findByIntentOrderNo(contractBusinessPO.getIntentOrderNo());
        if (intentOrderPO != null) {
            contractDetailResAppDTO.setDeliverAddress(intentOrderPO.getDeliverAddress());
            contractDetailResAppDTO.setDeliverContactPhone(intentOrderPO.getDeliverContactPhone());
            contractDetailResAppDTO.setDeliverContactor(intentOrderPO.getDeliverContactor());
            contractDetailResAppDTO.setDeliverMultiPower(intentOrderPO.getDeliverMultiPower());
            contractDetailResAppDTO.setOrderType(intentOrderPO.getOrderType());
        }
        // 查询合同业务
        fillContractAppDTOWithAsync(Lists.newArrayList(contractDetailResAppDTO));
        //处理app端的产品行进行合并汇总展示
        mergeContractBusinessItem(contractDetailResAppDTO.getContractItemResAppDTOS(),intentOrderPO);
        //只保留一级节点
        List<ContractItemResAppDTO>  parentContractItems=contractDetailResAppDTO.getContractItemResAppDTOS().stream().filter(item->ObjectUtil.equals(item.getSfParentItemId(),"0")).collect(Collectors.toList());
        if(isDeliver!=null&&!isDeliver){
            //如果非发货入口进入，需要查询一级节点的快照信息，如果存在快照，需要使用快照来替换片数和MW数
            fillParentItemsBySnapshot(parentContractItems);
            fillContractSummary(contractDetailResAppDTO,parentContractItems);
        }
        contractDetailResAppDTO.setContractItemResAppDTOS(parentContractItems);


        contractDetailResAppDTO.setIncoterm(contractBusinessPO.getIncoterm());
        contractDetailResAppDTO.setIncotermName(DeliveryTypeEnum.getDescByCode(contractBusinessPO.getIncoterm()));
        contractDetailResAppDTO.setSalesInternalUserId(contractBusinessPO.getSalesInternalUserId());
        contractDetailResAppDTO.setSalesInternalUserName(contractBusinessPO.getSalesInternalUserName());
        contractDetailResAppDTO.setIndustryLevelOne(contractBusinessPO.getIndustryLevelOne());
        contractDetailResAppDTO.setIndustryLevelOneName(contractManager.getOptionDesc(OptionGroupConstant.INDUSTRY_ATTRIBUTE, contractBusinessPO.getIndustryLevelOne()));
        contractDetailResAppDTO.setIndustryLevelTwo(contractBusinessPO.getIndustryLevelTwo());
        contractDetailResAppDTO.setIndustryLevelTwoName(contractManager.getOptionDesc(OptionGroupConstant.INDUSTRY_ATTRIBUTE, contractBusinessPO.getIndustryLevelTwo()));
        contractDetailResAppDTO.setOperationInternalUserId(contractBusinessPO.getOperationInternalUserId());
        contractDetailResAppDTO.setOperationInternalUserName(contractBusinessPO.getOperationInternalUserName());
        contractDetailResAppDTO.setMarketingAccountId(contractBusinessPO.getMarketingAccountId());
        contractDetailResAppDTO.setMarketingAccountName(getMarketingAccountName(contractBusinessPO.getMarketingAccountId()));
        contractDetailResAppDTO.setCurrency(contractBusinessPO.getCurrency());
        contractDetailResAppDTO.setRequiredDeliveryDate(contractBusinessPO.getRequiredDeliveryDate());
        contractDetailResAppDTO.setArrivalCityCode(contractBusinessPO.getArrivalCityCode());
        contractDetailResAppDTO.setArrivalCountryCode(contractBusinessPO.getArrivalCountryCode());
        contractDetailResAppDTO.setArrivalProvinceCode(contractBusinessPO.getArrivalProvinceCode());

        // 批量查询区域信息
        Result<List<RegionQueryResDTO>> regionByCodesResult = regionFeign.getRegionBySfDcCodes(Lists.newArrayList(contractBusinessPO.getArrivalCityCode(), contractBusinessPO.getArrivalCountryCode(), contractBusinessPO.getArrivalProvinceCode()).stream().filter(ObjectUtil::isNotEmpty).collect(Collectors.toList()));
        if (ObjectUtil.isNotEmpty(regionByCodesResult.getData())) {
            Map<String, String> reginCode2regionName = regionByCodesResult.getData().stream().collect(Collectors.toMap(e -> e.getSfdcCode() + "_" + e.getLevel(), RegionQueryResDTO::getName));
            contractDetailResAppDTO.setArrivalCityName(reginCode2regionName.get(contractDetailResAppDTO.getArrivalCityCode() + "_" + RegionLevelEnum.CITY.getCode()));
            contractDetailResAppDTO.setArrivalCountryName(reginCode2regionName.get(contractDetailResAppDTO.getArrivalCountryCode() + "_" + RegionLevelEnum.COUNTRY.getCode()));
            contractDetailResAppDTO.setArrivalProvinceName(reginCode2regionName.get(contractDetailResAppDTO.getArrivalProvinceCode() + "_" + RegionLevelEnum.PROVINCE.getCode()));
            contractDetailResAppDTO.setDeliveryArea(contractDetailResAppDTO.getArrivalCountryName() + "/" + contractDetailResAppDTO.getArrivalProvinceName() + "/" + contractDetailResAppDTO.getArrivalCityName());
        }
        contractDetailResAppDTO.setContractChangeResPcDTOS(changeInfoInit(contractDetailResAppDTO.getSfContractNo()));
        contractDetailResAppDTO.setSignEntity(StringUtils.isEmpty(contractDetailResAppDTO.getCapitalEnterpriseName()) ? contractDetailResAppDTO.getEnterpriseName() : contractDetailResAppDTO.getCapitalEnterpriseName());
        return contractDetailResAppDTO;
    }

    /**
     * 对MW和总价重新计算
     * @param contractDetailResAppDTO
     * @param parentContractItems
     */
    private void fillContractSummary(ContractDetailResAppDTO contractDetailResAppDTO, List<ContractItemResAppDTO> parentContractItems) {
        contractDetailResAppDTO.setTotalVolumeMw(parentContractItems.stream().map(item->item.getQuantityMw()).reduce(BigDecimal.ZERO,BigDecimal::add));
        contractDetailResAppDTO.setTotalAmountRmb(parentContractItems.stream().map(item->item.getUnitPriceW().multiply(item.getQuantityMw()).multiply(new BigDecimal(1e6))).reduce(BigDecimal.ZERO,BigDecimal::add));
    }

    public void mergeContractBusinessItem(List<? extends ContractAppPcCommonBusinessItemDTO> contractItemResAppDTOS, IntentOrderPO intentOrderPO) {
//        if(intentOrderPO!=null&&intentOrderPO.getCreatedTime().isBefore(LocalDateTimeUtil.parse(multiPowerBeginTime,"yyyy-MM-dd HH:mm:ss"))){
//            //多功率发货上线之前的订单 不做处理
//            return;
//        }
//        List<ContractItemResAppDTO> contractItemResAppDTOS= contractDetailResAppDTO.getContractItemResAppDTOS();
        contractItemResAppDTOS.forEach(item->{
            if(StringUtils.isBlank(item.getSfParentItemId())){
                item.setSfParentItemId("0");
            }
        });

        //以sfParentItemId作为父节点id构建成一棵树
        Map<String, ContractAppPcCommonBusinessItemDTO> sfItemId2ContractItemResAppDTO = contractItemResAppDTOS.stream().collect(Collectors.toMap(ContractAppPcCommonBusinessItemDTO::getSfContractItemId, Function.identity()));
        for (ContractAppPcCommonBusinessItemDTO contractItemResAppDTO : contractItemResAppDTOS) {
            if (!ObjectUtil.equals(contractItemResAppDTO.getSfParentItemId(),"0")) {
                ContractAppPcCommonBusinessItemDTO parentContractItemResAppDTO = sfItemId2ContractItemResAppDTO.get(contractItemResAppDTO.getSfParentItemId());
                if (ObjectUtil.isNotEmpty(parentContractItemResAppDTO)) {
                    if(parentContractItemResAppDTO.getChildren()==null){
                        parentContractItemResAppDTO.setChildren(new ArrayList<>());
                    }
                    parentContractItemResAppDTO.getChildren().add(contractItemResAppDTO);
                }
            }
        }

        //合并contractItemResAppDTOS中children的quantityP,quantityMw,toBeExecutedQuantityP,并且支持递归到children的children
        for (ContractAppPcCommonBusinessItemDTO contractItemResAppDTO : contractItemResAppDTOS) {
            contractItemResAppDTO.setQuantityP(mergeQuantityP(contractItemResAppDTO));
            contractItemResAppDTO.setQuantityMw(mergeQuantityMw(contractItemResAppDTO));
            contractItemResAppDTO.setToBeExecutedQuantityP(mergeToBeExecutedQuantityP(contractItemResAppDTO));
        }


//        contractDetailResAppDTO.setContractItemResAppDTOS(contractItemResAppDTOS.stream().filter(item->ObjectUtil.equals(item.getSfParentItemId(),"0")).collect(Collectors.toList()));
    }

    /**
     * 使用快照数据替换产品行部分信息
     * @param parentContractItems
     */
    public void fillParentItemsBySnapshot(List<? extends ContractAppPcCommonBusinessItemDTO> parentContractItems) {
        if(CollUtil.isEmpty(parentContractItems)){
            return;
        }
        List<ContractBusinessItemSnapshotPO> contractBusinessItemPOs = contractBusinessItemSnapshotMapperService.list(new QueryWrapper<ContractBusinessItemSnapshotPO>().lambda()
                .eq(ContractBusinessItemSnapshotPO::getContractId, parentContractItems.get(0).getContractId()));

        if(CollUtil.isEmpty(contractBusinessItemPOs)){
            return;
        }
        Map<String,ContractBusinessItemSnapshotPO> snapShotPOBySfIdMap=contractBusinessItemPOs.stream().collect(Collectors.toMap(ContractBusinessItemSnapshotPO::getSfContractItemId,  Function.identity()));

        parentContractItems.forEach(item->{
            if(snapShotPOBySfIdMap.containsKey(item.getSfContractItemId())){
                //片数和MW数使用快照替换
                item.setQuantityP(snapShotPOBySfIdMap.get(item.getSfContractItemId()).getQuantityP());
                item.setQuantityMw(snapShotPOBySfIdMap.get(item.getSfContractItemId()).getQuantityMw());
            }
        });
    }

    private Integer mergeToBeExecutedQuantityP(ContractAppPcCommonBusinessItemDTO contractItemResAppDTO) {
        if(CollUtil.isNotEmpty(contractItemResAppDTO.getChildren())){
            Integer childrenToBeExecutedQuantityP = contractItemResAppDTO.getChildren().stream().map(item->mergeToBeExecutedQuantityP(item)).reduce(new Integer(0),Integer::sum);
            return contractItemResAppDTO.getToBeExecutedQuantityP()+childrenToBeExecutedQuantityP;
        }else{
            return contractItemResAppDTO.getToBeExecutedQuantityP();
        }
    }

    private BigDecimal mergeQuantityMw(ContractAppPcCommonBusinessItemDTO contractItemResAppDTO) {
        if(CollUtil.isNotEmpty(contractItemResAppDTO.getChildren())){
            BigDecimal childrenQuantityMw = contractItemResAppDTO.getChildren().stream().map(item->mergeQuantityMw(item)).reduce(BigDecimal.ZERO, BigDecimal::add);
            return contractItemResAppDTO.getQuantityMw().add(childrenQuantityMw);
        }else{
            return contractItemResAppDTO.getQuantityMw();
        }
    }

    private BigDecimal mergeQuantityP(ContractAppPcCommonBusinessItemDTO contractItemResAppDTO) {
        if(CollUtil.isNotEmpty(contractItemResAppDTO.getChildren())){
            BigDecimal childrenQuantityP = contractItemResAppDTO.getChildren().stream().map(item->mergeQuantityP(item)).reduce(BigDecimal.ZERO, BigDecimal::add);
            return contractItemResAppDTO.getQuantityP().add(childrenQuantityP);
        }else{
            return contractItemResAppDTO.getQuantityP();
        }
    }

    private List<ContractChangeResPcDTO> changeInfoInit(String sfContractNo) {

        // 查询子合同（变更合同）
        List<ContractPO> childrenContractPOS = contractMapperService.listBySfLastContractNos(Lists.newArrayList(sfContractNo));
        List<String> contractIds02 = childrenContractPOS.stream().map(ContractPO::getContractId).collect(Collectors.toList());
        List<ContractBusinessPO> contractBusinessPOS = contractBusinessMapperService.listByContractIds(contractIds02);
        Map<String, ContractBusinessPO> contractId2contractBusinessPO = contractBusinessPOS.stream().collect(Collectors.toMap(ContractBusinessPO::getContractId, Function.identity()));
        // 批量查询付款条件
        List<String> paymentTermsList = contractBusinessPOS.stream().map(ContractBusinessPO::getPaymentTerms).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        Map<String, String> optionValue2desc = getOptionValue2desc(OptionGroupConstant.PAYMENT_TERM, paymentTermsList);

        return childrenContractPOS.stream().map(e -> {
            ContractBusinessPO contractBusinessPO = contractId2contractBusinessPO.get(e.getContractId());
            return ContractChangeResPcDTO.builder()
                    .contractStatus(e.getContractStatus())
                    .contractStatusText(ContractStatusEnum.getDescByCode(e.getContractStatus()))
                    .contractId(e.getContractId())
                    .sfContractNo(e.getSfContractNo())
                    .effectEnd02(getLocalDateWithNull(e.getEffectEnd()))
                    .effectStart02(getLocalDateWithNull(e.getEffectStart()))
                    .salesInternalUserName(contractBusinessPO.getSalesInternalUserName())
                    .enterpriseName(e.getEnterpriseName())
                    .applyDate(getLocalDateWithNull(e.getCreatedTime()))
                    .paymentTerms(contractBusinessPO.getPaymentTerms())
                    .paymentTermsText(optionValue2desc.get(contractBusinessPO.getPaymentTerms()))
                    .totalAmountRmb(contractBusinessPO.getTotalAmountRmb())
                    .tax(contractBusinessPO.getTax())
                    .taxText(getTaxText(contractBusinessPO.getTax()))
                    .totalVolumeMw(contractBusinessPO.getTotalVolumeMw())
                    .build();
        }).collect(Collectors.toList());
    }


    public void fillContractResPc02DTO(List<ContractResPc02DTO> contractResPc02DTOS) {
        // 批量查询付款条件
        List<String> paymentTermsList = contractResPc02DTOS.stream().map(ContractResPc02DTO::getPaymentTerms).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        Map<String, String> optionValue2desc = getOptionValue2desc(OptionGroupConstant.PAYMENT_TERM, paymentTermsList);
        for (ContractResPc02DTO e : contractResPc02DTOS) {
            e.setContractStatusText(ContractStatusEnum.getDescByCode(e.getContractStatus()));
            e.setTaxText(getTaxText(e.getTax()));
            e.setPaymentTermsText(optionValue2desc.get(e.getPaymentTerms()));
            e.setSignEntity(StringUtils.isEmpty(e.getCapitalEnterpriseName()) ? e.getEnterpriseName() : e.getCapitalEnterpriseName());
        }
    }

    public void fillContractDetailResPcDTO(ContractDetailResPcDTO contractDetailResPcDTO) {
        contractDetailResPcDTO.setStatusBeforeChangeText(ContractStatusEnum.getDescByCode(contractDetailResPcDTO.getStatusBeforeChange()));
        contractDetailResPcDTO.setIndustryLevelOneName(contractManager.getOptionDesc(OptionGroupConstant.INDUSTRY_ATTRIBUTE, contractDetailResPcDTO.getIndustryLevelOne()));
        contractDetailResPcDTO.setIndustryLevelTwoName(contractManager.getOptionDesc(OptionGroupConstant.INDUSTRY_ATTRIBUTE, contractDetailResPcDTO.getIndustryLevelTwo()));
        contractDetailResPcDTO.setSfRecordTypeText(contractManager.getOptionDesc(OptionGroupConstant.CONTRACT_RECORD_TYPE, contractDetailResPcDTO.getSfRecordType()));
        contractDetailResPcDTO.setContractChangeResPcDTOS(changeInfoInit(contractDetailResPcDTO.getSfContractNo()));
    }
}
