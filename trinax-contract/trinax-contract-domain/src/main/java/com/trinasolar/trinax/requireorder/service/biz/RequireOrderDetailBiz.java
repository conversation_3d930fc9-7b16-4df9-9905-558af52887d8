package com.trinasolar.trinax.requireorder.service.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.requireorder.constants.enums.OpportunitySegmentEnum;
import com.trinasolar.trinax.requireorder.constants.enums.RequireOrderStatusEnum;
import com.trinasolar.trinax.requireorder.constants.enums.TrackerTypeEnum;
import com.trinasolar.trinax.requireorder.dto.input.RequireOrderDetailReqDTO;
import com.trinasolar.trinax.requireorder.dto.output.RequireOrderDetailRespDTO;
import com.trinasolar.trinax.requireorder.dto.output.RequireOrderFileResDTO;
import com.trinasolar.trinax.requireorder.manager.RequireGetSubordinateManager;
import com.trinasolar.trinax.requireorder.manager.RequireOrganizationManager;
import com.trinasolar.trinax.requireorder.manager.RequireUserManager;
import com.trinasolar.trinax.requireorder.repository.atomicservice.RequireOrderFileMapperService;
import com.trinasolar.trinax.requireorder.repository.atomicservice.RequireOrderMapperService;
import com.trinasolar.trinax.requireorder.repository.dao.input.RequireOrderQueryObjectDAO;
import com.trinasolar.trinax.requireorder.repository.po.RequireOrderFilePO;
import com.trinasolar.trinax.requireorder.repository.po.RequireOrderPO;
import com.trinasolar.trinax.user.api.SysDealerSalesRelationFeign;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.SysOrganizationTypeEnum;
import com.trinasolar.trinax.user.constants.SysUserStatusEnum;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import com.trinasolar.trinax.user.dto.input.SysUserQueryDTO;
import com.trinasolar.trinax.user.dto.output.SysDealerSalesRelationRespDTO;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
public class RequireOrderDetailBiz {

    @Autowired
    private RequireOrderMapperService requireOrderMapperService;

    @Autowired
    private RequireOrderFileMapperService requireOrderFileMapperService;

    @Autowired
    private SysUserFeign sysUserFeign;

    @Autowired
    private RequireUserManager requireUserManager;

    @Autowired
    private RequireOrganizationManager requireOrganizationManager;

    @Autowired
    private RequireGetSubordinateManager requireGetSubordinateManager;

    @Autowired
    private SysDealerSalesRelationFeign sysDealerSalesRelationFeign;


    public RequireOrderDetailRespDTO findRequireOrderDetail(RequireOrderDetailReqDTO reqDTO) {
        RequireOrderPO requireOrderPO = requireOrderMapperService.getByRequireOrderNo(reqDTO.getRequireOrderNo());
        if(ObjectUtil.isEmpty(requireOrderPO)){
            return null;
        }
        RequireOrderDetailRespDTO respDTO = BeanUtil.toBean(requireOrderPO,RequireOrderDetailRespDTO.class);
        //查询详情时，需要需求单当前用户是否有权限查看
        //当前用户是否是管理员角色
        boolean isAdmin = requireUserManager.isAdmin(reqDTO.getCurrentUserId());

        //管理员能够看所有的，不做控制
        if (!isAdmin) {
            //其他用户走权限验证：无权限时直接返回空
            if (!permissionCheck(respDTO, reqDTO)) {
                return null;
            }
        }
        //构造返回内容
        generateResp(respDTO,reqDTO.getRequireOrderNo());
        return respDTO;
    }

    /**
     * 权限校验，有数据权限返回true；无数据权限返回false
     *
     * @return
     */
    private boolean permissionCheck(RequireOrderDetailRespDTO respDTO, RequireOrderDetailReqDTO reqDTO) {
        // 当前人是business_external_user_id;内外部跟进人;需求所有人 能看数据
        if(reqDTO.getCurrentUserId().equals(respDTO.getBusinessExternalUserId())
        || reqDTO.getCurrentUserId().equals(respDTO.getPartnerUserId())
        || reqDTO.getCurrentUserId().equals(respDTO.getSalesInternalUserId())
        || reqDTO.getCurrentUserId().equals(respDTO.getRequireOrderOwnerId())){
            return true;
        }
        //非管理员区分内外部用户分别校验
        if (SysUserTypeEnum.INTERNAL.getType().equals(reqDTO.getCurrentUserType())) {
            String orgType = requireOrganizationManager.getOrgTypeByUserId(reqDTO.getCurrentUserId());
            //用户组织类型为common时，查询所有类型为销售的业务组织编码
            if (SysOrganizationTypeEnum.COMMON.getCode().equals(orgType)) {
                List<String> subUserIdList = requireGetSubordinateManager.getOperationSubUserList(reqDTO.getCurrentUserId());
                subUserIdList.add(reqDTO.getCurrentUserId());
                return subUserIdList.contains(respDTO.getSalesInternalUserId())
                        || subUserIdList.contains(respDTO.getOperationInternalUserId());
            } else if (SysOrganizationTypeEnum.SALES.getCode().equals(orgType)) {
                List<RequireOrderQueryObjectDAO> queryObjectDAOS = requireGetSubordinateManager.getSalesSubordinate(reqDTO.getCurrentUserId());
                List<RequireOrderQueryObjectDAO> permissionList = queryObjectDAOS.stream()
                        .filter(e -> e.getUserId().equals(respDTO.getSalesInternalUserId()) && e.getBizOrganizationCode().equals(respDTO.getBizOrganizationCode()))
                        .collect(Collectors.toList());
                return !CollectionUtils.isEmpty(permissionList);
            } else if (SysOrganizationTypeEnum.OPERATION.getCode().equals(orgType)) {
                List<String> operationUserIdList = requireGetSubordinateManager.getOperationSubUserList(reqDTO.getCurrentUserId());
                //兼容返回运营人员为空的情况
                operationUserIdList.add(reqDTO.getCurrentUserId());
                return operationUserIdList.contains(respDTO.getOperationInternalUserId());
            } else {
                return false;
            }
        } else {
            List<RequireOrderQueryObjectDAO> queryObjectDAOS = requireGetSubordinateManager.getDealerSubordinate(reqDTO.getCurrentUserId());
            List<RequireOrderQueryObjectDAO> permissionList = queryObjectDAOS.stream()
                    .filter(e -> e.getUserId().equals(respDTO.getBusinessExternalUserId()) && e.getEnterpriseId().equals(respDTO.getEnterpriseId()))
                    .collect(Collectors.toList());
            return !CollectionUtils.isEmpty(permissionList);
        }
    }

    /**
     * 构造返回内容
     * @param respDTO
     * @param orderNo
     */
    private void generateResp(RequireOrderDetailRespDTO respDTO,String orderNo){
        respDTO.setRequireOrderStatusText(RequireOrderStatusEnum.getDescByCode(respDTO.getRequireOrderStatus()));
        respDTO.setOpportunitySegmentText(OpportunitySegmentEnum.getDescByCode(respDTO.getOpportunitySegment()));

        List<String> userIdList = new ArrayList<>();
        userIdList.add(respDTO.getBusinessExternalUserId());
        if(TrackerTypeEnum.SALES.getCode().equals(respDTO.getTrackerUserType())){
            userIdList.add(respDTO.getSalesInternalUserId());
        }else{
            userIdList.add(respDTO.getPartnerUserId());
        }

        if(CollUtil.isNotEmpty(userIdList)){
            SysUserQueryDTO queryDTO = new SysUserQueryDTO();
            queryDTO.setUserIdList(userIdList);
            Result<List<SysUserRespDTO>> listResult = sysUserFeign.listUser(queryDTO);
            if(Boolean.TRUE.equals(listResult.getSuccess()) && !CollectionUtils.isEmpty(listResult.getData())){
                Map<String,List<SysUserRespDTO>> map = listResult.getData().stream().collect(Collectors.groupingBy(SysUserRespDTO::getUserId));
                if (StringUtils.isNotBlank(respDTO.getBusinessExternalUserId())
                        && !CollectionUtils.isEmpty(map.get(respDTO.getBusinessExternalUserId()))
                        && !SysUserStatusEnum.WITHDRAW.getCode().equals(map.get(respDTO.getBusinessExternalUserId()).get(0).getStatus())) {
                    respDTO.setBusinessExternalUserPhone(map.get(respDTO.getBusinessExternalUserId()).get(0).getMobile());
                }
                if (StringUtils.isNotBlank(respDTO.getSalesInternalUserId())
                        && !CollectionUtils.isEmpty(map.get(respDTO.getSalesInternalUserId()))
                        && !SysUserStatusEnum.WITHDRAW.getCode().equals(map.get(respDTO.getSalesInternalUserId()).get(0).getStatus())) {
                    respDTO.setTrackerUserPhone(map.get(respDTO.getSalesInternalUserId()).get(0).getMobile());
                }
                if (StringUtils.isNotBlank(respDTO.getPartnerUserId())
                        && !CollectionUtils.isEmpty(map.get(respDTO.getPartnerUserId()))
                        && !SysUserStatusEnum.WITHDRAW.getCode().equals(map.get(respDTO.getPartnerUserId()).get(0).getStatus())) {
                    respDTO.setTrackerUserPhone(map.get(respDTO.getPartnerUserId()).get(0).getMobile());
                }
            }
        }
        //意向单提交用户与企业关系认证状态
        if(StringUtils.isNotBlank(respDTO.getEnterpriseId()) && StringUtils.isNotBlank(respDTO.getBusinessExternalUserId())){
            Result<List<SysDealerSalesRelationRespDTO>> listResult = sysDealerSalesRelationFeign.listByEnterpriseIdAndDealerUserId(respDTO.getEnterpriseId(),respDTO.getBusinessExternalUserId());
            if(CollUtil.isNotEmpty(listResult.getData())){
                respDTO.setAuthStatus(listResult.getData().get(0).getAuthStatus());
            }
        }

        List<RequireOrderFilePO> filePOList = requireOrderFileMapperService.getByRequireOrderNo(orderNo);
        List<RequireOrderFileResDTO> fileList = BeanUtil.copyToList(filePOList, RequireOrderFileResDTO.class);
        respDTO.setFileList(fileList);
    }

}
