package com.trinasolar.trinax.contract.service.biz;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.contract.constants.enums.ContractStatusEnum;
import com.trinasolar.trinax.contract.dto.input.contract.sales.ContractItemReportResDTO;
import com.trinasolar.trinax.contract.dto.output.contract.sales.ApprovedUnCountersignedContractDTO;
import com.trinasolar.trinax.contract.dto.output.contract.sales.CountersignedUnDeliveryContractDTO;
import com.trinasolar.trinax.intentorder.constants.enums.ProductNameEnum;
import com.trinasolar.trinax.partner.api.EnterpriseBizRelationFeign;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.dto.output.EnterpriseBizRelationResDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseDTO;
import com.trinasolar.trinax.user.api.SysOrganizationFeign;
import com.trinasolar.trinax.user.dto.output.SysOrganizationRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.*;
import java.util.stream.Collectors;


@RequiredArgsConstructor
@Service
@Slf4j
public class StatisticFillBiz {

    private final SysOrganizationFeign sysOrganizationFeign;

    private final EnterpriseBizRelationFeign enterpriseBizRelationFeign;

    private final EnterpriseFeign enterpriseFeign;

    public void fillApprovedUnCountersignedContractDTO(List<ApprovedUnCountersignedContractDTO> approvedUnCountersignedContractDTOS) {
        if (ObjectUtil.isEmpty(approvedUnCountersignedContractDTOS)) {
            return;
        }
        Map<String, String> statusCode2desc = Arrays.stream(ContractStatusEnum.values()).collect(Collectors.toMap(ContractStatusEnum::getCode, ContractStatusEnum::getDesc));
        Result<List<SysOrganizationRespDTO>> listResult = sysOrganizationFeign.listOrgByOrganizationCodes(approvedUnCountersignedContractDTOS.stream().map(ApprovedUnCountersignedContractDTO::getBizOrganizationCode).collect(Collectors.toList()));
        Map<String, String> organizationCode2name = listResult.getData().stream().collect(Collectors.toMap(SysOrganizationRespDTO::getOrganizationCode, SysOrganizationRespDTO::getOrganizationName));
        LocalDate today = LocalDate.now();
        for (ApprovedUnCountersignedContractDTO e : approvedUnCountersignedContractDTOS) {
            e.setYear(e.getApprovedTime().getYear());
            e.setContractStatusStr(statusCode2desc.get(e.getContractStatus()));
            e.setBizOrganizationName(organizationCode2name.get(e.getBizOrganizationCode()));
            long days = ChronoUnit.DAYS.between(e.getApprovedTime().toLocalDate(), today);
            e.setDays(days);
            e.setCreatedTimeStr(DateUtil.format(e.getCreatedTime(), DatePattern.NORM_DATE_PATTERN));
        }
    }

    public void fillCountersignedUnDeliveryContractDTO(List<CountersignedUnDeliveryContractDTO> countersignedUnDeliveryContractDTOS) {
        if (ObjectUtil.isEmpty(countersignedUnDeliveryContractDTOS)) {
            return;
        }
        Map<String, String> statusCode2desc = Arrays.stream(ContractStatusEnum.values()).collect(Collectors.toMap(ContractStatusEnum::getCode, ContractStatusEnum::getDesc));
        Result<List<SysOrganizationRespDTO>> listResult = sysOrganizationFeign.listOrgByOrganizationCodes(countersignedUnDeliveryContractDTOS.stream().map(CountersignedUnDeliveryContractDTO::getBizOrganizationCode).collect(Collectors.toList()));
        Map<String, String> organizationCode2name = listResult.getData().stream().collect(Collectors.toMap(SysOrganizationRespDTO::getOrganizationCode, SysOrganizationRespDTO::getOrganizationName));
        LocalDate today = LocalDate.now();
        for (CountersignedUnDeliveryContractDTO e : countersignedUnDeliveryContractDTOS) {
            e.setYear(e.getCounterSignedDate().getYear());
            e.setContractStatusStr(statusCode2desc.get(e.getContractStatus()));
            e.setBizOrganizationName(organizationCode2name.get(e.getBizOrganizationCode()));
            long days = ChronoUnit.DAYS.between(e.getCounterSignedDate().toLocalDate(), today);
            e.setDays(days);
            e.setCreatedTimeStr(DateUtil.format(e.getCreatedTime(), DatePattern.NORM_DATE_PATTERN));
        }
    }

    public void fillReportInfoContractDTO(List<ContractItemReportResDTO> records) {
        List<String> enterpriseIds=records.stream().map(ContractItemReportResDTO::getEnterpriseId).collect(Collectors.toList());
        Result<List<EnterpriseBizRelationResDTO>> result= enterpriseBizRelationFeign.listByEnterpriseIds(enterpriseIds);
        Assert.isTrue(result.getSuccess(),"查询企业所属战区失败！");
        final Map<String,String> et2BizCodeMap=new HashMap<>();
        final Map<String,String> organizationCode2name=new HashMap<>();
        final Map<String,String> ent2IndustryMap=new HashMap<>();
        if(!CollectionUtils.isEmpty(result.getData())){
            List<EnterpriseBizRelationResDTO> enterpriseBizRelationResDTOS=result.getData();
            et2BizCodeMap.putAll(enterpriseBizRelationResDTOS.stream().collect(Collectors.toMap(EnterpriseBizRelationResDTO::getEnterpriseId,EnterpriseBizRelationResDTO::getBizOrganizationCode)));
            Set<String> orgCodeSets=enterpriseBizRelationResDTOS.stream().map(EnterpriseBizRelationResDTO::getBizOrganizationCode).collect(Collectors.toSet());

            Result<List<SysOrganizationRespDTO>> listResult = sysOrganizationFeign.listOrgByOrganizationCodes(orgCodeSets.stream().collect(Collectors.toList()));
            organizationCode2name.putAll(listResult.getData().stream().collect(Collectors.toMap(SysOrganizationRespDTO::getOrganizationCode, SysOrganizationRespDTO::getOrganizationName)));
        }

        Result<List<EnterpriseDTO>> listResult=enterpriseFeign.listByEnterpriseIds(enterpriseIds);
        Assert.isTrue(listResult.getSuccess(),"查询企业信息失败！");
        if(!CollectionUtils.isEmpty(listResult.getData())){
            ent2IndustryMap.putAll(listResult.getData().stream().filter(item->StringUtils.isNotBlank(item.getIndustry())).collect(Collectors.toMap(EnterpriseDTO::getEnterpriseId,EnterpriseDTO::getIndustry)));
        }

        Map<String,String> giftProdNameMap=Arrays.stream(ProductNameEnum.values()).sequential().collect(Collectors.toMap(ProductNameEnum::getCode,ProductNameEnum::getDesc));
        records.stream().parallel().forEach(item->{
            //赠品端子名称转换
            if(StringUtils.isNotBlank(item.getProductName())){
                String giftProdName=giftProdNameMap.get(item.getProductName());
                if(giftProdName!=null){
                    item.setProductName(giftProdName);
                }
            }
            //战区转换
            String bizOrganizationCode= et2BizCodeMap.get(item.getEnterpriseId());
//            item.setBizOrganizationCode(et2BizCodeMap.get(item.getEnterpriseId()));
            item.setBizOrganizationName(organizationCode2name.get(bizOrganizationCode));
            item.setIndustry(ent2IndustryMap.get(item.getEnterpriseId()));
        });
    }
}
