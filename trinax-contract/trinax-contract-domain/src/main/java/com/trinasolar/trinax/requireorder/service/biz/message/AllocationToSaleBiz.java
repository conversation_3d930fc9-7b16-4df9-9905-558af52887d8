package com.trinasolar.trinax.requireorder.service.biz.message;

import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.messagetemplate.MessageTemplateNoticeTypeEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoBizCodeEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoOrganizationTypeEnum;
import com.trinasolar.trinax.basic.dto.input.MessageSendCommonReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendTodoReqDTO;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.requireorder.dto.mq.RequireOrderMessageMqDTO;
import com.trinasolar.trinax.requireorder.manager.RequireMessageFillManager;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class AllocationToSaleBiz {

    @Autowired
    private MqManager mqManager;

    @Autowired
    private RequireMessageFillManager requireMessageFillManager;

    @Value("${message.template.rq.allocationToSaleTodo}")
    private String todoTemplateCode;


    public void execute(RequireOrderMessageMqDTO reqDTO) {
        log.info("区域负责人分配需求给销售/客户主销售分配需求给销售,给销售发待办消息入参：{}", reqDTO);
        MessageSendCommonReqDTO todoMessageReqDTO = new MessageSendCommonReqDTO();
        todoMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.TODO.getCode());
        List<MessageSendTodoReqDTO> todoList = new ArrayList<>();

        Map<String, String> content = new HashMap<>();
        content.put("internalUserName", reqDTO.getCurrentUserName());
        content.put("requireOrderNo", reqDTO.getRequireOrderNo());

        MessageSendTodoReqDTO todoReqDTO = requireMessageFillManager.generateTodo(reqDTO,
                todoTemplateCode, TodoBizCodeEnum.RQ_ALLOCATION_TO_SALE.getCode(),
                Collections.singletonList(reqDTO.getSaleInternalUserId()),content, TodoOrganizationTypeEnum.SALES.getCode());

        todoList.add(todoReqDTO);
        todoMessageReqDTO.setTodoList(todoList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC, JacksonUtil.bean2Json(todoMessageReqDTO));
        log.info("区域负责人分配需求给销售/客户主销售分配需求给销售,给销售发待办消息完成，param:{}", JacksonUtil.bean2Json(todoMessageReqDTO));
    }


}
