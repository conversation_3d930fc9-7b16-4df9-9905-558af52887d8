package com.trinasolar.trinax.intentorder.manager;

import com.trinasolar.trinax.basic.dto.input.MessageSendJPushReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendNoticeReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendTodoReqDTO;
import com.trinasolar.trinax.basic.event.MessageTypeEnum;
import com.trinasolar.trinax.intentorder.dto.mq.IntentOrderMessageMqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
@Slf4j
public class IntentOrderMessageManager {
    /**
     * 组装系统通知信息
     */
    public MessageSendNoticeReqDTO generateNotice(IntentOrderMessageMqDTO reqDTO,String opType,String bizCode,
                                                  String templateCode, List<String>userIdList, Map<String,String> content) {
        MessageSendNoticeReqDTO noticeReqDTO = new MessageSendNoticeReqDTO();
        noticeReqDTO.setMessageType(MessageTypeEnum.MSG_TYPE_PRIVATE.getValue());
        noticeReqDTO.setOpType(opType);
        noticeReqDTO.setBizCode(bizCode);
        noticeReqDTO.setTemplateCode(templateCode);
        noticeReqDTO.setUserIdList(userIdList);
        noticeReqDTO.setBizNo(reqDTO.getIntentOrderNo());
        noticeReqDTO.setCreatedBy(reqDTO.getCurrentUserId());
        noticeReqDTO.setCreatedName(reqDTO.getCurrentUserName());
        noticeReqDTO.setContent(content);
        return noticeReqDTO;
    }

    public MessageSendNoticeReqDTO generateNotice2(IntentOrderMessageMqDTO reqDTO, String contractId, String opType,String bizCode,
                                                  String templateCode, List<String>userIdList, Map<String,String> content) {
        MessageSendNoticeReqDTO noticeReqDTO = new MessageSendNoticeReqDTO();
        noticeReqDTO.setMessageType(MessageTypeEnum.MSG_TYPE_PRIVATE.getValue());
        noticeReqDTO.setOpType(opType);
        noticeReqDTO.setBizCode(bizCode);
        noticeReqDTO.setTemplateCode(templateCode);
        noticeReqDTO.setUserIdList(userIdList);
        noticeReqDTO.setBizNo(contractId);
        noticeReqDTO.setCreatedBy(reqDTO.getCurrentUserId());
        noticeReqDTO.setCreatedName(reqDTO.getCurrentUserName());
        noticeReqDTO.setContent(content);
        return noticeReqDTO;
    }

    /**
     * 组装极光消息
     * @param templateCode
     * @param userIdList
     * @param content
     * @return
     */
    public MessageSendJPushReqDTO generateJPush(String templateCode, List<String>userIdList, Map<String,String> content) {
        MessageSendJPushReqDTO push = new MessageSendJPushReqDTO();
        push.setTemplateCode(templateCode);
        push.setUserIdList(userIdList);
        push.setContent(content);
        return push;
    }


    /**
     * 组装待办消息
     * @param reqDTO
     * @param templateCode
     * @param userIdList
     * @param content
     * @return
     */
    public MessageSendTodoReqDTO generateTodo(IntentOrderMessageMqDTO reqDTO,String templateCode,String bizType,
                                              List<String>userIdList, Map<String,String> content,String orgType) {
        MessageSendTodoReqDTO todoReqDTO = new MessageSendTodoReqDTO();
        todoReqDTO.setTemplateCode(templateCode);
        todoReqDTO.setUserIdList(userIdList);
        todoReqDTO.setBizCode(bizType);
        todoReqDTO.setBizNo(reqDTO.getIntentOrderNo());
        todoReqDTO.setOrganizationType(orgType);
        todoReqDTO.setCreatedBy(reqDTO.getCurrentUserId());
        todoReqDTO.setCreatedName(reqDTO.getCurrentUserName());
        todoReqDTO.setContent(content);
        return todoReqDTO;
    }

    public MessageSendTodoReqDTO generateTodo2(IntentOrderMessageMqDTO reqDTO, String contractId, String templateCode, String bizType,
                                              List<String>userIdList, Map<String, String> content, String orgType) {
        MessageSendTodoReqDTO todoReqDTO = new MessageSendTodoReqDTO();
        todoReqDTO.setTemplateCode(templateCode);
        todoReqDTO.setUserIdList(userIdList);
        todoReqDTO.setBizCode(bizType);
        todoReqDTO.setBizNo(contractId);
        todoReqDTO.setOrganizationType(orgType);
        todoReqDTO.setCreatedBy(reqDTO.getCurrentUserId());
        todoReqDTO.setCreatedName(reqDTO.getCurrentUserName());
        todoReqDTO.setContent(content);
        return todoReqDTO;
    }

}
