package com.trinasolar.trinax.contract.domain;

import cn.hutool.core.util.StrUtil;

public class YzhuoTest {
    public static void main(String[] args) {
        System.out.println(StrUtil.toUnderlineCase(",\n" +
                "    isDeleted,\n" +
                "    createdBy,\n" +
                "    createdName,\n" +
                "    createdTime,\n" +
                "    updatedBy,\n" +
                "    updatedName,\n" +
                "    updatedTime,\n" +
                "    contractId,\n" +
                "    sfContractId,\n" +
                "    sfContractFamilyNo,\n" +
                "    sfContractNo,\n" +
                "    sfLastContractNo,\n" +
                "    sfNextContractNo,\n" +
                "    bpmTaskId,\n" +
                "    sfRecordId,\n" +
                "    sfRecordType,\n" +
                "    contractSource,\n" +
                "    contractType,\n" +
                "    effectStart,\n" +
                "    effectEnd,\n" +
                "    contractStatus,\n" +
                "    statusBeforeChange,\n" +
                "    summary,\n" +
                "    comments,\n" +
                "    discardReason,\n" +
                "    isOnlineContract,\n" +
                "    agreementId,\n" +
                "    counterSignedDate,\n" +
                "    sfEnterpriseId,\n" +
                "    enterpriseId,\n" +
                "    enterpriseName,\n" +
                "    enterpriseContactName,\n" +
                "    isSigned,\n" +
                "    capitalEnterpriseId,\n" +
                "    capitalEnterpriseName,\n" +
                "    customerCategory,\n" +
                "    approvedTime,\n" +
                "\n" +
                "\n" +
                "sfOpportunityId,\n" +
                "intentOrderNo,\n" +
                "salesInternalUserId,\n" +
                "salesInternalUserName,\n" +
                "salesInternalUserNo,\n" +
                "bizOrganizationCode,\n" +
                "operationInternalUserId,\n" +
                "operationInternalUserName,\n" +
                "operationInternalUserNo,\n" +
                "currency,\n" +
                "application,\n" +
                "beneficiaryBankId,\n" +
                "marketingAccountId,\n" +
                "taxClassificationId,\n" +
                "tax,\n" +
                "     channel,\n" +
                "    scheduledDeliveryDate,\n" +
                "    requiredDeliveryDate,\n" +
                "    isLinkFrameContract,\n" +
                "    frameContractNo,\n" +
                "    incoterm,\n" +
                "    odm,\n" +
                "    openContract,\n" +
                "    isOpenContract,\n" +
                "    paymentNotes,\n" +
                "    paymentTerms,\n" +
                "    totalAmountRmb,\n" +
                "    totalAmountUsd,\n" +
                "    priceBookId,\n" +
                "    salesRegionCode,\n" +
                "    shippingNotes,\n" +
                "    contractTermsNotes,\n" +
                "    totalVolumeMw,\n" +
                "    industryLevelOne,\n" +
                "    industryLevelTwo,\n" +
                "    countryOfInstallation,\n" +
                "    destinationPortId,\n" +
                "    loadingPortId,\n" +
                "    billToAddressId,\n" +
                "    shipToAddressId,\n" +
                "    arrivalCityCode,\n" +
                "    arrivalProvinceCode,\n" +
                "    arrivalCountryCode,\n" +
                "    subRegionCode,\n" +
                "    externalUserId,\n" +
                "    externalUserName,\n" +
                "    customerSigningStatus,\n" +
                "    delivery,\n" +
                "    rebate,\n" +
                "    drSfId,\n" +
                "    originSalesInternalUserId,\n" +
                "    originOperationInternalUserId,\n" +
                "    originExternalUserId"));
    }
}
