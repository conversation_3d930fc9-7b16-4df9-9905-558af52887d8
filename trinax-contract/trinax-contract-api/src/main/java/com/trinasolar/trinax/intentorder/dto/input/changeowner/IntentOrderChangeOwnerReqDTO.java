package com.trinasolar.trinax.intentorder.dto.input.changeowner;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "意向单ChangeOwner入参")
public class IntentOrderChangeOwnerReqDTO {

    @Schema(description ="主任务编号")
    private String taskNo;

    @Schema(description ="子任务编号")
    private String subTaskNo;

    @Schema(description ="场景编码")
    private String situationCode;

    @Schema(description = "更新人", hidden = true)
    private String updatedBy;

    @Schema(description = "更新人姓名", hidden = true)
    private String updatedName;

    @Schema(description = "原主销售人员ID")
    private String originUserId;

    @Schema(description = "原主销售人员名字")
    private String originUserName;

    @Schema(description = "原主销售人员手机号")
    private String originUserPhone;

    @Schema(description = "新主销售人员ID")
    private String newUserId;

    @Schema(description = "新主销售人员姓名")
    private String newUserName;

    @Schema(description = "新主销售人员手机号")
    private String newUserPhone;

    @Schema(description ="变更用户类型：分销销售，分销运营，区域总，外部用户")
    private String changeType;

    @Schema(description ="changeOwner 条件入参")
    private List<IntentOrderChangeOwnerSubReqDTO> conditionList;

//    @Schema(description ="改变运营时入参")
//    private List<IntentOrderChangeOwnerSubReqDTO> changeOperationList;
//
//    @Schema(description ="改变外部用户时入参")
//    private List<IntentOrderChangeOwnerSubReqDTO> changeExternalList;
}
