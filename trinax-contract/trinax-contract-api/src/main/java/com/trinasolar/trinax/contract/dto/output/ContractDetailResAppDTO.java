package com.trinasolar.trinax.contract.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "合同-详情查询-app端查询")
public class ContractDetailResAppDTO extends ContractResAppDTO {

    @Schema(description = "贸易条款（配送方式）")
    private String incoterm;

    @Schema(description = "贸易条款名称（配送方式）")
    private String incotermName;

    @Schema(description = "行业大类 编码")
    private String industryLevelOne;

    @Schema(description = "行业大类 名称")
    private String industryLevelOneName;

    @Schema(description = "行业小类 编码")
    private String industryLevelTwo;

    @Schema(description = "行业小类 名称")
    private String industryLevelTwoName;

    @Schema(description = "运营人员ID")
    private String operationInternalUserId;

    @Schema(description = "运营人员姓名")
    private String operationInternalUserName;

    @Schema(description = "营销账套ID")
    private String marketingAccountId;

    @Schema(description = "营销账套名称")
    private String marketingAccountName;

    @Schema(description = "合同币种")
    private String currency;

    @Schema(description = "要求的交付日期")
    private LocalDate requiredDeliveryDate;

    @Schema(description = "货物到达国家编码")
    private String arrivalCountryCode;

    @Schema(description = "货物到达国家名称（界面上的货物到达区域）")
    private String arrivalCountryName;

    @Schema(description = "货物到达城市编码")
    private String arrivalCityCode;

    @Schema(description = "货物到达城市名称（界面上的地区）")
    private String arrivalCityName;

    @Schema(description = "货物到达省份编码")
    private String arrivalProvinceCode;

    @Schema(description = "货物到达省份名称（界面上的货物到达子区域）")
    private String arrivalProvinceName;

    @Schema(description = "交货区域")
    private String deliveryArea;

    @Schema(description = "pc端查询变更合同")
    List<ContractChangeResPcDTO> contractChangeResPcDTOS;

}
