package com.trinasolar.trinax.contract.dto.output.contract.frame;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class EdContractFramePcResDTO {

    @Schema(description = "至尊宝合同主键ID")
    private String contractId;

    @Schema(description = "合同号")
    private String sfContractNo;

    @Schema(description = "有效期开始")
    private LocalDateTime effectStart;

    @Schema(description = "有效期开始str")
    private String effectStartStr;

    @Schema(description = "有效期结束")
    private LocalDateTime effectEnd;

    @Schema(description = "有效期结束str")
    private String effectEndStr;

    @Schema(description = "合同状态")
    private String contractStatus;

    @Schema(description = "合同状态str")
    private String contractStatusStr;

    @Schema(description = "客户名称")
    private String enterpriseName;

    @Schema(description = "申请日期")
    private LocalDate applyDate;

    @Schema(description = "销售人员姓名")
    private String salesInternalUserName;

    @Schema(description = "是否渠道框架合同 Yes,No")
    private String isChannel;

}
