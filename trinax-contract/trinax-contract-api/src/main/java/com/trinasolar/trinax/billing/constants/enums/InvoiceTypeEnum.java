package com.trinasolar.trinax.billing.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum InvoiceTypeEnum {

    SPECIAL_VAT("VAT Invoice (Special)", "增值税专用发票"),
    VAT("VAT Invoice", "增值税普通发票"),
    VAT_FOR_EXPORT("VAT special invoice for export", "增值税出口商品专用发票"),

    ;
    private final String code;
    private final String desc;

    public static String getDescByCode(String code) {
        for (InvoiceTypeEnum value : InvoiceTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
