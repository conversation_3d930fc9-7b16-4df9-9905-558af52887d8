package com.trinasolar.trinax.billing.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "开票申请产品查询 Req")
public class InvoiceProductQryReqDTO {
    @Schema(description = "当前登录人用户 id")
    private String currentUserId;

    @Schema(description = "当前登录人用户 id")
    private String currentUserName;

    @Schema(description = "当前登录人用户类型")
    private String currentUserType;

    @Schema(description = " 发票 Id")
    @NotBlank(message = "invoiceApplicationId 不能为空")
    private String invoiceApplicationId;

}
