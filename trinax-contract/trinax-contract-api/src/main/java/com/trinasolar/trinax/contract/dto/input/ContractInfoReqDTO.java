package com.trinasolar.trinax.contract.dto.input;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trinasolar.trinax.integration.dto.input.oa.SaleContractToOaReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "合同接入-integration")
public class ContractInfoReqDTO {

    @NotNull
    @Schema(description = "创建人/SF ID")
    private String createdById;

    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdDate;

    @NotNull
    @Schema(description = "上次修改人/SF ID")
    private String lastModifiedById;

    @Schema(description = "上次修改人名字")
    private String lastModifiedByName;

    @Schema(description = "上次修改时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastModifiedDate;

    @NotNull
    @Schema(description = "TS合同id")
    private String contractId;

    @NotNull
    @Schema(description = "合同编号")
    private String contractNo;

    @NotNull
    @Schema(description = "变更合同编号")
    private String contractVersion;

    @Schema(description = "上一版合同号")
    private String originContract;

    @NotNull
    @Schema(description = "记录id")
    private String recordId;

    @NotNull
    @Schema(description = "记录类型编码")
    private String recordType;

    @NotNull
    @Schema(description = "运营人员id/SF ID")
    private String operationInternalUserId;

    @NotNull
    @Schema(description = "客户MDM编号")
    private String accountId;

    @Schema(description = "合同有效期自")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private LocalDate effectStart;

    @Schema(description = "合同有效期至")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private LocalDate effectEnd;

    @NotNull
    @Schema(description = "状态")
    private String status;

    @NotNull
    @Schema(description = "变更前状态")
    private String statusBeforeChange;

    @NotNull
    @Schema(description = "BPM审批单号")
    private String approvedTaskId;

    @NotNull
    @Schema(description = "备注")
    private String comments;

    @NotNull
    @Schema(description = "联系人名称")
    private String contactName;

    @NotNull
    @Schema(description = "合同源")
    private String contractSource;

    @NotNull
    @Schema(description = "评审评论")
    private String contractSummary;

    @NotNull
    @Schema(description = "合同类型")
    private String contractType;

    @Schema(description = "合同双签时间:yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private LocalDate counterSignedDate;

    @NotNull
    @Schema(description = "终止原因")
    private String discardReason;

    @NotNull
    @Schema(description = "ERP销售账套ID")
    private String erpOperatingUnit;

    @NotNull
    @Schema(description = "ERP Tax类型ID")
    private String erpTaxClassificationId;

    @NotNull
    @Schema(description = "税率")
    private String tax;

    @NotNull
    @Schema(description = "是否关联框架合同")
    private String isFromFrameContract;

    @Schema(description = "关联框架合同编号")
    private String frameContractNo;

    @NotNull
    @Schema(description = "地区编码")
    private String area;

    @NotNull
    @Schema(description = "货物到达区域编码")
    private String goodsArrivalRegion;

    @NotNull
    @Schema(description = "货物到达子区域编码")
    private String goodsArrivalSubRegion;

    @NotNull
    @Schema(description = "贸易条款编码")
    private String incoterm;

    @NotNull
    @Schema(description = "ODM")
    private String odm;

    @NotNull
    @Schema(description = "开口合同")
    private String openContract;

    @NotNull
    @Schema(description = "业务机会ID")
    private String opportunityId;

    @NotNull
    @Schema(description = "付款条件备注")
    private String paymentNotes;

    @NotNull
    @Schema(description = "付款条件编码")
    private String paymentTerms;

    @NotNull
    @Schema(description = "合同条款备注")
    private String termsRemark;

    @NotNull
    @Schema(description = "总金额（RMB）")
    private BigDecimal totalAmountRmb;

    @NotNull
    @Schema(description = "总金额（USD）")
    private BigDecimal totalAmountUsd;

    @NotNull
    @Schema(description = "价格手册ID")
    private String priceBookId;

    @NotNull
    @Schema(description = "销售区域编码")
    private String salesRegionCode;

    @NotNull
    @Schema(description = "销售人员ID")
    private String salesInternalUserId;

    @NotBlank(message = "要求交货日期不能为空")
    @Schema(description = "要求交货日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private LocalDate requiredDeliveryDate;

    @Schema(description = "计划交货日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private LocalDate scheduledDeliveryDate;

    @Schema(description = "发货备注")
    private String shippingNotes;

    @Schema(description = "收货地址Id")
    private String shipToAddress;

    @NotNull
    @Schema(description = "销售子区域")
    private String salesSubRegion;

    @NotNull
    @Schema(description = "总容量")
    private BigDecimal totalVolumeMw;

    @NotNull
    @Schema(description = "行业大类 编码")
    private String industryLevelOne;

    @NotNull
    @Schema(description = "行业小类 编码")
    private String industryLevelTwo;

    @NotNull
    @Schema(description = "应用场景")
    private String application;

    @NotNull
    @Schema(description = "币种编码")
    private String currency;

    @NotNull
    @Schema(description = "收款银行ID/SF ID")
    private String beneficiaryBankId;


    @Schema(description = "收票地址ID/SF ID")
    private String billToAddressId;

    @NotNull
    @Schema(description = "项目安装国家")
    private String countryOfInstallation;

    @NotNull
    @Schema(description = "目的港ID/SF ID")
    private String destinationPortId;

    @NotNull
    @Schema(description = "起运港ID/SF ID")
    private String loadingPortId;

    @NotNull
    @Schema(description = "销售渠道编码")
    private String channel;

    @NotNull
    @Schema(description = "价格手册ID")
    private String pricebookID;

    @Schema(description = "合同文件行")
    private List<ContractFileItems> contractFileItems;

    @Schema(description = "合同产品行")
    private List<ContractProductItems> contractProductItems;

    @Schema(description = "是否包含返利,true:包含返利,false:不包含返利")
    private Boolean rebate;

    @Schema(description = "bpm报文")
    private SaleContractToOaReqDTO bpmInfomation;
}

