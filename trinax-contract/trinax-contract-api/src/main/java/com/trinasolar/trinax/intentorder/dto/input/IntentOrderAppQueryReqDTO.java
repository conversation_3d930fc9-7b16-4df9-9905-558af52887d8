package com.trinasolar.trinax.intentorder.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "意向单APP端分页查询请求入参")
public class IntentOrderAppQueryReqDTO {

    @Schema(description = "客户名称/产品名称/尺寸")
    private String keyword;

    @Schema(description = "意向单状态")
    private String intentOrderStatus;

    @Schema(description = "当前用户id")
    private String currentUserId;

    @Schema(description = "当前用户类型")
    private String currentUserType;

    @Schema(description = "意向单单号：PC端需求新增入参")
    private String intentOrderNo;

    @Schema(description = "公司名称：PC端需求新增入参")
    private String enterpriseName;

    @Schema(description = "关联方：PC端需求新增入参")
    private String capitalEnterpriseName;

    @Schema(description = "创建开始时间：PC端需求新增入参")
    private LocalDateTime createdTimeStart;

    @Schema(description = "创建结束时间：PC端需求新增入参")
    private LocalDateTime createdTimeEnd;

    @Schema(description = "期望交货开始时间：PC端需求新增入参")
    private LocalDateTime expectDeliveryDateStart;

    @Schema(description = "期望交货结束时间：PC端需求新增入参")
    private LocalDateTime expectDeliveryDateEnd;

    @Schema(description = "产品名称：PC端需求新增入参")
    private String productName;

    @Schema(description = "创建人：PC端需求新增入参")
    private String createdBy;

    @Schema(description = "需求量Start：PC端需求新增入参")
    private BigDecimal potentialSalesVolumeStart;

    @Schema(description = "需求量End：PC端需求新增入参")
    private BigDecimal potentialSalesVolumeEnd;

    @Schema(description = "意向单类型 1:普通意向单 2:快速订单")
    private Integer orderType;

}
