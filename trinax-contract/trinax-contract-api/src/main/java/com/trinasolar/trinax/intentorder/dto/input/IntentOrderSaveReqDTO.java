package com.trinasolar.trinax.intentorder.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "意向单保存请求入参")
public class IntentOrderSaveReqDTO {

    @Schema(description = "意向单行信息")
    private List<IntentOrderItemSaveReqDTO> itemList;

    @Schema(description = "企业业务主键ID")
    private String enterpriseId;

    @Schema(description = "意向单公司Name")
    private String enterpriseName;

    @Schema(description = "销售账套Id")
    private String requestedOu;

    @Schema(description = "账套名称")
    private String requestedOuName;

    @Schema(description = "应用场景：业务机会市场分类")
    private String opportunitySegment;

    @Schema(description = "交货区域")
    private String goodsArrivalRegion;

    @Schema(description = "交货子区域")
    private String goodsArrivalSubRegion;

    @Schema(description = "地区")
    private String area;

    @Schema(description = "预计交货日期")
    private LocalDate estimatedDeliveryDate;

    @Schema(description = "期望交货日期")
    private LocalDate expectDeliveryDate;

    @Schema(description = "商机币种")
    private String opportunityCurrency;

    @Schema(description = "内部销售用户userId")
    private String salesInternalUserId;

    @Schema(description = "内部销售用户名字")
    private String salesInternalUserName;

    @Schema(description = "意向单所属用户联系方式")
    private String externalUserPhone;

    @Schema(description = "意向单所属用户名称：外部用户名字")
    private String externalUserName;

    @Schema(description = "意向单所属用户工号：外部用户userId")
    private String externalUserId;

    @Schema(description = "贸易术语-提货类型：自提/非自提")
    private String incoterm;

    @Schema(description = "行业属性")
    private String industryAttributes;

    @Schema(description = "行业属性-中文")
    private String industryAttributesText;

    @Schema(description = "是否代客下单")
    private String isSurrogateOrder;

    @Schema(description = "意向单备注")
    private String remark;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "创建人姓名")
    private String createdName;

    @Schema(description = "创建人电话")
    private String createdPhone;

    @Schema(description = "更新人")
    private String updatedBy;

    @Schema(description = "更新人姓名")
    private String updatedName;

    @Schema(description = "更新人电话")
    private String updatedPhone;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "意向单状态")
    private String status;

    @Schema(description = "意向单编号")
    private String intentOrderNo;

    @Schema(description = "区域|子区域|地区")
    private String fullRegion;

    @Schema(description = "efc")
    private boolean efc;

    @Schema(description = "关联方企业id")
    private String capitalEnterpriseId;

    @Schema(description = "关联方企业名称")
    private String capitalEnterpriseName;

    @Schema(description = "来源业务单号：需求单转意向单时为需求单号")
    private String sourceBizNo;

    @Schema(description = "来源类型")
    private String sourceType;

    @Schema(description = "签约实体的客户类型：如果选择了关联方，就是关联方的企业类型，否则就是选择的企业类型")
    private String customerCategory;

    @Schema(description = "框架合同sf Id")
    private String contractFrameId;

    @Schema(description = "收货联系人")
    private String deliverContactor;

    @Schema(description = "收货联系电话")
    private String deliverContactPhone;

    @Schema(description = "收货地址")
    private String deliverAddress;

    @Schema(description = "是否有多功率发货需求 1:是，0:否")
    private Integer deliverMultiPower;

    @Schema(description = "1:普通意向单 2:快速订单")
    private Integer orderType;

}
