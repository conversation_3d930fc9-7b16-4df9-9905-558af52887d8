package com.trinasolar.trinax.contract.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Created on 2025/2/7, 13:22
 * author: z<PERSON>yang
 */
@Data
public class ContractBusinessItemResDTO {

    @Schema(description = "德勤通合同行主键ID")
    private String contractBusinessItemId;

    @Schema(description = "原始合同行 id")
    private String sfParentItemId;

    @Schema(description = "德勤通合同主键ID")
    private String contractId;

    @Schema(description = "SF商机行编号")
    private String sfOpportunityItemId;

    @Schema(description = "产品类型:组件;备件和其他")
    private String moduleType;

    @Schema(description = "行类型：PRODUCT-产品；COMPLIMENTARY - 赠品")
    private String itemType;

    @Schema(description = "SF合同行ID")
    private String sfContractItemId;
}
