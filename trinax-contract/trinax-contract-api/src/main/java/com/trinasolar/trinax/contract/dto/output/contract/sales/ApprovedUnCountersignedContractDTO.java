package com.trinasolar.trinax.contract.dto.output.contract.sales;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ApprovedUnCountersignedContractDTO {

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "客户名称")
    private String enterpriseName;

    @Schema(description = "合同编号")
    private String sfContractNo;

    @Schema(description = "审批通过的时间", hidden = true)
    private LocalDateTime approvedTime;

    @Schema(description = "意向单编号")
    private String intentOrderNo;

    @Schema(description = "合同状态", hidden = true)
    private String contractStatus;

    @Schema(description = "合同状态str")
    private String contractStatusStr;

    @Schema(description = "销售人员姓名")
    private String salesInternalUserName;

    @Schema(description = "业务数据所属组织CODE", hidden = true)
    private String bizOrganizationCode;

    @Schema(description = "战区")
    private String bizOrganizationName;

    @Schema(description = "运营人员姓名")
    private String operationInternalUserName;

    @Schema(description = "创建时间", hidden = true)
    private LocalDateTime createdTime;

    @ExcelProperty("创建时间str")
    private String createdTimeStr;

    @Schema(description = "总容量(MW)")
    private BigDecimal totalVolumeMw;

    @Schema(description = "审批后几天未双签")
    private long days;



}
