package com.trinasolar.trinax.contract.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "确认订单/取消订单")
public class ContractModifyReqDTO {

    @Schema(description = "合同ID")
    private String contractId;

    @Schema(description = "意向单号")
    private String intentOrderNo;

    @Schema(description = "修改类型")
    private String modifyType;

    private String currentUserId;

    private String currentUserName;
}
