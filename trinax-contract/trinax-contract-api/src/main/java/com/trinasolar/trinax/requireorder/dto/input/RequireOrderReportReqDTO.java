package com.trinasolar.trinax.requireorder.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "需求单报表查询入参")
public class RequireOrderReportReqDTO {

    @Schema(description = "接口类型：新增-1；累计-2",hidden = true)
    private String apiType;

    @Schema(description = "开始时间",hidden = true)
    private LocalDateTime startTime;

    @Schema(description = "结束时间",hidden = true)
    private LocalDateTime endTime;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "需求单状态")
    private String requireOrderStatus;
}
