package com.trinasolar.trinax.contract.dto.output.contract.sales;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Schema(description = "")
@Data
@Accessors(chain = true)
@RequiredArgsConstructor(staticName = "builder")
public class InvoiceContractResDTO {

    @Schema(description = "salesforce合同id")
    private String sfContractNo;

    private List<CoBusinessItem> items;

}
