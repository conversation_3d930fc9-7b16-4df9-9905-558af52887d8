package com.trinasolar.trinax.intentorder.dto.output;

import com.trinasolar.trinax.masterdata.dto.output.ProductModulePowerDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "意向单行返回结果")
public class IntentOrderItemResDTO {

    /**
     * 自增id
     */
    private Long id;

    @Schema(description = "意向单单号")
    private String intentOrderNo;

    @Schema(description = "意向单行行号")
    private String intentOrderItemNo;

    @Schema(description = "意向单行分组id（同一组的产品也赠品groupId相同）")
    private String groupId;

    @Schema(description = "SF商机行编号")
    private String opportunityItemId;

    @Schema(description = "产品ID")
    private String productId;

    @Schema(description = "功率")
    private String power;

    @Schema(description = "输入类型：片-> PCs/ASP,兆瓦-> Requirement(MW)/ASP")
    private String inputType;

    @Schema(description = "需求量（W）")
    private BigDecimal quantityW;

    @Schema(description = "片数量")
    private Long quantityP;

    @Schema(description = "预计交货日期")
    private LocalDate estimatedDeliveryDate;

    @Schema(description = "需求量(MW)")
    private BigDecimal quantityMw;

    @Schema(description = "安装方式（默认竖装，用户不可见、不可改）")
    private String installation;

    @Schema(description = "安装方式-文本")
    private String installationText;

    @Schema(description = "正极电缆长度（M）")
    private String cableLengthPositive;

    @Schema(description = "负极电缆长度（M）")
    private String cableLengthCathode;

    @Schema(description = "端子规格")
    private String plugConnector;

    @Schema(description = "端子数量")
    private Long plugConnectorQuantity;

    @Schema(description = "参考价（元）：来自PBI")
    private BigDecimal guidePrice;

    @Schema(description = "销售价（元）-确认环节更新或代客下单直接提交")
    private BigDecimal salePrice;

    @Schema(description = "合计价格（参考价）")
    private BigDecimal guideTotalPrice;

    @Schema(description = "合计价格（销售价）")
    private BigDecimal saleTotalPrice;

    @Schema(description = "产品版本：来自PBI")
    private String productVersion;

    @Schema(description = "产品类型:组件;备件和其他")
    private String moduleType;

    @Schema(description = "行类型：PRODUCT-产品；COMPLIMENTARY - 赠品")
    private String itemType;

    @Schema(description = "操作类型")
    private String opType;

    @Schema(description = "主栅数")
    private String busBar;

    @Schema(description = "组件长度")
    private BigDecimal moduleLength;

    @Schema(description = "组件宽度")
    private BigDecimal moduleWidth;

    @Schema(description = "最小输出功率")
    private String minPower;

    @Schema(description = "最大输出功率")
    private String maxPower;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "创建人姓名")
    private String createdName;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "更新人")
    private String updatedBy;

    @Schema(description = "更新人姓名")
    private String updatedName;

    @Schema(description = "更新时间")
    private LocalDateTime updatedTime;

    @Schema(description = "删除标识：1-删除，0-未删除")
    private String isDeleted;

    @Schema(description = "产品图片地址")
    private String productImgUrl;

    @Schema(description = "产品类别")
    private String productCategory;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "EFC")
    private String efcText;

    @Schema(description = "意向单行Salesforce同步状态：DRAFT-草稿；CONFIRMED-已确认")
    private String status;

    @Schema(description = "净价")
    private BigDecimal netPrice;

    @Schema(description = "其他成本价")
    private BigDecimal otherCostPrice;

    @Schema(description = "赠品关系组件id")
    private Long relationId;

    @Schema(description = "开始功率")
    private String multiPowerBegin;

    @Schema(description = "结束功率")
    private String multiPowerEnd;

    private List<ProductModulePowerDTO> modulePowerDTOS;

}
