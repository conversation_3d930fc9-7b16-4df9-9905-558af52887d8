package com.trinasolar.trinax.contract.dto.output.statistic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class StatisticContractMwRespDTO {

    @Schema(description = "产品（族）")
    private String productName;

    @Schema(description = "产品下单总容量（MW）")
    private BigDecimal num;


}
