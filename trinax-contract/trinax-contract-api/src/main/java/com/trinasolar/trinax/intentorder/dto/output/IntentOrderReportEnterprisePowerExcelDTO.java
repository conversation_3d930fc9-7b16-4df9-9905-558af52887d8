package com.trinasolar.trinax.intentorder.dto.output;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "意向单最多最少客户报表查询导出")
public class IntentOrderReportEnterprisePowerExcelDTO {

    @ExcelProperty("客户")
    private String enterpriseName;

    @ExcelProperty("客户类型")
    private String customerCategory;

    @ExcelIgnore
    private String bizOrganizationCode;

    @ExcelProperty("战区")
    private String bizOrganizationName;

    @ExcelIgnore
    private String fullMonth;

    @ExcelProperty("年")
    private Integer singleYear;

    @ExcelProperty("月")
    private Integer singleMonth;

    @ExcelProperty("总容量（MW）")
    private BigDecimal potentialSalesVolume;
}
