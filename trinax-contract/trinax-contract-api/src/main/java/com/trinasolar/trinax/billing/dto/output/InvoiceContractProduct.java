package com.trinasolar.trinax.billing.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Schema(description = "开票申请列表页 合同 产品")
@RequiredArgsConstructor(staticName = "builder")
@Accessors(chain = true)
public class InvoiceContractProduct {

    @Schema(description = "开票申请单号")
    private String invoiceApplicationId;

    @Schema(description = "合同族号")
    private String sfContractFamilyNo;

    @Schema(description = "中间行表 唯一键")
    private String uniqueId;

    @Schema(description = "功耗")
    private BigDecimal power;

    @Schema(description = "开票数量")
    private Integer quantity;

    @Schema(description = "销售价")
    private String unitPriceW;

    @Schema(description = "产品小图")
    private String productIconUrl;

    @Schema(description = "合计金额 含税")
    private String totalAmount;

    @Schema(description = "行类型：PRODUCT-产品；COMPLIMENTARY - 赠品")
    private String itemType;

    @Schema(description = "开票产品容量(mw)=功率*开票数量")
    private String quantityMw;

    @Schema(description = "是否存在签收单")
    private boolean receiptExists;

    @Schema(description = "产品名称：合同上的 pbi 的产品名")
    private String productName;

    @Schema(description = "发票产品id 唯一主键")
    private String invoiceProductId;

    @Schema(description = "开票录入的产品类型")
    private String invoiceProductType;

    @Schema(description = "产品名称：手动录入的")
    private String invoiceProductName;

    @Schema(description = "产品类型:组件;备件和其他")
    private String moduleType;

    @Schema(description = "返利金额")
    private String rebateAmount;

    @Schema(description = "备注")
    private String comment;
}
