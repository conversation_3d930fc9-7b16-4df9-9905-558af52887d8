package com.trinasolar.trinax.intentorder.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "意向单最多最少客户报表查询反参")
public class IntentOrderReportEnterpriseSummaryResDTO {

    @Schema(description = "意向单数量Top10列表")
    List<IntentOrderReportEnterpriseResDTO> topAmountList;

    @Schema(description = "意向单数量Bottom10列表")
    List<IntentOrderReportEnterpriseResDTO> bottomAmountList;

    @Schema(description = "意向单功率Top10列表")
    List<IntentOrderReportEnterpriseResDTO> topPowerList;

    @Schema(description = "意向单功率Bottom10列表")
    List<IntentOrderReportEnterpriseResDTO> bottomPowerList;
}
