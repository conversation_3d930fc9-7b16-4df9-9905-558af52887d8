package com.trinasolar.trinax.contract.dto.input;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Schema(description = "合同产品行")
public class ContractProductItems {

    @Schema(description = "合同行ID")
    private String sfContractItemId;

    @Schema(description = "商机行ID")
    private String sfOpportunityItemId;

    @Schema(description = "SF产品ID")
    private String sfProductId;

    @Schema(description = "PBI产品ID")
    private String pbiProductId;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "端子")
    private String plugConnector;

    @Schema(description = "安装方式编码")
    private String installation;

    @Schema(description = "线缆长度")
    private String cableLength;

    @Schema(description = "负极电缆长度（M）")
    private BigDecimal cableLengthCathode;

    @Schema(description = "正极电缆长度（M）")
    private BigDecimal cableLengthPositive;

    @Schema(description = "功率")
    private BigDecimal power;

    @Schema(description = "片数量")
    private BigDecimal quantityQ;

    @Schema(description = "容量（MW）")
    private BigDecimal quantityMW;

    @Schema(description = "期望交付时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private LocalDate expectedDeliveryDate;

    @Schema(description = "净价")
    private BigDecimal netPrice;

    @Schema(description = "其他总成本")
    private BigDecimal totalAdditionalCost;

    @Schema(description = "销售价（每瓦）")
    private BigDecimal unitPrice;

    @Schema(description = "销售价（片）")
    private BigDecimal unitPricePieces;

    @Schema(description = "价格手册条目ID")
    private String priceBookEntryId;

    @Schema(description = "备注")
    private String notes;

    @Schema(description = "目的港ID")
    private String destinationPortId;

    @Schema(description = "起运港ID")
    private String loadingPortId;

    @Schema(description = "币种 编码")
    private String currency;

    @Schema(description = "剩余片数量")
    private Integer remainingQuantity;

    @Schema(description = "产品小计金额（含税）")
    private BigDecimal totalAmount;

    @Schema(description = "产品类型")
    private String productCatgory;

    @Schema(description = "EFC")
    private String efc;

    @Schema(description = "组件类型")
    private String moduleType;

    @Schema(description = "行类型")
    private String itemType;

    @Schema(description = "原始合同行 id")
    private String sfParentItemId;

    @Schema(description = "输入方式")
    private String inputType;
}
