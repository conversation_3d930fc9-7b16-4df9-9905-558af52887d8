package com.trinasolar.trinax.contract.dto.output.contract.sales;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Schema(description = "")
@Data
@Accessors(chain = true)
@RequiredArgsConstructor(staticName = "builder")
public class CoBusinessItem {

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "pbi产品id")
    private String pbiProductId;

    @Schema(description = "产品类型")
    private String moduleType;

    @Schema(description = "行类型：PRODUCT-产品；COMPLIMENTARY - 赠品")
    private String itemType;

    @Schema(description = "销售价/瓦")
    private BigDecimal unitPriceW;

    @Schema(description = "销售价/片")
    private BigDecimal unitPriceP;

    @Schema(description = "产品小图")
    private String productIconUrl;

    @Schema(description = "合同 id")
    private String contractId;

    @Schema(description = "mw")
    private String quantityMw;

    @Schema(description = "合同行 id")
    private String contractBusinessItemId;

    @Schema(description = "功耗")
    private BigDecimal power;

    @Schema(description = "合同片数")
    private BigDecimal quantityP;

    @Schema(description = "可开票片数")
    private Integer enableQuantity;
}
