package com.trinasolar.trinax.requireorder.dto.input.changeowner;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "意向单ChangeOwner入参")
public class RequireOrderChangeOwnerSubReqDTO {

    @Schema(description ="企业ID")
    private String enterpriseId;

    @Schema(description ="企业名称")
    private String enterpriseName;

    @Schema(description ="组织编码")
    private String organizationCode;

    @Schema(description ="组织名字")
    private String organizationName;

    @Schema(description ="条件用户id")
    private String userId;

    @Schema(description ="条件用户名")
    private String userName;



}
