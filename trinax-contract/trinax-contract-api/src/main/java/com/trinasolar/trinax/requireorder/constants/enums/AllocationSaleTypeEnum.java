package com.trinasolar.trinax.requireorder.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 需求单分配情况
 */
@AllArgsConstructor
@Getter
public enum AllocationSaleTypeEnum {
    ALLOCATION_SALE ("ALLOCATION_SALE", "分配给销售"),
    ALLOCATION_MANAGER ("ALLOCATION_MANAGER", "分配给区域总"),
    ;

    /**
     * 状态编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    public static String getDescByCode(String code){
        for (AllocationSaleTypeEnum value : AllocationSaleTypeEnum.values()) {
            if (value.getCode().equals(code)){
                return value.getDesc();
            }
        }
        return "";
    }

}
