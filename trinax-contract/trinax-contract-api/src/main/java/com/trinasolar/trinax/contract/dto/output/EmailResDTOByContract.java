package com.trinasolar.trinax.contract.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description ="获取邮件响应参数")
public class EmailResDTOByContract {
    @Schema(description = "发件人")
    private String sender;
    @Schema(description = "收件人")
    private String recipient;
    @Schema(description = "抄送人")
    private String cc;
    @Schema(description = "邮件主题")
    private String subject;
    @Schema(description = "邮件正文")
    private String body;
    @Schema(description = "发送时间")
    private Date sentTime;
}
