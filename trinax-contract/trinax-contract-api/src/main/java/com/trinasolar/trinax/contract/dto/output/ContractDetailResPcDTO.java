package com.trinasolar.trinax.contract.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "合同-详情查询-pc端查询")
public class ContractDetailResPcDTO extends ContractResPcDTO {

    @Schema(description = "变更前状态")
    private String statusBeforeChange;

    @Schema(description = "变更前状态-text")
    private String statusBeforeChangeText;

    @Schema(description = "终止原因")
    private String discardReason;

    @Schema(description = "开口合同")
    private String openContract;

    @Schema(description = "开口合同中文描述")
    private String openContractText;

    @Schema(description = "是否开口合同 1：是  0：否")
    private Integer isOpenContract;

    @Schema(description = "付款条件备注")
    private String paymentNotes;

    @Schema(description = "发货备注")
    private String shippingNotes;

    @Schema(description = "合同条款备注")
    private String contractTermsNotes;

    @Schema(description = "行业大类 编码")
    private String industryLevelOne;

    @Schema(description = "行业大类 名称")
    private String industryLevelOneName;

    @Schema(description = "行业小类 编码")
    private String industryLevelTwo;

    @Schema(description = "行业小类 名称")
    private String industryLevelTwoName;

    @Schema(description = "评审评论")
    private String summary;

    @Schema(description = "合同记录类型")
    private String sfRecordType;

    @Schema(description = "合同记录类型text")
    private String sfRecordTypeText;

    @Schema(description = "备注")
    private String comments;

    @Schema(description = "合同文件列表")
    private List<ContractFileResDTO> contractFileResDTOS;

    @Schema(description = "产品列表")
    private List<ContractItemResPcDTO> contractItemResPcDTOS;

    @Schema(description = "产品快照列表")
    private List<ContractItemResPcDTO> contractItemSnapResPcDTOS;

    @Schema(description = "变更合同列表")
    private List<ContractChangeResPcDTO> contractChangeResPcDTOS;

    @Schema(description = "业务所属战区编码")
    private String bizOrganizationCode;

    @Schema(description = "业务所属战区")
    private String bizOrganizationName;

    @Schema(description = "是否包含返利,true:包含返利,false:不包含返利")
    private Boolean rebate;

    @Schema(description = "返利金额")
    private BigDecimal rebateMoney;
    @Schema(description = "是否预付")
    private Integer isPrePayment;
    @Schema(description = "合同备注")
    private String contractComment;
    @Schema(description = "企业类型")
    private String enterpriseType;
    @Schema(description = "下一审批人")
    private String nextApprover;
    @Schema(description = "已审批人")
    private String approver;
    @Schema(description = "审批节点描述")
    private String approvalNodeDescription;
    @Schema(description = "审批意见")
    private String bpmApprovalOpinions;
    @Schema(description = "审批时间")
    private LocalDateTime bpmApprovedTime;
    @Schema(description = "审批状态")
    private String bpmStatus;
    @Schema(description = "预付天数")
    private Integer prePayDateGap;
    @Schema(description = "预付百分比")
    private String prePayPercent;
    @Schema(description = "付款天数")
    private Integer payDateGap;
    @Schema(description = "合同截止日期")
    private String deadlineDate;
    @Schema(description = "处理天数")
    private Integer handleDateGap;
    @Schema(description = "发货日期")
    private String deliveryDate;

}
