package com.trinasolar.trinax.intentorder.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "驾驶舱返回结果")
public class IntentOrderCockpitResDTO {
    @Schema(description = "意向单数量")
    private BigDecimal intentMount;
    @Schema(description = "意向单确认量,单位MW")
    private BigDecimal potentialSalesVolume;
    @Schema(description = "合同双签量")
    private BigDecimal contractMount;
    @Schema(description = "合同双签量,单位MW")
    private BigDecimal contractSignedMount;
    @Schema(description = "提货量,单位MW")
    private BigDecimal deliverMount;
}
