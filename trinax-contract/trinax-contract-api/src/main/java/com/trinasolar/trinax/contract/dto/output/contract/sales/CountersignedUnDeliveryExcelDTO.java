package com.trinasolar.trinax.contract.dto.output.contract.sales;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class CountersignedUnDeliveryExcelDTO {

    @ExcelProperty("年份")
    private Integer year;

    @ExcelProperty("客户")
    private String enterpriseName;

    @ExcelProperty("意向单编号")
    private String intentOrderNo;

    @ExcelProperty("合同编号")
    private String sfContractNo;

    @ExcelProperty("合同状态")
    private String contractStatusStr;

    @ExcelProperty("所属销售")
    private String salesInternalUserName;

    @ExcelProperty("战区")
    private String bizOrganizationName;

    @ExcelProperty("创建人")
    private String operationInternalUserName;

    @ExcelProperty("合同创建时间")
    private String createdTimeStr;

    @ExcelProperty("总功率(MW)")
    private BigDecimal totalVolumeMw;

    @ExcelProperty("双签后几天未创建发货申请")
    private long days;


}
