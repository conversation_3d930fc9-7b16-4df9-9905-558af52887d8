package com.trinasolar.trinax.intentorder.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * EFC类型类型
 */
@AllArgsConstructor
@Getter
public enum ModuleTypeEnum {
    /**
     * 如果是产品->组件
     * 如果是端子->备件和其他
     */
    MODULE ("Module", "组件"),
    PART ("Part", "备件和其他"),
    ;


    /**
     * 状态编码competitor
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    public static String getDescByCode(String code){
        for (ModuleTypeEnum value : ModuleTypeEnum.values()) {
            if (value.getCode().equals(code)){
                return value.getDesc();
            }
        }
        return "";
    }

}
