package com.trinasolar.trinax.cart.dto.input;

import lombok.Data;

/**
 * <p>
 * 权限处理入参
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Data
public class AuthQueryObjectDAO {

    /**
     * 用户userId
     */
    private String userId;

    /**
     * 用户对应企业id：经销商查询数据时，该字段有值
     */
    private String enterpriseId;

    /**
     * 用户对服务组织：内部用户查询数据时，该字段有值
     */
    private String bizOrganizationCode;



}
