package com.trinasolar.trinax.billing.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "开票申请详情页 Res")
@RequiredArgsConstructor(staticName = "builder")
@Accessors(chain = true)
public class InvoiceApplicationDetailResDTO {

    @Schema(description = "发票申请单号")
    private String invoiceApplicationId;

    @Schema(description = "公司抬头")
    private String enterpriseName;

    @Schema(description = "至尊宝企业 Id")
    private String enterpriseId;

    @Schema(description = "纳税人识别号")
    private String taxNumber;

    @Schema(description = "发票抬头 ID")
    private String headId;

    @Schema(description = "账套 id")
    private String marketingAccountId;

    @Schema(description = "账套 名称")
    private String marketingAccountOu;

    @Schema(description = "发票类型：增值税专用发票 增值税普通发票 增值税出口商品专用发票")
    private String invoiceType;

    @Schema(description = "发票类型 中文名称")
    private String invoiceTypeName;

    @Schema(description = "开票单位")
    private String invoicingUnit;

    @Schema(description = "开票单位 中文")
    private String invoicingUnitStr;

    @Schema(description = "invoicing_unit选其他开票单位时(手动录入)")
    private String otherUnit;

    @Schema(description = "公司抬头 地址")
    private String address;

    @Schema(description = "公司抬头 电话")
    private String mobile;

    @Schema(description = "公司抬头 开户行")
    private String bankName;

    @Schema(description = "公司抬头 银行账号")
    private String bankNo;

    @Schema(description = "德勤联系人")
    private String trinaContactId;

    @Schema(description = "德勤联系人")
    private String trinaContactName;

    @Schema(description = "德勤联系人")
    private String trinaContactMobile;

    @Schema(description = "备注")
    private String comment;

    @Schema(description = "开票申请人 id")
    private String applicantsUserId;

    @Schema(description = "开票申请人名称")
    private String applicantsUserName;

    @Schema(description = "开票申请人电话")
    private String applicantsMobile;

    @Schema(description = "开票申请金额")
    private BigDecimal invoiceApplicationAmount;

    @Schema(description = "开票总容量（mw）")
    private BigDecimal invoiceQuantityMw;

    @Schema(description = "开票申请创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "开票申请更新时间")
    private LocalDateTime updatedTime;

    @Schema(description = "开票申请状态")
    private String applicationStatus;

    @Schema(description = "开票申请状态")
    private String applicationStatusStr;
}
