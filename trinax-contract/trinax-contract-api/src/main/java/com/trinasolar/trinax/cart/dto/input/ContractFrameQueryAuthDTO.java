package com.trinasolar.trinax.cart.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Schema(description = "框架合同-权限查询")
public class ContractFrameQueryAuthDTO {

    @Schema(description = "当前用户id-临时字段", hidden = true)
    private String userID;

    @Schema(description = "运营人员有权限的客户列表-用于权限查询-operation", hidden = true)
    private List<String> operationEnterpriseIds;

    @Schema(description = "人员列表，匹配运营人员id-用于权限查询-common", hidden = true)
    private List<String> commonUserIdList;

}
