package com.trinasolar.trinax.contract.dto.input.contract;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Created on 2025/6/5, 15:11
 * author: zhangyang
 */
@Data
public class DeliverCancelItemDTO {

    /**
     * 发货合同行业务主键ID
     */
    @Schema(description = "发货合同行业务主键ID")
    private String deliverContractItemId;

    /**
     * 德勤通合同行主键ID
     */
    @Schema(description = "德勤通合同行主键ID")
    private String contractBusinessItemId;


    /**
     * 功率
     */
    @Schema(description = "功率")
    private BigDecimal power;

    /**
     * 初始申请片数
     */
    @Schema(description = "初始申请片数")
    private Integer quantityP;

    /**
     * 已取消片数
     */
    @Schema(description = "已取消片数")
    private Integer hasCanceledP;

    /**
     * 本次取消片数
     */
    @Schema(description = "本次取消片数")
    private Integer cancelP;
}
