package com.trinasolar.trinax.intentorder.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class IntentOrderUserDetailExcelResDTO {

    @Schema(description = "意向单编号")
    @ExcelProperty("意向单编号")
    private String intentOrderNo;

    @Schema(description = "SF商机ID")
    @ExcelProperty("SF商机ID")
    private String opportunityId;

    @Schema(description = "贸易术语")
    @ExcelIgnore
    private String incoterm;

    @Schema(description = "配送方式")
    @ExcelProperty("配送方式")
    private String deliveryType;

    @Schema(description = "客户名称")
    @ExcelProperty("客户名称")
    private String enterpriseName;

    @Schema(description = "关联方")
    @ExcelProperty("关联方")
    private String capitalEnterpriseName;

    @Schema(description = "产品需求量（MW）")
    @ExcelProperty("产品需求量（MW）")
    private BigDecimal potentialSalesVolume;

    @Schema(description = "期望交货时间")
    @ExcelProperty("期望交货时间")
    private LocalDate expectDeliveryDate;

    @Schema(description = "状态")
    @ExcelProperty("状态")
    private String status;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String createdName;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createdTime;

}
