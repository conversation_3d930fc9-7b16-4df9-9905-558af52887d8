package com.trinasolar.trinax.billing.dto.output;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "开票申请列表导出功能")
public class InvoiceDownResDTO {
    @ExcelProperty({"开票申请编号"})
    private String invoiceApplicationId;

    @ExcelProperty({"公司抬头"})
    private String enterpriseName;

    @ExcelProperty({"纳税人识别号"})
    private String taxNumber;

    @ExcelProperty({"供货方实体"})
    private String marketingAccountOu;

    @ExcelProperty({"按W开票"})
    private String isInvoicingUnitW;

    @ExcelProperty({"开票单位"})
    private String invoicingUnitStr;

    @ExcelProperty({"开户行"})
    private String bankName;

    @ExcelProperty({"银行账号"})
    private String bankNo;

    @ExcelProperty({"地址"})
    private String address;

    @ExcelProperty({" 电话"})
    private String mobile;

    @ExcelProperty({"开票申请金额(元)"})
    private BigDecimal invoiceApplicationAmount;

    @ExcelProperty({"申请开票容量(MW)"})
    private BigDecimal invoiceQuantityMw;

    @ExcelProperty({"德勤联系人"})
    private String trinaContactName;

    @ExcelProperty({"德勤联系人电话"})
    private String trinaContactMobile;

    @ExcelProperty({"开票申请人"})
    private String applicantsUserName;

    @ExcelProperty({"开票申请人电话"})
    private String applicantsMobile;

    @ExcelProperty({"开票申请创建时间"})
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime createdTime;

    @ExcelProperty({"开票申请修改人"})
    private String updatedName;

    @ExcelProperty({"开票申请修改时间"})
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime updatedTime;

    @ExcelProperty({"状态"})
    private String applicationStatusStr;


}
