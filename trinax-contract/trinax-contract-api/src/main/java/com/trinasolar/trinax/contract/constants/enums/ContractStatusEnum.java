package com.trinasolar.trinax.contract.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ContractStatusEnum {

    APPROVING("Approving", "审批中"),
    CHANGE_APPROVING("Change Approving", "变更审批中"),
    DISCARD_APPROVING("Discard Approving", "终止审批中"),
    APPROVED("Approved", "审批通过，待德勤用印签署"),
    SINGLE_SIGNED("Single Signed", "德勤已单签"),
    COUNTERSIGNED("Countersigned", "已双签"),
    REJECTED("Rejected", "审批拒绝"),
    CHANGE_REJECTED("Change Rejected", "变更审批拒绝"),
    DISCARD_REJECTED("Discard Rejected", "终止审批拒绝"),
    CANCELLED("Cancelled", "取消"),
    CONTRACT_DISCARDED("Contract Discarded", "合同已终止"),
    CHANGING("Changing", "变更中"),
    CONTRACT_CHANGED("Contract Changed", "合同已变更"),
    CONTRACT_DRAFT("Draft", "草稿"),
    QYS_INVALID("qys-invalid", "无效"),
    OA_FAILED("OA_FAILED", "提交审批失败"),

    ;

    private String code;

    private String desc;


    public static String getDescByCode(String code) {
        for (ContractStatusEnum value : ContractStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

    public static ContractStatusEnum getByCode(String code) {
        for (ContractStatusEnum value : ContractStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
