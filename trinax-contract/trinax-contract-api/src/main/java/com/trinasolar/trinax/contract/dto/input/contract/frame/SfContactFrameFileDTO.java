package com.trinasolar.trinax.contract.dto.input.contract.frame;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SfContactFrameFileDTO {

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件地址")
    private String fileUrl;

    @Schema(description = "文件来源")
    private String fileSource;

}
