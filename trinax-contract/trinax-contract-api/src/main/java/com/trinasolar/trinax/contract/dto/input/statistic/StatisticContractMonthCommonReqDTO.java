package com.trinasolar.trinax.contract.dto.input.statistic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class StatisticContractMonthCommonReqDTO {

    @NotNull(message = "年份不能为空")
    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "客户类别")
    private String customerCategory;

    @Schema(description = "合同状态")
    private String contractStatus;

    @Schema(description = "创建时间始", hidden = true)
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间止", hidden = true)
    private LocalDateTime createTimeEnd;

    @Schema(description = "客户ID列表", hidden = true)
    private List<String> enterpriseIds;

}
