package com.trinasolar.trinax.contract.dto.input.contract.sales;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trinasolar.trinax.contract.constants.enums.ItemChangeType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class ItemChange {

    @Schema(description = "合同号", example = "TSH-A11045-2401-TSI-00008-00")
    private String contractNo;

    @Schema(description = "至尊宝合同行id-不存在存在则是合同行拆分，在原有行上新增", example = "20240130000001")
    private String contractItemId;

    @Schema(description = "至尊宝原始合同行id ", example = "20240512689971")
    @NotBlank(message = "合同行 id 不能为空")
    private String originalItemId;

    @Schema(description = "唯一键，更新时必填")
    private String itemChangeId;

    @Schema(description = "期望交付时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate expectedDeliverDate;

    @Schema(description = "输入方式 PCs/ASP  或者 Requirement(MW)/ASP")
    @NotBlank(message = "输入方式不能为空")
    private String inputType;

    @Schema(description = "功率")
    @NotNull(message = "功率不能为空")
    private BigDecimal quantityW;

    @Schema(description = "总功率 mw")
    @NotNull(message = "总功率 mw 不能为空")
    private BigDecimal quantityMw;

    @Schema(description = "片数")
    @NotNull(message = "片数不能为空")
    private String quantityP;

    @Schema(description = "备注")
    private String comment;

    @Schema(description = "开始功率")
    private String multiPowerBegin;

    @Schema(description = "结束功率")
    private String multiPowerEnd;

    @Schema(description = "操作类型 删除时传delete")
    private ItemChangeType action;

}
