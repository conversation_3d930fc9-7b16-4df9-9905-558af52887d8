package com.trinasolar.trinax.contract.controller;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.contract.api.ContractItemChangeFeign;
import com.trinasolar.trinax.contract.dto.input.change.ContractItemChangeQueryReqDTO;
import com.trinasolar.trinax.contract.dto.output.change.ContractItemChangeQueryResDTO;
import com.trinasolar.trinax.contract.dto.output.change.ContractItemInfoResDTO;
import com.trinasolar.trinax.contract.service.ContractItemChangeService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@Tag(name = "合同行改变")
@RestController
@Slf4j
public class ContractItemChangeController implements ContractItemChangeFeign {

    private final ContractItemChangeService contractItemChangeService;


    @Override
    public Result<List<ContractItemChangeQueryResDTO>> queryChangeList(ContractItemChangeQueryReqDTO reqDTO) {
        return contractItemChangeService.queryChangeList(reqDTO);
    }

    @Override
    public Result<List<ContractItemInfoResDTO>> qryContractItem(String sfContractNo) {
        return contractItemChangeService.qryContractItem(sfContractNo);
    }
}
