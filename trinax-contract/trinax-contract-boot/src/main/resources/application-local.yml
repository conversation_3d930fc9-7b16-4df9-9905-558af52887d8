server:
  port: 8881
spring:
#  application:
#    # 应用名称
#    name: trans-service
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************************
    username: root
    password: 123456@2025
#    password: docker123456##
    hikari:
      minimum-idle: 10
      maximum-pool-size: 20
      idle-timeout: 10000
      max-lifetime: 1800000
      connection-timeout: 30000
#  redis:
#    cluster.nods: clustercfg.dev-membership-redis-cluster.q3bos4.cnw1.cache.amazonaws.com.cn:6379
#    ssl: true
#    password: dev123321crm3215433#2021
#    master-connection-minimum-idle-size: 6
#    slave-connection-minimum-idle-size: 6
#    master-connection-pool-size: 10
#    slave-connection-pool-size: 10
#    connect-timeout: 30000
#    timeout: 30000
  redis:
    # redis数据库索引（默认为0），我们使用索引为3的数据库，避免和其他数据库冲突
    database: 0
    # redis服务器地址（默认为localhost）
    host: ${REDIS_HOST:*************}
    # redis端口（默认为6379）
    port: ${REDIS_PORT:6379}
    # redis访问密码（默认为空）
#    password: ${REDIS_PASSWORD:Trina@123}
    # redis连接超时时间（单位为毫秒）
    timeout: 30000

#  kafka:
#    bootstrap-servers: b-1.dev-membership.qiritt.c4.kafka.cn-northwest-1.amazonaws.com.cn:9092,b-2.dev-membership.qiritt.c4.kafka.cn-northwest-1.amazonaws.com.cn:9092,b-3.dev-membership.qiritt.c4.kafka.cn-northwest-1.amazonaws.com.cn:9092
#    producer:
#      key-serializer: org.apache.kafka.common.serialization.StringSerializer
#      value-serializer: org.apache.kafka.common.serialization.StringSerializer
#      retries: 3
#    consumer:
#      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      group-id: contract-service-default

#  kafka:
#    bootstrap-servers: kafka0:9092;kafka1:9092;kafka2:9092
#    producer:
#      key-serializer: org.apache.kafka.common.serialization.StringSerializer
#      value-serializer: org.apache.kafka.common.serialization.StringSerializer
#      retries: 3
#    consumer:
#      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      group-id: contract-service-default

xxl:
  job:
    adminAddress: ${XXLJOB_ADMINADDRESS:http://localhost:9997/xxl-job-admin}
    executorName: trinax-contract-service
    ip:
    port: 12000
    accessToken:
    logPath: /apps/logs/${spring.application.name}/xxl-job
    logRetentionDays: -1

#日志上报环境地址
logging:
  collector:
    #dev
    address: localhost:8088

#德勤域名
trinax:
  host: https://trinaxapi-dev.deloitte.com/


rocketmq:
  name-server: ${ROCKETMQ_HOST:*************:9876}
  producer:
    group: contract
  consumer:
    group:
    topic:
remain:
  mock: true #是否mock剩余数量
management:
  endpoints:
    web:
      exposure:
        include: health,heapdump

# 本地缓存开关，开启时使用本地缓存
caffeine:
  switch:
    intentOrder: closed
    contract: closed
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志

invoice:
  receiveEmail: <EMAIL>
  cc: <EMAIL>


trina:
  multiPower:
    beginTime: 2025-05-10 00:00:00

host:
  file: https://trinaxfile.deloitte.com
  esign: https://esign.deloitte.com