# Tomcat
server:
  port: 80
#日志组件
trinasolar:
  team: dtt-team
  project:
    name: trinax-contract-service
# Spring
spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  application:
    # 应用名称
    name: trinax-contract-service
    version: 1.0
  profiles:
    # 环境配置
    active: ${ACTIVE_PROFILE:local}
#    active: dev
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      username: deloitte
#      password: ${NACOS_PASSWORD}
      discovery:
        # 使用德勤分配的 namespace 代替，如没有则注释掉这一样
        namespace: trinax
        # 服务注册地址
        server-addr: ${NACOS:localhost}:8848
#        server-addr: mynacos:8848
      config:
       # 使用德勤分配的 namespace 代替，如没有则注释掉这一样
        namespace: trinax
         # 使用德勤分配的 group 代替，如没有则注释掉这一样
#        group: trinax-public
        # 配置中心地址
        server-addr: ${NACOS:localhost}:8848
#        server-addr: mynacos:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-dataids: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: mysentinel:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: ${NACOS:localhost}:8848
#            server-addr: mynacos:8848
            dataId: sentinel-contract-service
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: flow
#  mail:
#    host: smtp.163.com
#    username: <EMAIL>
#    password: SPLTIJVCNPCCCP
#    default-encoding: UTF-8
#    protocol: smtp
#    test-connection: true
#    properties:
#      mail:
#        smtp:
#          auth: true
#          starttls:
#            enable: true
#            required: true
#          socketFactory:
#            class: javax.net.ssl.SSLSocketFactory
#            port: 465
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
mybatis-plus:

  mapper-locations: classpath*:/mapper/**/*.xml
  global-config:
    db-config:
      id-type: none
management:
  health.defaults.enabled: false

  #SPLTIJVCNPCCCPFU

# id生成配置
id-generator.segments:
  intent-order-no:
    step: 3
    limitId: 999999
    preloadNext: false
  intent-order-item-no:
    step: 3
    limitId: 999999
    preloadNext: false
  contract-no:
    step: 3
    limitId: 999999
    preloadNext: false
  require-order-no:
    step: 3
    limitId: 999999
    preloadNext: false
  contractFrame:
    step: 3
    limitId: 999999
    preloadNext: false
  invoice-no:
    step: 3
    limitId: 999999
    preloadNext: false
  invoice-product:
    step: 3
    limitId: 999999
    preloadNext: false
  invoice-contract-main:
    step: 3
    limitId: 999999
    preloadNext: false
  invoice-contract-relation:
    step: 3
    limitId: 999999
    preloadNext: false
  item-change-no:
    step: 3
    limitId: 999999
    preloadNext: false

message:
  template:
    externalSubmitPush: IO_EXTERNAL_CREATE_JPUSH
    externalSubmitTodo: IO_EXTERNAL_CREATE_TODO
    internalConfirmExternalCreatePush: IO_INTERNAL_CONFIRM_EXTERNAL_CREATE_JPUSH
    internalConfirmExternalCreateNotice: IO_INTERNAL_CONFIRM_EXTERNAL_CREATE_NOTICE
    internalConfirmExternalCreateNoticeOperation: IO_INTERNAL_CONFIRM_NOTICE
    externalCancelNotice: IO_EXTERNAL_CANCEL_NOTICE
    internalCancelNotice: IO_INTERNAL_CANCEL_NOTICE
    repeatConfirmNoticeExternal: IO_INTERNAL_EDIT_AFTER_CONFIRM_EXTERNAL_NOTICE
    repeatConfirmNoticeOperation: IO_INTERNAL_EDIT_AFTER_CONFIRM_OPERATION_NOTICE
    internalCreateNotice: IO_INTERNAL_CREATE_NOTICE
    internalCreateNoticeOperation: IO_INTERNAL_CONFIRM_NOTICE

    qoExternalSubmitPush: QO_EXTERNAL_CREATE_JPUSH
    qoExternalSubmitTodo: QO_EXTERNAL_CREATE_TODO
    qoInternalConfirmExternalCreatePush: QO_INTERNAL_CONFIRM_EXTERNAL_CREATE_JPUSH
    qoInternalConfirmExternalCreateNotice: QO_INTERNAL_CONFIRM_EXTERNAL_CREATE_NOTICE
    qoInternalConfirmExternalCreateNoticeOperation: QO_INTERNAL_CONFIRM_NOTICE
    qoExternalCancelNotice: QO_EXTERNAL_CANCEL_NOTICE
    qoInternalCancelNotice: QO_INTERNAL_CANCEL_NOTICE
    qoRepeatConfirmNoticeExternal: QO_INTERNAL_EDIT_AFTER_CONFIRM_EXTERNAL_NOTICE
    qoRepeatConfirmNoticeOperation: QO_INTERNAL_EDIT_AFTER_CONFIRM_OPERATION_NOTICE
    qoInternalCreateNotice: QO_INTERNAL_CREATE_NOTICE
    qoInternalCreateNoticeOperation: QO_INTERNAL_CONFIRM_NOTICE
    qoExternalConfirmInternalCreateNoticeSales: QO_EXTERNAL_CONFIRM_NOTICE
    qoInternalCreateTodo: QO_EXTERNAL_CONFIRM_TODO
    qoConfirmTodo: QO_INTERNAL_CONTRACT_TODO

    contractUnApproveNotice: CONTRACT_UN_APPROVE_NOTICE
    contractSingleSignedPush: CONTRACT_SINGLE_SIGNED_PUSH
    contractSingleSignedTodo: CONTRACT_SINGLE_SIGNED_TODO
    contractDoubleSignedNotice: CONTRACT_LOCK_SIGNED_NOTICE
    frameLockSingleTodo: CONTRACT_FRAME_LOCK_SINGLE_TODO
    frameLockDoublePartnerNotice: CONTRACT_FRAME_LOCK_DOUBLE_PARTNER_NOTICE
    frameLockDoubleOperationNotice: CONTRACT_FRAME_LOCK_DOUBLE_OPERATOIN_NOTICE
    frameUnApproveNotice: CONTRACT_FRAME_UN_APPROVE_NOTICE
    itemChangeNoticeTemplateCode: CONTRACT_CHANGE_POWER
    rq:
      allocationToPartnerNotice: RQ_ALLOCATION_TO_PARTNER_NOTICE
      allocationToSaleTodo: RQ_ALLOCATION_TO_SALE_TODO
      areaManagerAllocationToAnotherTodo: RQ_AREA_MANAGER_ALLOCATION_TO_ANOTHER_TODO
      confirmToIntentOrderNotice: RQ_CONFIRM_TO_INTENT_ORDER_NOTICE
      externalCancelNotice: RQ_EXTERNAL_CANCEL_NOTICE
      externalCreateEnterpriseOrderTodo: RQ_EXTERNAL_CREATE_ENTERPRISE_ORDER_TODO
      externalCreatePersonalOrderTodo: RQ_EXTERNAL_CREATE_PERSONAL_ORDER_TODO
      internalCancelNotice: RQ_INTERNAL_CANCEL_NOTICE
      internalCancelNoticePartner: RQ_INTERNAL_CANCEL_NOTICE_PARTNER
    invoice:
      externalSubmitNotice: EXTERNAL_SUBMIT_INVOICE_NOTICE

# 配置项中的企业，组织类型为COMMON的用户，不需要查看该企业数据（该客户是测试客户，需要排除）
intentOrder:
  exclude:
    enterpriseId: CE202401019999