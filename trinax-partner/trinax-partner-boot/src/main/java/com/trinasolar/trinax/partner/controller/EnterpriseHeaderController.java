package com.trinasolar.trinax.partner.controller;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.partner.api.EnterpriseHeaderFeign;
import com.trinasolar.trinax.partner.domain.contract.service.EnterpriseHeaderService;
import com.trinasolar.trinax.partner.dto.input.EnterpriseHeaderQueryReqDTO;
import com.trinasolar.trinax.partner.dto.input.EnterpriseHeaderSaveOrUpdateReqDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseHeaderQueryResDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "企业地址信息接口")
public class EnterpriseHeaderController implements EnterpriseHeaderFeign {

    private final EnterpriseHeaderService enterpriseHeaderService;

    @Override
    public Result<List<EnterpriseHeaderQueryResDTO>> authPassedEnterpriseHeaderByDealer(EnterpriseHeaderQueryReqDTO reqDTO) {
        return enterpriseHeaderService.authPassedEnterpriseHeaderByDealer(reqDTO);
    }
    @Override
    public Result<String> saveOrUpdateEnterpriseAddress(EnterpriseHeaderSaveOrUpdateReqDTO reqDTO) {
        return enterpriseHeaderService.saveOrUpdateEnterpriseAddress(reqDTO);
    }
    @Override
    public Result<String> removeById(Long id) {
        return enterpriseHeaderService.removeById(id);
    }

    @Override
    public Result<EnterpriseHeaderQueryResDTO> getDetailByHeaderId(String headerId) {
        return enterpriseHeaderService.getDetailByHeaderId(headerId);
    }
}
