# Tomcat
server:
  port: 80
#日志组件
trinasolar:
  team: dtt-team
  project:
    name: trinax-partner-service
# Spring
spring:
  application:
    # 应用名称
    name: trinax-partner-service
    version: 1.0
  profiles:
    # 环境配置
    active: ${ACTIVE_PROFILE:local}
#    active: dev
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
#      username: trinasolarx
#      password: ${NACOS_PASSWORD}
      discovery:
        # 使用德勤分配的 namespace 代替，如没有则注释掉这一样
        namespace: trinax
        # 服务注册地址
        server-addr: ${NACOS:localhost}:8848
#        server-addr: mynacos:8848
      config:
       # 使用德勤分配的 namespace 代替，如没有则注释掉这一样
        namespace: trinax
         # 使用德勤分配的 group 代替，如没有则注释掉这一样
#        group: trinax-public
        # 配置中心地址
        server-addr: ${NACOS:localhost}:8848
#        server-addr: mynacos:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-dataids: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: mysentinel:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: ${NACOS:localhost}:8848
#            server-addr: mynacos:8848
            dataId: sentinel-integration-service
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: flow
#  mail:
#    host: smtp.163.com
#    username: <EMAIL>
#    password: SPLTIJVCNPCCCP
#    default-encoding: UTF-8
#    protocol: smtp
#    test-connection: true
#    properties:
#      mail:
#        smtp:
#          auth: true
#          starttls:
#            enable: true
#            required: true
#          socketFactory:
#            class: javax.net.ssl.SSLSocketFactory
#            port: 465

mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  global-config:
    db-config:
      id-type: none
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
management:
  health.defaults.enabled: false

# id生成配置
id-generator.segments:
  enterprise-id:
    step: 3
    limitId: 9999
    preloadNext: false
  enterprise-header-id:
    step: 3
    limitId: 9999
    preloadNext: false

  #SPLTIJVCNPCCCPFU
rocketmq:
  name-server: ${ROCKETMQ_HOST:*************:9876}
  producer:
    group: partner
  consumer:
    group: partner
    topic:

#建商通知消息
message:
  template:
    salesApplyNotice: ENTERPRISE_SALES_APPLY_NOTICE
    salesSubmitSuccessNotice: ENTERPRISE_SALES_SUBMIT_SUCCESS_NOTICE
    salesSubmitFailNotice: ENTERPRISE_SALES_SUBMIT_FAIL_NOTICE
    operationSubmitSuccessNotice: ENTERPRISE_OPERATION_SUBMIT_SUCCESS_NOTICE
    operationSubmitFailNotice: ENTERPRISE_OPERATION_SUBMIT_FAIL_NOTICE