server:
  port: 89
spring:
#  application:
#    # 应用名称
#    name: trans-service
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************************************
    username: root
    password: 123456@2025
#    password: docker123456##
    hikari:
      minimum-idle: 10
      maximum-pool-size: 20
      idle-timeout: 10000
      max-lifetime: 1800000
      connection-timeout: 30000
#  redis:
#    cluster.nods: clustercfg.dev-membership-redis-cluster.q3bos4.cnw1.cache.amazonaws.com.cn:6379
#    ssl: true
#    password: dev123321crm3215433#2021
#    master-connection-minimum-idle-size: 6
#    slave-connection-minimum-idle-size: 6
#    master-connection-pool-size: 10
#    slave-connection-pool-size: 10
#    connect-timeout: 30000
#    timeout: 30000
  redis:
    # redis数据库索引（默认为0），我们使用索引为3的数据库，避免和其他数据库冲突
    database: 0
    # redis服务器地址（默认为localhost）
    host: *************
    # redis端口（默认为6379）
    port: 6379
    # redis访问密码（默认为空）
#    password: redis123456##
    # redis连接超时时间（单位为毫秒）
    timeout: 30000

#  kafka:
#    bootstrap-servers: b-1.dev-membership.qiritt.c4.kafka.cn-northwest-1.amazonaws.com.cn:9092,b-2.dev-membership.qiritt.c4.kafka.cn-northwest-1.amazonaws.com.cn:9092,b-3.dev-membership.qiritt.c4.kafka.cn-northwest-1.amazonaws.com.cn:9092
#    producer:
#      key-serializer: org.apache.kafka.common.serialization.StringSerializer
#      value-serializer: org.apache.kafka.common.serialization.StringSerializer
#      retries: 3
#    consumer:
#      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      group-id: integration-service-default

#  kafka:
#    bootstrap-servers: kafka0:9092;kafka1:9092;kafka2:9092
#    producer:
#      key-serializer: org.apache.kafka.common.serialization.StringSerializer
#      value-serializer: org.apache.kafka.common.serialization.StringSerializer
#      retries: 3
#    consumer:
#      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      group-id: integration-service-default

xxl:
  job:
    adminAddress: ${XXLJOB_ADMINADDRESS:http://localhost:9997/xxl-job-admin}
    executorName: trinax-partner-service
    ip:
    port: 12000
    accessToken:
    logPath: /apps/logs/${spring.application.name}/xxl-job
    logRetentionDays: -1
#日志上报环境地址
logging:
  collector:
    #dev
    address: localhost:8088

tianyancha:
  mock-on: false

enterprise:
  auth:
    date:
      check:
        month : 12
    template:
      download : https://trinaxfile-uat.deloitte.com/filebff/file/download/pub/d5f14b7d760545a8af602827926155d1
trina:
  user:
    permission:
      check_org_code: TRINA_CN_SALES_HUADONG,TRINA_CN_SALES_HUABEI,TRINA_CN_SALES_DONGBEI,TRINA_CN_SALES_HUANAN,TRINA_CN_SALES_ZHEJIANG,TRINA_CN_SALES_LUEXIANG,TRINA_CN_SALES_STRATEGICMARKETING