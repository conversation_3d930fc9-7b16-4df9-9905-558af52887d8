package com.trinasolar.trinax.partner.domain.contract.service.biz.changeowner;

import cn.hutool.core.collection.CollUtil;
import com.trinasolar.trinax.basic.api.ChangeOwnerFeign;
import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.changeowner.ChangeTaskStatusEnum;
import com.trinasolar.trinax.basic.dto.input.changeOwner.*;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.EnterpriseBizMapperService;
import com.trinasolar.trinax.partner.domain.contract.repository.po.EnterpriseBizPO;
import com.trinasolar.trinax.partner.dto.input.EnterpriseChangeOwnerReqDTO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@RequiredArgsConstructor
@Component
@Slf4j
public class MainSaleChangeOwnerBiz {

    private final EnterpriseBizMapperService enterpriseBizMapperService;
    private final MqManager mqManager;
    private final ChangeOwnerFeign changeOwnerFeign;

    /**
     * 提交变更：生成主任务，子任务，发送mq
     */
    public Result<String> changeOwner(EnterpriseChangeOwnerReqDTO reqDTO) {
        log.info("Main sales change owner 入参ChangeOwnerMainSalesReqDTO：{}", reqDTO);

        List<EnterpriseBizPO> enterpriseBizPOList = getEnterprise(reqDTO);
        if(CollUtil.isEmpty(enterpriseBizPOList)){
            //不存在变更数据时，
            // 只需要修改任务状态
            updateTaskStatus(reqDTO,true,0,"");
            //添加任务记录
            sendChangeMq(reqDTO);
        }else{
            //填充数据
            fillContractData(enterpriseBizPOList,reqDTO);
            boolean changed = false;
            String errMessage = "";
            try{
                changed = enterpriseBizMapperService.updateBatchById(enterpriseBizPOList);
            }catch (Exception e){
                errMessage = e.getMessage();
                log.error("Exception message:",e);
            }finally {
                //修改任务状态
                updateTaskStatus(reqDTO,changed,enterpriseBizPOList.size(),errMessage);
                //发送任务记录MQ
                sendChangeMq(reqDTO);
            }
        }
        return Result.ok();

    }

    private void fillContractData(List<EnterpriseBizPO> enterpriseBizPOList, EnterpriseChangeOwnerReqDTO reqDTO){
        enterpriseBizPOList.forEach(e->{
            e.setMainSalesUserId(reqDTO.getNewUserId());
            e.setMainSalesUserName(reqDTO.getNewUserName());
            e.setUpdatedBy(reqDTO.getUpdatedBy());
            e.setUpdatedName(reqDTO.getUpdatedName());
            e.setUpdatedTime(LocalDateTime.now());
        });
    }
    private List<EnterpriseBizPO> getEnterprise(EnterpriseChangeOwnerReqDTO reqDTO){
        return enterpriseBizMapperService.lambdaQuery()
                .in(EnterpriseBizPO::getEnterpriseId,reqDTO.getEnterpriseIdList())
                .eq(EnterpriseBizPO::getMainSalesUserId,reqDTO.getOriginUserId())
                .list();
    }

    private void updateTaskStatus(EnterpriseChangeOwnerReqDTO reqDTO, boolean changed,int amount,String errMessage){
        ChangeOwnerStatusUpdateReqDTO updateReqDTO =  new ChangeOwnerStatusUpdateReqDTO();
        updateReqDTO.setTaskNo(reqDTO.getTaskNo());
        updateReqDTO.setSubTaskNo(reqDTO.getSubTaskNo());
        updateReqDTO.setUpdateUserId(reqDTO.getUpdatedBy());
        updateReqDTO.setUpdateUserName(reqDTO.getUpdatedName());
        updateReqDTO.setSubTaskStatus(changed ? ChangeTaskStatusEnum.SUCCESS.getCode():ChangeTaskStatusEnum.FAIL.getCode());
        updateReqDTO.setOriginUserId(reqDTO.getOriginUserId());
        updateReqDTO.setNewUserId(reqDTO.getNewUserId());
        updateReqDTO.setChangeAmount(amount);
        updateReqDTO.setErrMessage(errMessage);
        changeOwnerFeign.updateTaskStatus(updateReqDTO);
    }

    private void sendChangeMq(EnterpriseChangeOwnerReqDTO reqDTO){
        ChangeOwnerRecordMqDTO mqDTO = new ChangeOwnerRecordMqDTO();
        mqDTO.setTaskNo(reqDTO.getTaskNo());
        mqDTO.setSubTaskNo(reqDTO.getSubTaskNo());
        mqDTO.setSituationCode(reqDTO.getSituationCode());
        mqDTO.setOldValue(reqDTO.getOriginUserId() + "," + reqDTO.getOriginUserName());
        mqDTO.setNewValue(reqDTO.getNewUserId() + "," + reqDTO.getNewUserName());
        mqDTO.setChangeTable("trinax_partner.enterprise_biz");
        mqDTO.setChangeField("main_sales_user_id,main_sales_user_name");
        mqDTO.setCurrentUserId(reqDTO.getUpdatedBy());
        mqDTO.setCurrentUserName(reqDTO.getUpdatedName());
        log.info("TodoList ChangeOwnerRecord mqDTO：{}", reqDTO);
        mqManager.sendTopic(BasicCommonConstant.CHANGE_OWNER_RECORD_TOPIC, JacksonUtil.bean2Json(mqDTO));
    }

}
