package com.trinasolar.trinax.partner.domain.contract.service;

import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.partner.dto.input.*;
import com.trinasolar.trinax.partner.dto.input.enterprise.EnterpriseSaveReqDTO;
import com.trinasolar.trinax.partner.dto.input.enterprise.EnterpriseSearchReqDTO;
import com.trinasolar.trinax.partner.dto.input.statistic.StatisticEnterpriseNumReqDTO;
import com.trinasolar.trinax.partner.dto.output.*;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseRelationPcDTO;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseSearchResDTO;
import com.trinasolar.trinax.partner.dto.output.statistic.StatisticEnterpriseNumRespDTO;

import java.util.List;
import java.util.Map;

public interface EnterpriseService {

    /**
     * 表拆分后的代码逻辑改造-无需处理
     */
    List<EnterpriseInfoDTO> getEnterpriseInfoBySales(String userId);

    /**
     * 表拆分后的代码逻辑改造-无需处理
     */
    List<EnterpriseInfoDTO> getEnterpriseInfoByDealer(String userId);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    EnterpriseBriefDTO getEnterpriseByEnterpriseId(String enterpriseId);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    EnterpriseDTO getEnterpriseDTO(String enterpriseId);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    List<EnterpriseBriefDTO> getEnterpriseByEnterpriseIds(List<String> enterpriseIds);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    List<EnterpriseBriefDTO> getAllEnterpriseBriefs();

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    List<EnterpriseBriefDTO> listWithCustomerCategory(String customerCategory);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    List<EnterpriseBriefDTO> listEnterpriseEditAndAddPc();

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    List<EnterpriseDTO> getLinkEnterprise(LinkEnterpriseQueryReqDTO reqDTO);

    /**
     * 根据linkEnterpriseId查询
     */
    List<EnterpriseDTO> getEnterpriseListByLinkId(EnterpriseQueryDTO reqDTO);

    /**
     * 根据linkEnterpriseId批量查询
     */
    Map<String, List<EnterpriseDTO>> getEnterprisesByLinkId(LinkEnterpriseQueryReqDTO reqDTO);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    List<EnterpriseDTO> getLinkEnterpriseUnion(LinkEnterpriseUnionQueryReqDTO reqDTO);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    EnterpriseDTO getEnterpriseBySfId(String sfId);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    EnterpriseMainSalesDTO getEnterpriseForMainSales(String enterpriseId, String userId);

    Result<String> syncEnterprise(EnterpriseSaveReqDTO req);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    void allocateMainSales(AllocateMainSalesDTO allocateMainSalesDTO);

    Result<PageResponse<EnterpriseSearchResDTO>> searchEnterprise(EnterpriseSearchReqDTO req);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    EnterprisePcDetailDTO pcEnterpriseByEnterpriseId(String enterpriseId);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    PageResponse<EnterpriseResPcDTO> pageEnterpriseResPC(PageRequest<EnterpriseQueryReqDTO> reqDTO);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    List<EnterpriseResPcExcelDTO> listEnterpriseResPcExcel(EnterpriseQueryReqDTO reqDTO);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    List<String> listByMainSalesId(String mainSalesUserId);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    List<EnterpriseDTO> listByQuery(EnterpriseQueryDTO queryDTO);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    void updateMainSales(AllocateMainSalesDTO allocateMainSalesDTO);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    PageResponse<EnterpriseDTO> pageByQuery(PageRequest<EnterpriseQueryDTO> pageReqDTO);

    /**
     * 表拆分后的代码逻辑改造-已完成
     */
    List<EnterpriseBriefDTO> questionnaireEnterpriseEditAndAdd(String name);

    PageResponse<EnterpriseCapitalResPcDTO> pageEnterpriseCapitalResPC(PageRequest<EnterpriseCapitalQueryReqDTO> pageReqDTO);

    EnterpriseAccessDetailDTO getEnterpriseAccess(String enterpriseId);

    List<String> qryRelCo(String enterpriseId);

    StatisticEnterpriseNumRespDTO statisticEnterpriseNum(StatisticEnterpriseNumReqDTO reqDTO);

    void updateEnterpriseApply(EnterpriseApplyReqDTO enterpriseApplyReqDTO);

}
