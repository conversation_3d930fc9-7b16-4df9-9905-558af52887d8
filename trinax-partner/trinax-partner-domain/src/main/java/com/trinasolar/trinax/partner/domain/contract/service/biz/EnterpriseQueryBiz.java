package com.trinasolar.trinax.partner.domain.contract.service.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.trinasolar.trinax.basic.api.OptionFeign;
import com.trinasolar.trinax.basic.constants.enums.OptionGroupConstant;
import com.trinasolar.trinax.basic.dto.input.option.OptionItemQueryReqDTO;
import com.trinasolar.trinax.basic.dto.output.OptionItemResDTO;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.integration.api.TianyanchaFeign;
import com.trinasolar.trinax.integration.dto.input.enterprise.CompanySearchReqDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.CompanySearchResDTO;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseTypeEnum;
import com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.EnterpriseMapperService;
import com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.EnterpriseRelationMapperService;
import com.trinasolar.trinax.partner.domain.contract.repository.mapper.EnterpriseFullDataMapper;
import com.trinasolar.trinax.partner.domain.contract.repository.po.EnterpriseFullDataPO;
import com.trinasolar.trinax.partner.domain.contract.repository.po.EnterprisePO;
import com.trinasolar.trinax.partner.domain.contract.repository.po.EnterpriseRelationPO;
import com.trinasolar.trinax.partner.dto.input.enterprise.EnterpriseSearchReqDTO;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseSearchResDTO;
import com.trinasolar.trinax.user.api.SysDealerSalesRelationFeign;
import com.trinasolar.trinax.user.dto.input.PageExternalEnterpriseQueryDTO;
import com.trinasolar.trinax.user.dto.output.ExternalEnterpriseRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@RequiredArgsConstructor
@Service
@Slf4j
public class EnterpriseQueryBiz {
    private final TianyanchaFeign tianyanchaFeign;
    private final SysDealerSalesRelationFeign salesRelationFeign;
    private final EnterpriseFullDataMapper enterpriseFullDataMapper;
    private final EnterpriseRelationMapperService enterpriseRelationMapperService;
    private final EnterpriseMapperService enterpriseMapperService;

    private final OptionFeign optionFeign;
    @Value("${qcc.excludeCompany}")
    List<Object> qccExcludeCompany;


    public Result<PageResponse<EnterpriseSearchResDTO>> searchEnterprise(EnterpriseSearchReqDTO req) {

        //已绑定企业数据
        List<ExternalEnterpriseRespDTO> bindEnterpriseList = bindEnterpriseInit(req);

        //天眼查数据
        Result<PageResponse<CompanySearchResDTO>> result = findTianyanCha(req);


        return loadResult(bindEnterpriseList, result, req.getEnterpriseId());
    }

    private Result<PageResponse<CompanySearchResDTO>> findTianyanCha(EnterpriseSearchReqDTO req) {

        Result<PageResponse<CompanySearchResDTO>> result = tianyanchaFeign.searchCompany(BeanUtil.copyProperties(req, CompanySearchReqDTO.class));
        if (Boolean.FALSE.equals(result.getSuccess())) {
            throw new BizException(result.getCode(), result.getMessage());
        }
        return result;
    }

    private Result<PageResponse<EnterpriseSearchResDTO>> loadResult(List<ExternalEnterpriseRespDTO> bindEnterpriseList,
                                                                    Result<PageResponse<CompanySearchResDTO>> result, String enterpriseId) {

        List<EnterpriseSearchResDTO> originRecords = BeanUtil.copyToList(result.getData().getRecords(), EnterpriseSearchResDTO.class);
        //过滤掉无效数据
        List<EnterpriseSearchResDTO> records =originRecords.stream().filter(a->!qccExcludeCompany.contains(a.getName())&&!a.getName().contains("德勤光能")).collect(Collectors.toList());
        List<String> creditCodeList = new ArrayList<>();

        if (ObjectUtils.isEmpty(records)) {
            return Result.ok(PageResponse.toResult(
                    result.getData().getIndex(),
                    result.getData().getSize(),
                    result.getData().getTotal(),
                    new ArrayList<>()
            ));
        }
        if (ObjectUtil.isNotEmpty(enterpriseId)) {
            List<String> linkEnterpriseIds = enterpriseRelationMapperService.listByEnterpriseId(enterpriseId).stream().map(EnterpriseRelationPO::getLinkEnterpriseId).collect(Collectors.toList());
            Set<String> creditCodeSet = enterpriseMapperService.listByEnterpriseIds(linkEnterpriseIds).stream().map(EnterprisePO::getCreditCode).collect(Collectors.toSet());
            for (EnterpriseSearchResDTO e : records) {
                e.setLinked(creditCodeSet.contains(e.getCreditCode()));
            }
        }
        //加载绑定标志
        records.forEach(a -> {
            //默认未绑定
            a.setBind(0);
            //默认非生态合作伙伴
            a.setPartner(false);
            //默认初始化不可绑定
            a.setSubsisting(false);
            a.setCustomerCategory(EnterpriseTypeEnum.BUSINESS.getCode());
            creditCodeList.add(a.getCreditCode());
            for (int i = 0; i < bindEnterpriseList.size(); i++) {
                if (StringUtils.equals(a.getCreditCode(), bindEnterpriseList.get(i).getTaxNumber())) {
                    a.setBind(1);
                }
            }
        });
        List<EnterpriseFullDataPO> enterpriseList = enterpriseFullDataMapper.qryEnterpriseByCodeList(creditCodeList);
        if (ObjectUtils.isNotEmpty(enterpriseList)) {
            enterpriseList.forEach(enterprise -> {
                records.forEach(a -> {
                    if (StringUtils.equals(enterprise.getCreditCode(), a.getCreditCode())) {
                        a.setCustomerCategory(enterprise.getCustomerCategory());
                        if (enterprise.getCustomerCategory().contains(EnterpriseTypeEnum.PARTNER.getCode())) {
                            a.setPartner(true);
                        }
                    }
                });
            });
        }
        OptionItemQueryReqDTO qryCondition = new OptionItemQueryReqDTO();
        qryCondition.setOptionGroup(OptionGroupConstant.OPTION_GROUP_REG_STATUS);
        List<OptionItemResDTO> optionItemList = optionFeign.qryOptionItemList(qryCondition).getData();

        if (ObjectUtils.isNotEmpty(optionItemList)) {
            List<String> optionValue = optionItemList.stream()
                    .map(OptionItemResDTO::getOptionValue)
                    .collect(Collectors.toList());
            records.forEach(a -> {
                if (optionValue.contains(a.getRegStatus())) {
                    a.setSubsisting(true);
                }
            });
        }
        return Result.ok(PageResponse.toResult(
                result.getData().getIndex(),
                result.getData().getSize(),
                result.getData().getTotal(),
                records
        ));
    }

    private List<ExternalEnterpriseRespDTO> bindEnterpriseInit(EnterpriseSearchReqDTO req) {

        PageExternalEnterpriseQueryDTO enterpriseQueryDTO = PageExternalEnterpriseQueryDTO.builder()
                .dealerUserId(req.getUserId())
                .build();
        List<ExternalEnterpriseRespDTO> bindEnterpriseList = salesRelationFeign.listExternalEnterprise(enterpriseQueryDTO).getData();
        if (ObjectUtils.isEmpty(bindEnterpriseList)) {
            bindEnterpriseList = new ArrayList<>();
        }
        return bindEnterpriseList;
    }
}
