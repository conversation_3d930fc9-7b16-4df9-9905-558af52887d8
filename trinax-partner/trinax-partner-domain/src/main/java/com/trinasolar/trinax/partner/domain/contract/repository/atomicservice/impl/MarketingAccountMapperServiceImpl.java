package com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.MarketingAccountMapperService;
import com.trinasolar.trinax.partner.domain.contract.repository.mapper.MarketingAccountMapper;
import com.trinasolar.trinax.partner.domain.contract.repository.po.MarketingAccountPO;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class MarketingAccountMapperServiceImpl extends ServiceImpl<MarketingAccountMapper, MarketingAccountPO> implements MarketingAccountMapperService {

    @Override
    public MarketingAccountPO findByAccountId(String accountId) {
        LambdaQueryWrapper<MarketingAccountPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketingAccountPO::getAccountId, accountId);
        return getOne(queryWrapper);
    }

    @Override
    public List<MarketingAccountPO> listByAccountIdList(List<String> accountIdList) {
        if (ObjectUtil.isEmpty(accountIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MarketingAccountPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MarketingAccountPO::getAccountId, accountIdList);
        return list(queryWrapper);
    }
}
