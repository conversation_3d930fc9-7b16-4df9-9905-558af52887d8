package com.trinasolar.trinax.partner.domain.contract.service.biz;

import cn.hutool.core.util.ObjectUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.partner.domain.contract.service.EnterpriseBizRelationService;
import com.trinasolar.trinax.partner.domain.contract.service.EnterpriseService;
import com.trinasolar.trinax.partner.dto.input.EnterpriseQueryAuthReqDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseBizRelationResDTO;
import com.trinasolar.trinax.user.api.SysDealerSalesRelationFeign;
import com.trinasolar.trinax.user.api.SysSalesOperationRelationFeign;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.SysOrganizationTypeEnum;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import com.trinasolar.trinax.user.constants.UserConstant;
import com.trinasolar.trinax.user.dto.input.SubordinateQueryReqDTO;
import com.trinasolar.trinax.user.dto.output.SysSalesOperationRelationRespDTO;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
public class EnterpriseAuthBiz {

    private final SysUserFeign sysUserFeign;
    private final EnterpriseBizRelationService enterpriseBizRelationService;
    private final SysDealerSalesRelationFeign sysDealerSalesRelationFeign;
    private final SysSalesOperationRelationFeign sysSalesOperationRelationFeign;

    public boolean handleAuth(EnterpriseQueryAuthReqDTO reqDTO) {
        String loginUserId = reqDTO.getLoginUserId();
        Result<SysUserRespDTO> sysUserRespDTOResult = sysUserFeign.getUserByUserId(loginUserId);
        SysUserRespDTO sysUserRespDTO = sysUserRespDTOResult.getData();
        // 1. 如果是超级管理员，那么直接可以看所有
        if (ObjectUtil.isNotEmpty(sysUserRespDTO.getRoleIds()) && sysUserRespDTO.getRoleIds().contains(UserConstant.ADMIN_ROLE_ID)) {
            return true;
        }
        if (SysOrganizationTypeEnum.SALES.getCode().equals(sysUserRespDTO.getOrganizationType())) {
            // 销售用户
            if (ObjectUtil.isNotEmpty(sysUserRespDTO.getRoleIds()) && sysUserRespDTO.getRoleIds().contains(UserConstant.SALES_AREA_MANAGER_ROLE_ID)) {
                // 2. 销售区域总能查看到自己所在战区的所有客户
                List<EnterpriseBizRelationResDTO> enterpriseBizRelationResDTOS = enterpriseBizRelationService.listByBizOrganizationCode(sysUserRespDTO.getOrganizationCode());
                if (ObjectUtil.isEmpty(enterpriseBizRelationResDTOS)) {
                    return false;
                }
                reqDTO.setEnterpriseIds(enterpriseBizRelationResDTOS.stream().map(EnterpriseBizRelationResDTO::getEnterpriseId).collect(Collectors.toList()));
            } else {
                // 3. 非销售区域总：查看自己作为主销售或作为专属销售的客户
                List<String> userIds = Collections.singletonList(loginUserId);
                // 去查询作为专属销售的企业id列表
                Result<List<String>> enterpriseIdsResult = sysDealerSalesRelationFeign.listEnterpriseIdBySalesIds(userIds);
                if (ObjectUtil.isNotEmpty(enterpriseIdsResult.getData())) {
                    reqDTO.setEnterpriseIds(enterpriseIdsResult.getData());
                }
                reqDTO.setSalesUserIds(userIds);
            }
        } else if (SysOrganizationTypeEnum.OPERATION.getCode().equals(sysUserRespDTO.getOrganizationType()) || SysOrganizationTypeEnum.COMMON.getCode().equals(sysUserRespDTO.getOrganizationType())) {
            // 4. 运营用户&常规用户: 自己和所有下级对应的销售，作为主销售或作为专属销售的客户
            // 4.1 查询自己和所有下级
            Result<List<SysUserRespDTO>> sysUserFeignSubordinateResult = sysUserFeign.getSubordinate(SubordinateQueryReqDTO.builder().userId(loginUserId).sysUserTypeEnum(SysUserTypeEnum.INTERNAL).build());
            if (ObjectUtil.isEmpty(sysUserFeignSubordinateResult.getData())) {
                return false;
            }
            // 4.2 查询自己和所有下级（作为运营）对应的销售
            List<SysSalesOperationRelationRespDTO> sysSalesOperationRelationRespDTOS = sysSalesOperationRelationFeign.listByOperationUserIds(sysUserFeignSubordinateResult.getData().stream().map(SysUserRespDTO::getUserId).collect(Collectors.toList()));
            if (ObjectUtil.isEmpty(sysSalesOperationRelationRespDTOS)) {
                return false;
            }
            List<String> salesUserIds = sysSalesOperationRelationRespDTOS.stream().map(SysSalesOperationRelationRespDTO::getSalesUserId).collect(Collectors.toList());
            // 去查询作为专属销售的企业id列表
            Result<List<String>> enterpriseIdsResult = sysDealerSalesRelationFeign.listEnterpriseIdBySalesIds(salesUserIds);
            if (ObjectUtil.isNotEmpty(enterpriseIdsResult.getData())) {
                reqDTO.setEnterpriseIds(enterpriseIdsResult.getData());
            }
            reqDTO.setSalesUserIds(salesUserIds);
        } else {
            // 其它组织类型的用户
            // 无任何数据权限，直接返回空
            return false;
        }
        return true;
    }

}
