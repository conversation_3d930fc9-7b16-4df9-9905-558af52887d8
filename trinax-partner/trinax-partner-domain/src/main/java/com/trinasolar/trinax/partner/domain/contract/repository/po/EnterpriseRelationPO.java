package com.trinasolar.trinax.partner.domain.contract.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseRelationStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 客户企业关系
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("enterprise_relation")
public class EnterpriseRelationPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 企业ID
     */
    private String enterpriseId;

    /**
     * 企业类型;CAPITAL-资方，PARTNER-生态伙伴
     */
    private String enterpriseType;

    /**
     * 关联企业ID
     */
    private String linkEnterpriseId;

    /**
     * 关联企业类型;CAPITAL-资方，PARTNER-生态伙伴
     */
    private String linkEnterpriseType;

    /**
     * @see EnterpriseRelationStatus
     */
    @Schema(description = "状态：0失效，1有效")
    private Integer status;

    /**
     * 是否已删除;1：已删除 0：未删除
     */
    private Integer isDeleted;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建人姓名
     */
    private String createdName;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新人姓名
     */
    private String updatedName;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;


}
