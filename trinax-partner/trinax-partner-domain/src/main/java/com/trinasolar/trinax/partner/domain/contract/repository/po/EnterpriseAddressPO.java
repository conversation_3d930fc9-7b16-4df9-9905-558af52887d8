package com.trinasolar.trinax.partner.domain.contract.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseAddressType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("enterprise_address")
public class EnterpriseAddressPO {
    /**
     * 自增主键
     */
    @Schema(name = "自增主键", description = "自增主键")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 企业ID
     */
    @Schema(name = "企业ID", description = "企业ID")
    private String enterpriseId;

    /**
     * sf ID
     */
    @Schema(name = "sf ID", description = "sf ID")
    private String sfId;

    /**
     * 收件人名字
     */
    @Schema(name = "收件人名字", description = "收件人名字")
    private String recipientName;

    /**
     * 收件人联系方式
     */
    @Schema(name = "收件人联系方式", description = "收件人联系方式")
    private String recipientPhone;

    /**
     * 地址ID
     */
    @Schema(name = "地址ID", description = "地址ID")
    private String addressId;

    /**
     * 地址名称
     */
    @Schema(name = "地址名称", description = "地址名称")
    private String addressName;

    @Schema(name = "国", description = "国编码")
    private String countryCode;
    /**
     *
     */
    @Schema(name = "省", description = "省")
    private String provinceCode;

    /**
     *
     */
    @Schema(name = "市", description = "市")
    private String cityCode;

    /**
     *
     */
    @Schema(name = "县", description = "县")
    private String districtCode;

    /**
     *
     */
    @Schema(name = "街道", description = "街道")
    private String streetCode;

    @Schema(name = "国", description = "国")
    private String countryName;

    @Schema(name = "省", description = "省")
    private String provinceName;

    @Schema(name = "市", description = "市")
    private String cityName;

    @Schema(name = "县", description = "县")
    private String districtName;

    @Schema(name = "街道", description = "街道")
    private String streetName;

    @Schema(name = "邮政编码", description = "邮政编码")
    private String postalCode;
    /**
     * 详细地址
     */
    @Schema(name = "详细地址", description = "详细地址")
    private String addressDetail;

    /**
     * 完整地址
     */
    @Schema(name = "完整地址", description = "完整地址")
    private String fullAddress;

    @Schema(name = "地址状态", description = "地址状态")
    private String addressStatus;

    /**
     * @see EnterpriseAddressType
     */
    @Schema( description = "地址类型")
    private String addressType;

    /**
     * 是否为默认;1：是    0：否
     */
    @Schema(name = "是否为默认;1：是    0：否", description = "是否为默认;1：是    0：否")
    private Integer isDefault;

    /**
     * 是否已删除;1：已删除    0：未删除
     */
    @Schema(name = "是否已删除;1：已删除    0：未删除", description = "是否已删除;1：已删除    0：未删除")
    private Integer isDeleted;

    /**
     * 创建人
     */
    @Schema(name = "创建人", description = "创建人")
    private String createdBy;

    /**
     * 创建人姓名
     */
    @Schema(name = "创建人姓名", description = "创建人姓名")
    private String createdName;

    /**
     * 创建时间
     */
    @Schema(name = "创建时间", description = "创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @Schema(name = "更新人", description = "更新人")
    private String updatedBy;

    /**
     * 更新人姓名
     */
    @Schema(name = "更新人姓名", description = "更新人姓名")
    private String updatedName;

    /**
     * 更新时间
     */
    @Schema(name = "更新时间", description = "更新时间")
    private LocalDateTime updatedTime;
}

