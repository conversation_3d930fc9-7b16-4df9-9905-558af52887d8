package com.trinasolar.trinax.partner.domain.contract.service.impl;

import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.partner.constants.ContactorSyncConstant;
import com.trinasolar.trinax.partner.domain.contract.service.ContactSyncService;
import com.trinasolar.trinax.partner.domain.contract.service.biz.enterprise.ContactSyncBiz;
import com.trinasolar.trinax.partner.dto.mq.ContactorSyncMqDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ContactSyncServiceImpl implements ContactSyncService {
    @Autowired
    ContactSyncBiz contactSyncBiz;

    @Override
    public void contactSyncSF(ContactorSyncMqDTO req) {
        switch (req.getSyncType()) {
            case ContactorSyncConstant.SYNC_ALL_ON_CO:
                //同步企业下所有认证通过的联系人
                contactSyncBiz.syncAllOnCo(req);
                break;
            case ContactorSyncConstant.SYNC_IN_USER_ON_CO:
                //enterpriseId+传入联系人更新
                contactSyncBiz.syncInuserOnCo(req);
                break;
            case ContactorSyncConstant.SYNC_ALL_ON_USER:
                //以联系人的方式 更新相关企业联系人
                contactSyncBiz.syncAllOnUser(req);
                break;
            default:
                throw new BizException(ResultCode.FAIL.getCode(), "同步联系人类型异常");
        }
    }


}
