package com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseAddressType;
import com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.EnterpriseAddressMapperService;
import com.trinasolar.trinax.partner.domain.contract.repository.mapper.EnterpriseAddressMapper;
import com.trinasolar.trinax.partner.domain.contract.repository.po.EnterpriseAddressPO;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class EnterpriseAddressMapperServiceImpl
        extends ServiceImpl<EnterpriseAddressMapper, EnterpriseAddressPO>
        implements EnterpriseAddressMapperService {

    @Override
    public List<EnterpriseAddressPO> listByEnterpriseIdsAndAddressType(List<String> enterpriseIds, EnterpriseAddressType enterpriseAddressType) {
        if (ObjectUtil.isEmpty(enterpriseIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<EnterpriseAddressPO>()
                .eq(EnterpriseAddressPO::getAddressType, enterpriseAddressType.getCode())
                .in(EnterpriseAddressPO::getEnterpriseId, enterpriseIds).orderByDesc(EnterpriseAddressPO::getId));
    }

    @Override
    public EnterpriseAddressPO getRegistByEnterpriseId(String enterpriseId) {
        return lambdaQuery()
                .eq(EnterpriseAddressPO::getAddressType, EnterpriseAddressType.REGIST.getCode())
                .eq(EnterpriseAddressPO::getEnterpriseId, enterpriseId)
                .one();
    }
}
