package com.trinasolar.trinax.partner.domain.contract.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.EnterpriseBizRelationMapperService;
import com.trinasolar.trinax.partner.domain.contract.repository.po.EnterpriseBizRelationPO;
import com.trinasolar.trinax.partner.domain.contract.service.EnterpriseBizRelationService;
import com.trinasolar.trinax.partner.dto.output.EnterpriseBizRelationResDTO;
import com.trinasolar.trinax.user.constants.UserConstant;
import dtt.cache.redisclient.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EnterpriseBizRelationServiceImpl implements EnterpriseBizRelationService {

    @Autowired
    private EnterpriseBizRelationMapperService enterpriseBizRelationMapperService;

    @Autowired
    RedisUtil redisUtil;

    @Override
    public List<EnterpriseBizRelationResDTO> getEnterpriseBizRelationByEnterpriseId(String enterpriseId) {

        String redisKey = UserConstant.ENTERPRISE_BIZ_RELATION_CACHE_PREFIX + enterpriseId;
        String cache = redisUtil.get(redisKey);
        if (StringUtils.isNotBlank(cache)) {
            return JacksonUtil.json2List(cache, EnterpriseBizRelationResDTO.class);
        }
        List<EnterpriseBizRelationPO> enterpriseBizRelationPOList = enterpriseBizRelationMapperService
                .lambdaQuery().eq(EnterpriseBizRelationPO::getEnterpriseId, enterpriseId)
                .eq(EnterpriseBizRelationPO::getIsDeleted, 0).list();
        List<EnterpriseBizRelationResDTO> result = BeanUtil.copyToList(enterpriseBizRelationPOList, EnterpriseBizRelationResDTO.class);
        cache = JacksonUtil.bean2Json(result);
        redisUtil.set(redisKey, cache, UserConstant.USER_CACHE_TIME);
        return result;
    }

    @Override
    public List<EnterpriseBizRelationResDTO> listByBizOrganizationCode(String bizOrganizationCode) {
        List<EnterpriseBizRelationPO> enterpriseBizRelationPOS = enterpriseBizRelationMapperService.listByBizOrganizationCode(bizOrganizationCode);
        return BeanUtil.copyToList(enterpriseBizRelationPOS, EnterpriseBizRelationResDTO.class);
    }

    @Override
    public List<EnterpriseBizRelationResDTO> listByBizOrganizationCodes(List<String> bizOrganizationCodes) {
        List<EnterpriseBizRelationPO> enterpriseBizRelationPOS = enterpriseBizRelationMapperService.listByBizOrganizationCodes(bizOrganizationCodes);
        return BeanUtil.copyToList(enterpriseBizRelationPOS, EnterpriseBizRelationResDTO.class);
    }

    @Override
    public List<EnterpriseBizRelationResDTO> listByEnterpriseIds(List<String> enterpriseIds) {
        List<EnterpriseBizRelationPO> enterpriseBizRelationPOS = enterpriseBizRelationMapperService.listByEnterpriseIds(enterpriseIds);
        return BeanUtil.copyToList(enterpriseBizRelationPOS, EnterpriseBizRelationResDTO.class);
    }

}
