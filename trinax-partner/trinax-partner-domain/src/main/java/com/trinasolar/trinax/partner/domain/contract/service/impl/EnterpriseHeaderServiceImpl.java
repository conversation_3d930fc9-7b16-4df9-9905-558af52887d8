package com.trinasolar.trinax.partner.domain.contract.service.impl;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.EnterpriseHeaderMapperService;
import com.trinasolar.trinax.partner.domain.contract.service.EnterpriseHeaderService;
import com.trinasolar.trinax.partner.domain.contract.service.biz.EnterpriseHeaderQueryBiz;
import com.trinasolar.trinax.partner.domain.contract.service.biz.EnterpriseHeaderSaveOrUpdateBiz;
import com.trinasolar.trinax.partner.dto.input.*;
import com.trinasolar.trinax.partner.dto.output.EnterpriseHeaderQueryResDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EnterpriseHeaderServiceImpl implements EnterpriseHeaderService {

    @Autowired
    private EnterpriseHeaderQueryBiz enterpriseHeaderQueryBiz;

    @Autowired
    private EnterpriseHeaderSaveOrUpdateBiz enterpriseHeaderSaveOrUpdateBiz;

    @Autowired
    private EnterpriseHeaderMapperService enterpriseHeaderMapperService;

    @Override
    public Result<List<EnterpriseHeaderQueryResDTO>> authPassedEnterpriseHeaderByDealer(EnterpriseHeaderQueryReqDTO reqDTO) {
        return enterpriseHeaderQueryBiz.authPassedEnterpriseHeaderByDealer(reqDTO);
    }

    @Override
    public Result<String> saveOrUpdateEnterpriseAddress(EnterpriseHeaderSaveOrUpdateReqDTO reqDTO) {
        return enterpriseHeaderSaveOrUpdateBiz.saveOrUpdateEnterpriseAddress(reqDTO);
    }

    @Override
    public Result<String> removeById(Long id) {
        boolean flag = enterpriseHeaderMapperService.removeById(id);
        return flag?Result.ok("删除成功"):Result.fail("删除失败");
    }

    @Override
    public Result<EnterpriseHeaderQueryResDTO> getDetailByHeaderId(String headerId) {
        return enterpriseHeaderQueryBiz.getDetailByHeaderId(headerId);
    }
}
