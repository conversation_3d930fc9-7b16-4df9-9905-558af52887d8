package com.trinasolar.trinax.partner.dto.output.enterprise;

import com.trinasolar.trinax.partner.dto.input.enterprise.EnterpriseBizAuthDTO;
import com.trinasolar.trinax.partner.dto.input.enterprise.FileData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
@Schema(description = "企业业务信息返参")
public class EnterpriseBusinessPCResDTO {

    @Schema(description = "行业")
    private String industry;

    @Schema(description = "行业名称")
    private String industryName;

    @Schema(description = "客户组织性质")
    private String customerOrgNature;

    @Schema(description = "客户建商状态")
    private String customerStatus;

    @Schema(description = "客户建商状态")
    private String customerStatusStr;

    @Schema(description = "客户组织性质描述")
    private String customerOrgNatureDesc;

    @Schema(description = "ERP业务实体")
    private String erpBusinessEntity;

    @Schema(description = "ERP业务实体")
    private String erpBusinessEntityName;

    @Schema(description = "企业 id")
    private String enterpriseId;

    @Schema(description = "企业 名称")
    private String enterpriseName;

    @Schema(description = "交易规模")
    private String transactionScale;

    @Schema(description = "交易规模描述")
    private String transactionScaleDesc;

    @Schema(description = "主销售 ID")
    private String mainSalesUserId;

    @Schema(description = "准入状态：access 准入， Applying 申请中， Pending admittance 待准入")
    private String admissionStatus;

    private String admissionDesc;

    @Schema(description = "类型: BUSINESS 工商业， PARTNER 生态伙伴， CAPITAL 关联方")
    private String customerCategory;

    @Schema(description = "开票国家编码")
    private String countryCode;

    @Schema(description = "开票国家")
    private String countryName;

    @Schema(description = "开票省编码")
    private String provinceCode;

    @Schema(description = "开票省")
    private String provinceName;

    @Schema(description = "开票城市编码")
    private String cityCode;

    @Schema(description = "开票城市")
    private String cityName;

    @Schema(description = "邮政编码")
    private String postalCode;

    @Schema(description = "开票街道编码")
    private String streetCode;

    @Schema(description = "开票街道")
    private String streetName;

    @Schema(description = "业务线")
    private String businessLine;

    @Schema(description = "业务线中文")
    private String businessLineName;

    @Schema(description = "隶属集团(最顶层母公司)")
    private String subordinateGroup;

    @Schema(description = "年营业额")
    private String annualTurnover;

    @Schema(description = "业务联系人")
    private String contactName;

    @Schema(description = "业务联系人用户 id")
    private String contactUserId;

    @Schema(description = "联系电话(业务联系人)")
    private String contactMobile;

    @Schema(description = "主要经营业务及性质")
    private String mainBusiness;

    @Schema(description = "主要经营业务及性质描述")
    private String mainBusinessDesc;

    @Schema(description = "主要品牌")
    private String mainBrand;

    @Schema(description = "年销售总量(MW)")
    private String totalAnnualSales;

    @Schema(description = "德勤占比")
    private String percentage;

    @Schema(description = "开户行")
    private String bankName;

    @Schema(description = "账号")
    private String bankId;

    @Schema(description = "申请加盟时间")
    private LocalDate applyTime;

    @Schema(description = "法人联系电话")
    private String legalPersonMobile;

    @Schema(description = "法人代表名称")
    private String legalPersonName;

    @Schema(description = "企业文件信息")
    private List<FileData> fileDataList;

    private EnterpriseBizAuthDTO authDTO;

}