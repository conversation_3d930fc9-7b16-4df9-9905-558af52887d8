package com.trinasolar.trinax.partner.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


@Data
public class EnterpriseChangeOwnerReqDTO {

    @Schema(description ="主任务编号")
    private String taskNo;

    @Schema(description ="子任务编号")
    private String subTaskNo;

    @Schema(description ="场景编码")
    private String situationCode;

    @Schema(description = "更新人", hidden = true)
    private String updatedBy;

    @Schema(description = "更新人姓名", hidden = true)
    private String updatedName;

    @Schema(description = "原主销售人员ID")
    private String originUserId;

    @Schema(description = "原主销售人员名字")
    private String originUserName;

    @Schema(description = "原主销售人员手机号")
    private String originUserPhone;

    @Schema(description = "新主销售人员ID")
    private String newUserId;

    @Schema(description = "新主销售人员姓名")
    private String newUserName;

    @Schema(description = "新主销售人员手机号")
    private String newUserPhone;

    @Schema(description = "需要变更主销售的企业")
    private List<String> enterpriseIdList;

}
