package com.trinasolar.trinax.partner.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@NoArgsConstructor
@Schema(description = "查询企业对应的关联企业信息入参")
public class LinkEnterpriseUnionQueryReqDTO {

    @NotBlank(message = "当前企业ID不为空")
    @Schema(description = "当前企业ID")
    private List<String> enterpriseIds;


    @NotBlank(message = "当前企业类型不为空")
    @Schema(description = "当前企业类型：EnterpriseTypeEnum")
    private String enterpriseType;

    @Schema(description = "关联企业类型：EnterpriseTypeEnum")
    private String linkEnterpriseType;
}
