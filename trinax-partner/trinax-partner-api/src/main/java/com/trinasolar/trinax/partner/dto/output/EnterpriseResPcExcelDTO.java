package com.trinasolar.trinax.partner.dto.output;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class EnterpriseResPcExcelDTO {

    @ExcelProperty("SF客户编码")
    private String sfId;

    @ExcelProperty("客户ID")
    private String enterpriseId;

    @ExcelProperty("客户名称")
    private String name;

    @ExcelProperty("MDM ID")
    private String mdmId;

    @ExcelProperty("纳税人识别号")
    private String taxNumber;

    @ExcelProperty("经营状态")
    private String regStatus;

    @ExcelProperty("客户类别")
    private String customerCategoryStr;

    @ExcelProperty("区域")
    private String countryName;

    @ExcelProperty("子区域")
    private String provinceName;

    @ExcelProperty("地区")
    private String cityName;

    @ExcelProperty("客户状态")
    private String customerStatusStr;

    @ExcelProperty("法人代表")
    private String legalPersonName;

    @ExcelProperty("统一社会信用代码")
    private String creditCode;

    @ExcelProperty("组织机构代码")
    private String orgNumber;

    @ExcelProperty("公司类型")
    private String companyOrgType;

    @ExcelProperty("行业")
    private String industry;

    @ExcelProperty("注册地址")
    private String regLocation;

    @ExcelProperty("客户所有人")
    private String mainSalesUserName;

    @ExcelProperty("客户所有人联系方式")
    private String mainSalesUserMobile;

    @ExcelProperty("客户组织性质")
    private String customerOrgNatureStr;

    @ExcelProperty("业务线")
    private String businessLineStr;

    @ExcelProperty("客户角色")
    private String customerRoleStr;

    @ExcelProperty("币种")
    private String currency;

    @ExcelProperty("ERP业务实体")
    private String erpBusinessEntityStr;

    @ExcelProperty("ERP税率")
    private String erpTaxRate;

    @ExcelProperty("是否区域KA")
    private String isRegionKaStr;

    @ExcelProperty("开单街道")
    private String billingStreetName;

    @ExcelProperty("开单城市")
    private String billingCityName;

    @ExcelProperty("开单邮政编码")
    private String billingPostalCode;

    @ExcelProperty("客户等级")
    private String customerLevelStr;

}
