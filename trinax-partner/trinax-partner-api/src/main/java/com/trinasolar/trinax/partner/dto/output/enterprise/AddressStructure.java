package com.trinasolar.trinax.partner.dto.output.enterprise;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AddressStructure {
    @Schema(description = "code")
    private String code;
    @Schema(description = "name")
    private String name;
    @Schema(description = "是否是直辖市")
    private Boolean isDirect;
    @Schema(description = "salesforce编码")
    private String sfDcCode;

    public AddressStructure(String code, String name, Boolean isDirect, String sfDcCode) {
        this.code = code;
        this.name = name;
        this.isDirect = isDirect;
        this.sfDcCode = sfDcCode;
    }
}