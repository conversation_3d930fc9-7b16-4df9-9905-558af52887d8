package com.trinasolar.trinax.partner.dto.input.address;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Schema(description = "企业地址查询")
@Accessors(chain = true)
@RequiredArgsConstructor(staticName = "builder")
public class CoAddressQryReqDTO {

    @Schema(description = "企业地址 id")
    private String addressId;

}
