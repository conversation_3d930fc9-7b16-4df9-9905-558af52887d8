package com.trinasolar.trinax.partner.constants.enums;

public class EnterpriseInfoConstants {

    /**
     * 外部公司
     */
    public static final String EXTERNAL_COMPANY = "N";

    /**
     * 内部公司
     */
    public static final String INTENT_COMPANY = "Y";

    /**
     * 客户状态，默认 active
     */
    public static final String CREATE_BUSINESS_ACTIVE = "active";

    /**
     * 业务线 建商默认传给 sf
     */
    public static final String CREATE_BUSINESS_MODEL = "Module";

    /**
     * 币种 建商默认传给 sf
     */
    public static final String CREATE_BUSINESS_CURRENCY = "CNY";

    /**
     *  建商默认传给 sf
     */
    public static final String CREATE_BUSINESS_MAIN_SEGMENT = "Distribution";

    /**
     *  建商默认传给 sf
     */
    public static final String CREATE_BUSINESS_MARKET_SEGMENT = "C&I";

    /**
     *  建商默认传给 sf
     */
    public static final String CREATE_BUSINESS_CHANNEL_TYPE = "Distribution";

    /**
     *  建商默认传给 sf
     */
    public static final String CREATE_BUSINESS_CUSTOMER_ROLE = "Distributor";

    // 授权审批等待
    public static final String ENTERPRISE_AUTH_PENDING = "Pending";
    // 授权审批通过
    public static final String ENTERPRISE_AUTH_CONFIRM = "Access";
    // 授权审批拒绝
    public static final String ENTERPRISE_AUTH_REFUSE = "Reject";
}
