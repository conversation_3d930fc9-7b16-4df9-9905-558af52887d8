package com.trinasolar.trinax.partner.api;


import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.partner.constants.ServiceIds;
import com.trinasolar.trinax.partner.dto.input.ErpOrderTypeQueryReqDTO;
import com.trinasolar.trinax.partner.dto.output.ErpOrderTypeResDTO;
import com.trinasolar.trinax.partner.dto.output.MarketingAccountRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = ServiceIds.PARTNER_SERVICE)
public interface MarketingAccountFeign {

    @Operation(summary = "通过账套ID查询账套信息")
    @PostMapping({"/getMarketingAccountRespDTO"})
    Result<MarketingAccountRespDTO> getMarketingAccountRespDTO(@RequestParam String marketingAccountId);

    @Operation(summary = "通过账套ID列表批量查询账套信息")
    @PostMapping({"/listMarketingAccountRespDTO"})
    Result<List<MarketingAccountRespDTO>> listMarketingAccountRespDTO(@RequestBody List<String> marketingAccountIdList);


    @Operation(summary = "根据字典组查询字典值")
    @PostMapping("/dict/getErpOrderType")
    Result<List<ErpOrderTypeResDTO>> getErpOrderType(@RequestBody ErpOrderTypeQueryReqDTO queryReq);

    @Operation(summary = "获取账套列表")
    @GetMapping("/listMarketAccount")
    Result<List<MarketingAccountRespDTO>> listMarketAccount();

}
