package com.trinasolar.trinax.partner.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class EnterpriseInfoDTO {

    private String enterpriseId;

    @Schema(description ="企业名称")
    private String name;

    @Schema(description ="是否为默认企业 1：是 0：否")
    private Integer isDefault;

    @Schema(description ="所在地区")
    private String detailRegion;

    @Schema(description ="企业头像 暂无此数据")
    private String avatar;

}
