package com.trinasolar.trinax.partner.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@RequiredArgsConstructor(staticName = "builder")
public class ErpOrderTypeQueryReqDTO {

    @Schema(description = "内部公司：Y/N")
    private String intercompanyOrderType;

    @Schema(description = "erpOrderTypeId 列表")
    private List<String> typeIdList;
}
